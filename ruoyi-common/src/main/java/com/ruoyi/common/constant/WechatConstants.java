package com.ruoyi.common.constant;

/**
 * 微信小程序常量信息
 * 
 * <AUTHOR>
 */
public class WechatConstants
{
    /**
     * 微信小程序获取access_token的URL
     */
    public static final String WECHAT_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    /**
     * 微信小程序获取用户信息的URL
     */
    public static final String WECHAT_USER_INFO_URL = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    /**
     * 微信小程序发送订阅消息的URL
     */
    public static final String WECHAT_SEND_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s";

    /**
     * access_token缓存键前缀
     */
    public static final String ACCESS_TOKEN_CACHE_KEY = "wechat:access_token:";

    /**
     * 微信API成功返回码
     */
    public static final int WECHAT_SUCCESS_CODE = 0;

    /**
     * access_token过期错误码
     */
    public static final int ACCESS_TOKEN_EXPIRED_CODE = 40001;

    /**
     * access_token无效错误码
     */
    public static final int ACCESS_TOKEN_INVALID_CODE = 42001;

    /**
     * 微信支付成功状态
     */
    public static final String WECHAT_PAY_SUCCESS = "SUCCESS";

    /**
     * 微信支付失败状态
     */
    public static final String WECHAT_PAY_FAIL = "FAIL";

    // ==================== 微信支付V3版本API地址 ====================

    /**
     * 微信支付V3版本基础URL
     */
    public static final String WECHAT_PAY_V3_BASE_URL = "https://api.mch.weixin.qq.com";

    /**
     * 微信支付V3版本小程序下单URL
     */
    public static final String WECHAT_PAY_V3_JSAPI_URL = "/v3/pay/transactions/jsapi";

    /**
     * 微信支付V3版本查询订单URL（按商户订单号）
     */
    public static final String WECHAT_PAY_V3_QUERY_BY_OUT_TRADE_NO_URL = "/v3/pay/transactions/out-trade-no/%s";

    /**
     * 微信支付V3版本查询订单URL（按微信支付订单号）
     */
    public static final String WECHAT_PAY_V3_QUERY_BY_TRANSACTION_ID_URL = "/v3/pay/transactions/id/%s";

    /**
     * 微信支付V3版本关闭订单URL
     */
    public static final String WECHAT_PAY_V3_CLOSE_ORDER_URL = "/v3/pay/transactions/out-trade-no/%s/close";

    /**
     * 微信支付V3版本申请退款URL
     */
    public static final String WECHAT_PAY_V3_REFUND_URL = "/v3/refund/domestic/refunds";

    /**
     * 微信支付V3版本查询退款URL
     */
    public static final String WECHAT_PAY_V3_REFUND_QUERY_URL = "/v3/refund/domestic/refunds/%s";

    /**
     * 微信支付V3版本签名算法
     */
    public static final String WECHAT_PAY_V3_SIGN_TYPE = "WECHATPAY2-SHA256-RSA2048";

    /**
     * 微信支付V3版本User-Agent
     */
    public static final String WECHAT_PAY_V3_USER_AGENT = "WechatPay-Java-SDK";

    /**
     * 微信支付V3版本Accept
     */
    public static final String WECHAT_PAY_V3_ACCEPT = "application/json";

    /**
     * 微信支付V3版本Content-Type
     */
    public static final String WECHAT_PAY_V3_CONTENT_TYPE = "application/json; charset=utf-8";
}
