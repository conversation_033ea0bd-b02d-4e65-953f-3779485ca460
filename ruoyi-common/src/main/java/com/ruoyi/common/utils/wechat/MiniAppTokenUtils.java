package com.ruoyi.common.utils.wechat;

import com.ruoyi.common.utils.StringUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 小程序Token工具类
 * 用于生成、验证和刷新小程序用户的JWT token
 *
 * <AUTHOR>
 */
@Component
public class MiniAppTokenUtils {
    private static final Logger log = LoggerFactory.getLogger(MiniAppTokenUtils.class);

    /** token密钥 */
    private static String secret = "miniapp_secret_key_2025";

    /** token有效期（默认2小时） */
    private static int expireTime = 7200;

    /** token刷新阈值（默认30分钟，即剩余30分钟时可以刷新） */
    private static int refreshThreshold = 1800;

    /** token缓存键前缀 */
    private static final String TOKEN_CACHE_KEY = "miniapp:token:";

    /**
     * 生成token
     *
     * @param openid 用户openid
     * @param userId 用户ID
     * @return token字符串
     */
    public static String generateToken(String openid, Long userId) {
        if (StringUtils.isEmpty(openid) || userId == null) {
            log.error("生成token失败：openid或userId不能为空");
            return null;
        }

        try {
            Date now = new Date();
            Date expireDate = new Date(now.getTime() + expireTime * 1000);

            Map<String, Object> claims = new HashMap<>();
            claims.put("openid", openid);
            claims.put("userId", userId);
            claims.put("type", "miniapp");

            String token = Jwts.builder()
                    .setClaims(claims)
                    .setIssuedAt(now)
                    .setExpiration(expireDate)
                    .signWith(SignatureAlgorithm.HS512, secret)
                    .compact();

            // 缓存token
            String cacheKey = TOKEN_CACHE_KEY + openid;
            WechatCacheWrapper.put(cacheKey, token, expireTime);

            log.info("生成token成功，openid：{}，userId：{}", openid, userId);
            return token;
        } catch (Exception e) {
            log.error("生成token异常", e);
            return null;
        }
    }

    /**
     * 验证token
     *
     * @param token 待验证的token
     * @return 验证结果，包含用户信息
     */
    public static TokenValidationResult validateToken(String token) {
        if (StringUtils.isEmpty(token)) {
            return new TokenValidationResult(false, "token不能为空", null, null);
        }

        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();

            String openid = claims.get("openid", String.class);
            Long userId = claims.get("userId", Long.class);
            String type = claims.get("type", String.class);

            if (StringUtils.isEmpty(openid) || userId == null || !"miniapp".equals(type)) {
                return new TokenValidationResult(false, "token格式不正确", null, null);
            }

            // 检查token是否在缓存中
            String cacheKey = TOKEN_CACHE_KEY + openid;
            String cachedToken = WechatCacheWrapper.get(cacheKey);
            if (!token.equals(cachedToken)) {
                return new TokenValidationResult(false, "token已失效", null, null);
            }

            // 检查是否需要刷新token
            Date expiration = claims.getExpiration();
            Date now = new Date();
            long remainingTime = (expiration.getTime() - now.getTime()) / 1000;

            boolean needRefresh = remainingTime <= refreshThreshold;

            log.debug("验证token成功，openid：{}，userId：{}，剩余时间：{}秒", openid, userId, remainingTime);
            return new TokenValidationResult(true, "验证成功", openid, userId, needRefresh);

        } catch (Exception e) {
            log.error("验证token异常：{}", e.getMessage());
            return new TokenValidationResult(false, "token验证失败：" + e.getMessage(), null, null);
        }
    }

    /**
     * 刷新token
     *
     * @param oldToken 旧token
     * @return 新token
     */
    public static String refreshToken(String oldToken) {
        TokenValidationResult validationResult = validateToken(oldToken);
        if (!validationResult.isValid()) {
            log.error("刷新token失败：旧token无效");
            return null;
        }

        String openid = validationResult.getOpenid();
        Long userId = validationResult.getUserId();

        // 生成新token
        String newToken = generateToken(openid, userId);
        if (StringUtils.isNotEmpty(newToken)) {
            log.info("刷新token成功，openid：{}", openid);
        }

        return newToken;
    }

    /**
     * 删除token
     *
     * @param openid 用户openid
     */
    public static void removeToken(String openid) {
        if (StringUtils.isNotEmpty(openid)) {
            String cacheKey = TOKEN_CACHE_KEY + openid;
            WechatCacheWrapper.remove(cacheKey);
            log.info("删除token成功，openid：{}", openid);
        }
    }

    /**
     * 检查token是否存在
     *
     * @param openid 用户openid
     * @return 是否存在有效token
     */
    public static boolean hasValidToken(String openid) {
        if (StringUtils.isEmpty(openid)) {
            return false;
        }

        String cacheKey = TOKEN_CACHE_KEY + openid;
        String token = WechatCacheWrapper.get(cacheKey);
        if (StringUtils.isEmpty(token)) {
            return false;
        }

        TokenValidationResult result = validateToken(token);
        return result.isValid();
    }

    /**
     * Token验证结果类
     */
    public static class TokenValidationResult {
        private boolean valid;
        private String message;
        private String openid;
        private Long userId;
        private boolean needRefresh;

        public TokenValidationResult(boolean valid, String message, String openid, Long userId) {
            this(valid, message, openid, userId, false);
        }

        public TokenValidationResult(boolean valid, String message, String openid, Long userId, boolean needRefresh) {
            this.valid = valid;
            this.message = message;
            this.openid = openid;
            this.userId = userId;
            this.needRefresh = needRefresh;
        }

        // Getters
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public String getOpenid() { return openid; }
        public Long getUserId() { return userId; }
        public boolean isNeedRefresh() { return needRefresh; }
    }

    // Setters for configuration
    public static void setSecret(String secret) {
        MiniAppTokenUtils.secret = secret;
    }

    public static void setExpireTime(int expireTime) {
        MiniAppTokenUtils.expireTime = expireTime;
    }

    public static void setRefreshThreshold(int refreshThreshold) {
        MiniAppTokenUtils.refreshThreshold = refreshThreshold;
    }
}
