package com.ruoyi.common.utils.wechat;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 微信缓存包装类，支持过期时间
 *
 * <AUTHOR>
 */
public class WechatCacheWrapper {
    private static final Logger log = LoggerFactory.getLogger(WechatCacheWrapper.class);

    private static final ConcurrentMap<String, CacheItem> cache = new ConcurrentHashMap<>();

    /**
     * 缓存项
     */
    private static class CacheItem {
        private final Object value;
        private final long expireTime;

        public CacheItem(Object value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }

        public Object getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }

    /**
     * 存储缓存
     *
     * @param key           缓存键
     * @param value         缓存值
     * @param expireSeconds 过期时间（秒）
     */
    public static void put(String key, Object value, int expireSeconds) {
        long expireTime = System.currentTimeMillis() + expireSeconds * 1000L;
        cache.put(key, new CacheItem(value, expireTime));
        log.debug("缓存存储成功，key: {}, 过期时间: {}秒", key, expireSeconds);
    }

    /**
     * 获取缓存
     *
     * @param key 缓存键
     * @return 缓存值，如果不存在或已过期返回null
     */
    public static String get(String key) {
        CacheItem item = cache.get(key);
        if (item == null) {
            return null;
        }

        if (item.isExpired()) {
            cache.remove(key);
            log.debug("缓存已过期并移除，key: {}", key);
            return null;
        }

        return (String) item.getValue();
    }

    /**
     * 移除缓存
     *
     * @param key 缓存键
     */
    public static void remove(String key) {
        cache.remove(key);
        log.debug("缓存移除成功，key: {}", key);
    }

    /**
     * 清理所有过期缓存
     */
    public static void cleanExpiredCache() {
        int cleanCount = 0;
        for (String key : cache.keySet()) {
            CacheItem item = cache.get(key);
            if (item != null && item.isExpired()) {
                cache.remove(key);
                cleanCount++;
            }
        }
        if (cleanCount > 0) {
            log.info("清理过期缓存完成，清理数量: {}", cleanCount);
        }
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public static int size() {
        return cache.size();
    }

    /**
     * 清空所有缓存
     */
    public static void clear() {
        cache.clear();
        log.info("清空所有缓存完成");
    }
}
