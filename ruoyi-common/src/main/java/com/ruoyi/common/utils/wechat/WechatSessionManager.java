package com.ruoyi.common.utils.wechat;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.utils.StringUtils;

/**
 * 微信SessionKey管理器
 * 用于服务端安全存储和管理用户的sessionKey
 * 注意：sessionKey不应该下发到客户端，只在服务端使用
 *
 * <AUTHOR>
 */
public class WechatSessionManager {
    private static final Logger log = LoggerFactory.getLogger(WechatSessionManager.class);

    /**
     * sessionKey缓存，key为openid，value为SessionInfo
     */
    private static final ConcurrentMap<String, SessionInfo> sessionCache = new ConcurrentHashMap<>();

    /**
     * sessionKey默认过期时间（秒），微信sessionKey有效期为3天
     */
    private static final long DEFAULT_EXPIRE_TIME = 3 * 24 * 60 * 60 * 1000L; // 3天

    /**
     * SessionKey信息封装
     */
    private static class SessionInfo {
        private final String sessionKey;
        private final long expireTime;

        public SessionInfo(String sessionKey, long expireTime) {
            this.sessionKey = sessionKey;
            this.expireTime = expireTime;
        }

        public String getSessionKey() {
            return sessionKey;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }

    /**
     * 存储用户的sessionKey
     *
     * @param openid     用户openid
     * @param sessionKey 微信sessionKey
     */
    public static void storeSessionKey(String openid, String sessionKey) {
        if (StringUtils.isEmpty(openid) || StringUtils.isEmpty(sessionKey)) {
            log.warn("存储sessionKey失败：openid或sessionKey为空");
            return;
        }

        long expireTime = System.currentTimeMillis() + DEFAULT_EXPIRE_TIME;
        sessionCache.put(openid, new SessionInfo(sessionKey, expireTime));
        log.debug("存储sessionKey成功，openid: {}", openid);
    }

    /**
     * 获取用户的sessionKey
     *
     * @param openid 用户openid
     * @return sessionKey，如果不存在或已过期返回null
     */
    public static String getSessionKey(String openid) {
        if (StringUtils.isEmpty(openid)) {
            return null;
        }

        SessionInfo sessionInfo = sessionCache.get(openid);
        if (sessionInfo == null) {
            return null;
        }

        if (sessionInfo.isExpired()) {
            sessionCache.remove(openid);
            log.debug("sessionKey已过期并移除，openid: {}", openid);
            return null;
        }

        return sessionInfo.getSessionKey();
    }

    /**
     * 移除用户的sessionKey
     *
     * @param openid 用户openid
     */
    public static void removeSessionKey(String openid) {
        if (StringUtils.isNotEmpty(openid)) {
            sessionCache.remove(openid);
            log.debug("移除sessionKey成功，openid: {}", openid);
        }
    }

    /**
     * 清理所有过期的sessionKey
     */
    public static void cleanExpiredSessions() {
        int cleanCount = 0;
        for (String openid : sessionCache.keySet()) {
            SessionInfo sessionInfo = sessionCache.get(openid);
            if (sessionInfo != null && sessionInfo.isExpired()) {
                sessionCache.remove(openid);
                cleanCount++;
            }
        }
        if (cleanCount > 0) {
            log.info("清理过期sessionKey完成，清理数量: {}", cleanCount);
        }
    }

    /**
     * 获取当前缓存的sessionKey数量
     *
     * @return 缓存数量
     */
    public static int getCacheSize() {
        return sessionCache.size();
    }

    /**
     * 清空所有sessionKey缓存
     */
    public static void clearAll() {
        sessionCache.clear();
        log.info("清空所有sessionKey缓存完成");
    }

    /**
     * 检查用户是否有有效的sessionKey
     *
     * @param openid 用户openid
     * @return 是否有有效的sessionKey
     */
    public static boolean hasValidSessionKey(String openid) {
        return StringUtils.isNotEmpty(getSessionKey(openid));
    }
}
