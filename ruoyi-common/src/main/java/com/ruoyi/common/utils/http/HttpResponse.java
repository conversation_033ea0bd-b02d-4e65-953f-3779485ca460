package com.ruoyi.common.utils.http;

/**
 * HTTP响应结果封装类
 * 
 * <AUTHOR>
 */
public class HttpResponse
{
    /** 响应状态码 */
    private int statusCode;

    /** 响应体 */
    private String body;

    /** 是否成功 */
    private boolean success;

    /** 错误信息 */
    private String errorMessage;

    public HttpResponse()
    {
    }

    public HttpResponse(int statusCode, String body)
    {
        this.statusCode = statusCode;
        this.body = body;
        this.success = (statusCode >= 200 && statusCode < 300);
    }

    public HttpResponse(int statusCode, String body, String errorMessage)
    {
        this.statusCode = statusCode;
        this.body = body;
        this.success = (statusCode >= 200 && statusCode < 300);
        this.errorMessage = errorMessage;
    }

    /**
     * 创建成功响应
     */
    public static HttpResponse success(int statusCode, String body)
    {
        return new HttpResponse(statusCode, body);
    }

    /**
     * 创建失败响应
     */
    public static HttpResponse error(int statusCode, String errorMessage)
    {
        return new HttpResponse(statusCode, null, errorMessage);
    }

    /**
     * 创建异常响应
     */
    public static HttpResponse exception(String errorMessage)
    {
        return new HttpResponse(-1, null, errorMessage);
    }

    public int getStatusCode()
    {
        return statusCode;
    }

    public void setStatusCode(int statusCode)
    {
        this.statusCode = statusCode;
        this.success = (statusCode >= 200 && statusCode < 300);
    }

    public String getBody()
    {
        return body;
    }

    public void setBody(String body)
    {
        this.body = body;
    }

    public boolean isSuccess()
    {
        return success;
    }

    public void setSuccess(boolean success)
    {
        this.success = success;
    }

    public String getErrorMessage()
    {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage)
    {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString()
    {
        return "HttpResponse{" +
                "statusCode=" + statusCode +
                ", body='" + body + '\'' +
                ", success=" + success +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
