package com.ruoyi.common.utils.wechat;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.config.WechatMiniAppConfig;
import com.ruoyi.common.constant.WechatConstants;
import com.ruoyi.common.core.domain.WechatApiResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 微信小程序工具类
 *
 * <AUTHOR>
 */
@Component
public class WechatMiniAppUtils {
    private static final Logger log = LoggerFactory.getLogger(WechatMiniAppUtils.class);

    @Autowired
    private WechatMiniAppConfig wechatConfig;

    /**
     * 获取小程序全局唯一后台接口调用凭据（access_token）
     *
     * @return access_token
     */
    public String getAccessToken() {
        return getAccessToken(false);
    }

    /**
     * 获取小程序全局唯一后台接口调用凭据（access_token）
     *
     * @param forceRefresh 是否强制刷新
     * @return access_token
     */
    public String getAccessToken(boolean forceRefresh) {
        String cacheKey = WechatConstants.ACCESS_TOKEN_CACHE_KEY + wechatConfig.getAppId();

        // 如果不强制刷新，先从缓存获取
        if (!forceRefresh) {
            String cachedToken = WechatCacheWrapper.get(cacheKey);
            if (StringUtils.isNotEmpty(cachedToken)) {
                log.debug("从缓存获取access_token: {}", cachedToken);
                return cachedToken;
            }
        }

        // 从微信服务器获取access_token
        String accessToken = fetchAccessTokenFromWechat();

        if (StringUtils.isNotEmpty(accessToken)) {
            // 缓存access_token，设置过期时间为配置时间减去5分钟（提前刷新）
            int cacheTime = Math.max(wechatConfig.getTokenCacheTime() - 300, 300);
            WechatCacheWrapper.put(cacheKey, accessToken, cacheTime);
            log.info("获取并缓存access_token成功，缓存时间: {}秒", cacheTime);
        }

        return accessToken;
    }

    /**
     * 从微信服务器获取access_token
     *
     * @return access_token
     */
    private String fetchAccessTokenFromWechat() {
        try {
            String appId = wechatConfig.getAppId();
            String appSecret = wechatConfig.getAppSecret();

            if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
                log.error("微信小程序配置不完整，appId: {}, appSecret: {}", appId, StringUtils.isEmpty(appSecret) ? "未配置" : "已配置");
                return null;
            }

            String url = String.format(WechatConstants.WECHAT_ACCESS_TOKEN_URL, appId, appSecret);
            log.debug("请求微信access_token，URL: {}", url);

            String response = HttpUtils.sendGet(url);
            log.debug("微信access_token响应: {}", response);

            if (StringUtils.isEmpty(response)) {
                log.error("获取微信access_token失败，响应为空");
                return null;
            }

            WechatApiResult result = JSON.parseObject(response, WechatApiResult.class);

            if (result == null) {
                log.error("解析微信access_token响应失败: {}", response);
                return null;
            }

            if (!result.isSuccess()) {
                log.error("获取微信access_token失败，错误码: {}, 错误信息: {}", result.getErrCode(), result.getErrMsg());
                return null;
            }

            String accessToken = result.getAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                log.error("微信返回的access_token为空: {}", response);
                return null;
            }

            log.info("成功获取微信access_token，有效期: {}秒", result.getExpiresIn());
            return accessToken;
        } catch (Exception e) {
            log.error("获取微信access_token异常", e);
            return null;
        }
    }

    /**
     * 通过code换取网页授权access_token和openid
     *
     * @param jsCode 小程序登录时获取的code
     * @return 微信API响应结果
     */
    public WechatApiResult code2Session(String jsCode) {
        try {
            String appId = wechatConfig.getAppId();
            String appSecret = wechatConfig.getAppSecret();

            if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
                log.error("微信小程序配置不完整");
                return new WechatApiResult(-1, "微信小程序配置不完整");
            }

            if (StringUtils.isEmpty(jsCode)) {
                log.error("jsCode不能为空");
                return new WechatApiResult(-1, "jsCode不能为空");
            }

            String url = String.format(WechatConstants.WECHAT_USER_INFO_URL, appId, appSecret, jsCode);
            log.debug("请求微信code2Session，URL: {}", url);

            String response = HttpUtils.sendGet(url);
            log.debug("微信code2Session响应: {}", response);

            if (StringUtils.isEmpty(response)) {
                log.error("微信code2Session响应为空");
                return new WechatApiResult(-1, "微信服务器无响应");
            }

            WechatApiResult result = JSON.parseObject(response, WechatApiResult.class);

            if (result == null) {
                log.error("解析微信code2Session响应失败: {}", response);
                return new WechatApiResult(-1, "解析响应失败");
            }

            if (!result.isSuccess()) {
                log.error("微信code2Session失败，错误码: {}, 错误信息: {}", result.getErrCode(), result.getErrMsg());
            } else {
                log.info("微信code2Session成功，openId: {}", result.getOpenId());
            }

            return result;
        } catch (Exception e) {
            log.error("微信code2Session异常", e);
            return new WechatApiResult(-1, "系统异常：" + e.getMessage());
        }
    }

    /**
     * 清除access_token缓存
     */
    public void clearAccessTokenCache() {
        String cacheKey = WechatConstants.ACCESS_TOKEN_CACHE_KEY + wechatConfig.getAppId();
        WechatCacheWrapper.remove(cacheKey);
        log.info("已清除access_token缓存");
    }

    /**
     * 验证access_token是否有效
     *
     * @param accessToken 待验证的access_token
     * @return 是否有效
     */
    public boolean isAccessTokenValid(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return false;
        }

        try {
            // 可以通过调用微信API来验证token是否有效
            // 这里简单返回true，实际项目中可以调用微信的接口验证
            return true;
        } catch (Exception e) {
            log.error("验证access_token异常", e);
            return false;
        }
    }
}
