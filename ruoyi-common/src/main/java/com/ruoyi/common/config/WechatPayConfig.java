package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信支付V3版本配置属性
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "wechat.pay")
public class WechatPayConfig
{
    /** 商户号 */
    private String mchId;

    /** APIv3密钥 */
    private String apiV3Key;

    /** 商户私钥文件路径 */
    private String privateKeyPath;

    /** 商户证书序列号 */
    private String merchantSerialNumber;

    /** 支付回调通知URL */
    private String notifyUrl = "/api/wechat/pay/notify";

    /** 支付超时时间（分钟），默认30分钟 */
    private int timeoutMinutes = 30;

    /** 微信平台公钥字符串（PEM格式） */
    private String wechatPlatformPublicKey;

    /** 微信平台公钥文件路径 */
    private String wechatPlatformPublicKeyPath;

    public String getMchId()
    {
        return mchId;
    }

    public void setMchId(String mchId)
    {
        this.mchId = mchId;
    }



    public String getNotifyUrl()
    {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl)
    {
        this.notifyUrl = notifyUrl;
    }

    public int getTimeoutMinutes()
    {
        return timeoutMinutes;
    }

    public void setTimeoutMinutes(int timeoutMinutes)
    {
        this.timeoutMinutes = timeoutMinutes;
    }

    public String getApiV3Key()
    {
        return apiV3Key;
    }

    public void setApiV3Key(String apiV3Key)
    {
        this.apiV3Key = apiV3Key;
    }

    public String getPrivateKeyPath()
    {
        return privateKeyPath;
    }

    public void setPrivateKeyPath(String privateKeyPath)
    {
        this.privateKeyPath = privateKeyPath;
    }

    public String getMerchantSerialNumber()
    {
        return merchantSerialNumber;
    }

    public void setMerchantSerialNumber(String merchantSerialNumber)
    {
        this.merchantSerialNumber = merchantSerialNumber;
    }

    public String getWechatPlatformPublicKey()
    {
        return wechatPlatformPublicKey;
    }

    public void setWechatPlatformPublicKey(String wechatPlatformPublicKey)
    {
        this.wechatPlatformPublicKey = wechatPlatformPublicKey;
    }

    public String getWechatPlatformPublicKeyPath()
    {
        return wechatPlatformPublicKeyPath;
    }

    public void setWechatPlatformPublicKeyPath(String wechatPlatformPublicKeyPath)
    {
        this.wechatPlatformPublicKeyPath = wechatPlatformPublicKeyPath;
    }

}
