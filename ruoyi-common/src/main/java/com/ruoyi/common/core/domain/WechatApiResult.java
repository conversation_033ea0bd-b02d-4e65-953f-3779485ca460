package com.ruoyi.common.core.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 微信API响应结果封装
 * 
 * <AUTHOR>
 */
public class WechatApiResult
{
    /** 错误码 */
    @JsonProperty("errcode")
    private Integer errCode;

    /** 错误信息 */
    @JsonProperty("errmsg")
    private String errMsg;

    /** access_token */
    @JsonProperty("access_token")
    private String accessToken;

    /** 有效期，单位：秒 */
    @JsonProperty("expires_in")
    private Integer expiresIn;

    /** 用户唯一标识 */
    @JsonProperty("openid")
    private String openId;

    /** 会话密钥 */
    @JsonProperty("session_key")
    private String sessionKey;

    /** 用户在开放平台的唯一标识符 */
    @JsonProperty("unionid")
    private String unionId;

    public WechatApiResult()
    {
    }

    public WechatApiResult(Integer errCode, String errMsg)
    {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess()
    {
        return errCode == null || errCode == 0;
    }

    public Integer getErrCode()
    {
        return errCode;
    }

    public void setErrCode(Integer errCode)
    {
        this.errCode = errCode;
    }

    public String getErrMsg()
    {
        return errMsg;
    }

    public void setErrMsg(String errMsg)
    {
        this.errMsg = errMsg;
    }

    public String getAccessToken()
    {
        return accessToken;
    }

    public void setAccessToken(String accessToken)
    {
        this.accessToken = accessToken;
    }

    public Integer getExpiresIn()
    {
        return expiresIn;
    }

    public void setExpiresIn(Integer expiresIn)
    {
        this.expiresIn = expiresIn;
    }

    public String getOpenId()
    {
        return openId;
    }

    public void setOpenId(String openId)
    {
        this.openId = openId;
    }

    public String getSessionKey()
    {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey)
    {
        this.sessionKey = sessionKey;
    }

    public String getUnionId()
    {
        return unionId;
    }

    public void setUnionId(String unionId)
    {
        this.unionId = unionId;
    }

    @Override
    public String toString()
    {
        return "WechatApiResult{" +
                "errCode=" + errCode +
                ", errMsg='" + errMsg + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", expiresIn=" + expiresIn +
                ", openId='" + openId + '\'' +
                ", sessionKey='" + sessionKey + '\'' +
                ", unionId='" + unionId + '\'' +
                '}';
    }
}
