package com.ruoyi.common.core.domain.request.wechat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 微信支付下单请求
 * 
 * <AUTHOR>
 */
public class WechatPayRequest
{
    /** 订单号 */
    @NotBlank(message = "订单号不能为空")
    private String outTradeNo;

    /** 商品描述 */
    @NotBlank(message = "商品描述不能为空")
    private String body;

    /** 订单总金额，单位为分 */
    @NotNull(message = "订单金额不能为空")
    @Positive(message = "订单金额必须大于0")
    private Integer totalFee;

    private String openid;

    /** 商品详情（可选） */
    private String detail;

    /** 附加数据（可选） */
    private String attach;

    /** 货币类型，默认CNY */
    private String feeType = "CNY";

    /** 终端IP */
    private String spbillCreateIp;

    /** 交易起始时间 */
    private String timeStart;

    /** 交易结束时间 */
    private String timeExpire;

    /** 商品标记（可选） */
    private String goodsTag;

    public String getOutTradeNo()
    {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo)
    {
        this.outTradeNo = outTradeNo;
    }

    public String getBody()
    {
        return body;
    }

    public void setBody(String body)
    {
        this.body = body;
    }

    public Integer getTotalFee()
    {
        return totalFee;
    }

    public void setTotalFee(Integer totalFee)
    {
        this.totalFee = totalFee;
    }

    public String getOpenid()
    {
        return openid;
    }

    public void setOpenid(String openid)
    {
        this.openid = openid;
    }

    public String getDetail()
    {
        return detail;
    }

    public void setDetail(String detail)
    {
        this.detail = detail;
    }

    public String getAttach()
    {
        return attach;
    }

    public void setAttach(String attach)
    {
        this.attach = attach;
    }

    public String getFeeType()
    {
        return feeType;
    }

    public void setFeeType(String feeType)
    {
        this.feeType = feeType;
    }

    public String getSpbillCreateIp()
    {
        return spbillCreateIp;
    }

    public void setSpbillCreateIp(String spbillCreateIp)
    {
        this.spbillCreateIp = spbillCreateIp;
    }

    public String getTimeStart()
    {
        return timeStart;
    }

    public void setTimeStart(String timeStart)
    {
        this.timeStart = timeStart;
    }

    public String getTimeExpire()
    {
        return timeExpire;
    }

    public void setTimeExpire(String timeExpire)
    {
        this.timeExpire = timeExpire;
    }

    public String getGoodsTag()
    {
        return goodsTag;
    }

    public void setGoodsTag(String goodsTag)
    {
        this.goodsTag = goodsTag;
    }
}
