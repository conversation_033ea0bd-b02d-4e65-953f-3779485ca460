package com.ruoyi.common.core.domain;


import com.ruoyi.common.core.domain.entity.User;

/**
 * 小程序登录结果封装
 *
 * <AUTHOR>
 */
public class MiniAppLoginResult {
    /**
     * 是否为新用户
     */
    private boolean isNewUser;

    /**
     * 用户信息
     */
    private User user;

    /**
     * 登录token（如果需要的话）
     */
    private String token;

    public MiniAppLoginResult() {
    }

    public MiniAppLoginResult(boolean isNewUser, User user) {
        this.isNewUser = isNewUser;
        this.user = user;
    }

    public MiniAppLoginResult(boolean isNewUser, User user, String token) {
        this.isNewUser = isNewUser;
        this.user = user;
        this.token = token;
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public void setNewUser(boolean newUser) {
        isNewUser = newUser;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }



    @Override
    public String toString() {
        return "MiniAppLoginResult{" +
                "isNewUser=" + isNewUser +
                ", user=" + user +
                ", token='" + token + '\'' +
                '}';
    }
}
