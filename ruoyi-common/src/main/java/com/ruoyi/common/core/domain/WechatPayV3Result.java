package com.ruoyi.common.core.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 微信支付V3版本响应结果
 * 
 * <AUTHOR>
 */
public class WechatPayV3Result
{
    /** 预支付交易会话标识 */
    @JsonProperty("prepay_id")
    private String prepayId;

    /** 小程序ID */
    private String appid;

    /** 商户号 */
    @JsonProperty("mchid")
    private String mchId;

    /** 商户订单号 */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /** 微信支付订单号 */
    @JsonProperty("transaction_id")
    private String transactionId;

    /** 交易类型 */
    @JsonProperty("trade_type")
    private String tradeType;

    /** 交易状态 */
    @JsonProperty("trade_state")
    private String tradeState;

    /** 交易状态描述 */
    @JsonProperty("trade_state_desc")
    private String tradeStateDesc;

    /** 付款银行 */
    @JsonProperty("bank_type")
    private String bankType;

    /** 附加数据 */
    private String attach;

    /** 支付完成时间 */
    @JsonProperty("success_time")
    private String successTime;

    /** 订单金额信息 */
    private Amount amount;

    /** 支付者信息 */
    private Payer payer;

    /** 场景信息 */
    @JsonProperty("scene_info")
    private SceneInfo sceneInfo;

    /** 优惠功能 */
    @JsonProperty("promotion_detail")
    private java.util.List<PromotionDetail> promotionDetail;

    /**
     * 订单金额信息
     */
    public static class Amount {
        /** 订单总金额，单位为分 */
        private Integer total;

        /** 用户支付金额，单位为分 */
        @JsonProperty("payer_total")
        private Integer payerTotal;

        /** 货币类型 */
        private String currency;

        /** 用户支付币种 */
        @JsonProperty("payer_currency")
        private String payerCurrency;

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        public Integer getPayerTotal() {
            return payerTotal;
        }

        public void setPayerTotal(Integer payerTotal) {
            this.payerTotal = payerTotal;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getPayerCurrency() {
            return payerCurrency;
        }

        public void setPayerCurrency(String payerCurrency) {
            this.payerCurrency = payerCurrency;
        }
    }

    /**
     * 支付者信息
     */
    public static class Payer {
        /** 用户标识 */
        private String openid;

        public String getOpenid() {
            return openid;
        }

        public void setOpenid(String openid) {
            this.openid = openid;
        }
    }

    /**
     * 场景信息
     */
    public static class SceneInfo {
        /** 商户端设备号 */
        @JsonProperty("device_id")
        private String deviceId;

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }
    }

    /**
     * 优惠功能
     */
    public static class PromotionDetail {
        /** 券ID */
        @JsonProperty("coupon_id")
        private String couponId;

        /** 优惠名称 */
        private String name;

        /** 优惠范围 */
        private String scope;

        /** 优惠类型 */
        private String type;

        /** 优惠券面额 */
        private Integer amount;

        /** 活动ID */
        @JsonProperty("stock_id")
        private String stockId;

        /** 微信出资 */
        @JsonProperty("wechatpay_contribute")
        private Integer wechatpayContribute;

        /** 商户出资 */
        @JsonProperty("merchant_contribute")
        private Integer merchantContribute;

        /** 其他出资 */
        @JsonProperty("other_contribute")
        private Integer otherContribute;

        /** 优惠币种 */
        private String currency;

        /** 单品列表 */
        @JsonProperty("goods_detail")
        private java.util.List<GoodsDetail> goodsDetail;

        // Getters and Setters
        public String getCouponId() {
            return couponId;
        }

        public void setCouponId(String couponId) {
            this.couponId = couponId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getScope() {
            return scope;
        }

        public void setScope(String scope) {
            this.scope = scope;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }

        public String getStockId() {
            return stockId;
        }

        public void setStockId(String stockId) {
            this.stockId = stockId;
        }

        public Integer getWechatpayContribute() {
            return wechatpayContribute;
        }

        public void setWechatpayContribute(Integer wechatpayContribute) {
            this.wechatpayContribute = wechatpayContribute;
        }

        public Integer getMerchantContribute() {
            return merchantContribute;
        }

        public void setMerchantContribute(Integer merchantContribute) {
            this.merchantContribute = merchantContribute;
        }

        public Integer getOtherContribute() {
            return otherContribute;
        }

        public void setOtherContribute(Integer otherContribute) {
            this.otherContribute = otherContribute;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public java.util.List<GoodsDetail> getGoodsDetail() {
            return goodsDetail;
        }

        public void setGoodsDetail(java.util.List<GoodsDetail> goodsDetail) {
            this.goodsDetail = goodsDetail;
        }
    }

    /**
     * 单品列表
     */
    public static class GoodsDetail {
        /** 商品编码 */
        @JsonProperty("goods_id")
        private String goodsId;

        /** 商品数量 */
        private Integer quantity;

        /** 商品单价 */
        @JsonProperty("unit_price")
        private Integer unitPrice;

        /** 商品优惠金额 */
        @JsonProperty("discount_amount")
        private Integer discountAmount;

        /** 商品备注 */
        @JsonProperty("goods_remark")
        private String goodsRemark;

        // Getters and Setters
        public String getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(String goodsId) {
            this.goodsId = goodsId;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public Integer getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(Integer unitPrice) {
            this.unitPrice = unitPrice;
        }

        public Integer getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(Integer discountAmount) {
            this.discountAmount = discountAmount;
        }

        public String getGoodsRemark() {
            return goodsRemark;
        }

        public void setGoodsRemark(String goodsRemark) {
            this.goodsRemark = goodsRemark;
        }
    }

    /** 是否成功 */
    public boolean isSuccess() {
        return "SUCCESS".equals(tradeState);
    }

    // Main class getters and setters
    public String getPrepayId() {
        return prepayId;
    }

    public void setPrepayId(String prepayId) {
        this.prepayId = prepayId;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeState() {
        return tradeState;
    }

    public void setTradeState(String tradeState) {
        this.tradeState = tradeState;
    }

    public String getTradeStateDesc() {
        return tradeStateDesc;
    }

    public void setTradeStateDesc(String tradeStateDesc) {
        this.tradeStateDesc = tradeStateDesc;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(String successTime) {
        this.successTime = successTime;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public Payer getPayer() {
        return payer;
    }

    public void setPayer(Payer payer) {
        this.payer = payer;
    }

    public SceneInfo getSceneInfo() {
        return sceneInfo;
    }

    public void setSceneInfo(SceneInfo sceneInfo) {
        this.sceneInfo = sceneInfo;
    }

    public java.util.List<PromotionDetail> getPromotionDetail() {
        return promotionDetail;
    }

    public void setPromotionDetail(java.util.List<PromotionDetail> promotionDetail) {
        this.promotionDetail = promotionDetail;
    }
}
