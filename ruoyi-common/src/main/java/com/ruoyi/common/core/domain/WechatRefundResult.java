package com.ruoyi.common.core.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 微信支付退款响应结果
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
public class WechatRefundResult {
    /**
     * 退款单号
     */
    @JsonProperty("refund_id")
    private String refundId;

    /**
     * 商户退款单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 微信支付交易单号
     */
    @JsonProperty("transaction_id")
    private String transactionId;

    /**
     * 商户订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 退款渠道
     */
    private String channel;

    /**
     * 退款入账账户
     */
    @JsonProperty("user_received_account")
    private String userReceivedAccount;

    /**
     * 退款成功时间
     */
    @JsonProperty("success_time")
    private String successTime;

    /**
     * 退款创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 退款状态
     */
    private String status;


    private String message;

    /**
     * 资金账户
     */
    @JsonProperty("funds_account")
    private String fundsAccount;

    /**
     * 金额信息
     */
    private RefundAmount amount;

    /**
     * 优惠退款信息
     */
    @JsonProperty("promotion_detail")
    private List<PromotionDetail> promotionDetail;

    /**
     * 退款金额信息
     */
    public static class RefundAmount {
        /**
         * 订单金额，单位为分
         */
        private Integer total;

        /**
         * 退款金额，单位为分
         */
        private Integer refund;

        /**
         * 用户支付金额，单位为分
         */
        @JsonProperty("payer_total")
        private Integer payerTotal;

        /**
         * 用户退款金额，单位为分
         */
        @JsonProperty("payer_refund")
        private Integer payerRefund;

        /**
         * 应结订单金额，单位为分
         */
        @JsonProperty("settlement_total")
        private Integer settlementTotal;

        /**
         * 应结退款金额，单位为分
         */
        @JsonProperty("settlement_refund")
        private Integer settlementRefund;

        /**
         * 优惠退款金额，单位为分
         */
        @JsonProperty("discount_refund")
        private Integer discountRefund;

        /**
         * 退款手续费，单位为分
         */
        @JsonProperty("refund_fee")
        private Integer refundFee;

        /**
         * 币种
         */
        private String currency;

        /**
         * 手续费来源账户列表
         */
        private List<Object> from;

        // Getters and Setters
        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        public Integer getRefund() {
            return refund;
        }

        public void setRefund(Integer refund) {
            this.refund = refund;
        }

        public Integer getPayerTotal() {
            return payerTotal;
        }

        public void setPayerTotal(Integer payerTotal) {
            this.payerTotal = payerTotal;
        }

        public Integer getPayerRefund() {
            return payerRefund;
        }

        public void setPayerRefund(Integer payerRefund) {
            this.payerRefund = payerRefund;
        }

        public Integer getSettlementTotal() {
            return settlementTotal;
        }

        public void setSettlementTotal(Integer settlementTotal) {
            this.settlementTotal = settlementTotal;
        }

        public Integer getSettlementRefund() {
            return settlementRefund;
        }

        public void setSettlementRefund(Integer settlementRefund) {
            this.settlementRefund = settlementRefund;
        }

        public Integer getDiscountRefund() {
            return discountRefund;
        }

        public void setDiscountRefund(Integer discountRefund) {
            this.discountRefund = discountRefund;
        }

        public Integer getRefundFee() {
            return refundFee;
        }

        public void setRefundFee(Integer refundFee) {
            this.refundFee = refundFee;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public List<Object> getFrom() {
            return from;
        }

        public void setFrom(List<Object> from) {
            this.from = from;
        }
    }

    /**
     * 优惠退款信息
     */
    public static class PromotionDetail {
        /**
         * 券或者立减优惠id
         */
        @JsonProperty("promotion_id")
        private String promotionId;

        /**
         * 优惠范围
         */
        private String scope;

        /**
         * 优惠类型
         */
        private String type;

        /**
         * 优惠券面额
         */
        private Integer amount;

        /**
         * 优惠退款金额
         */
        @JsonProperty("refund_amount")
        private Integer refundAmount;

        /**
         * 商品列表
         */
        @JsonProperty("goods_detail")
        private List<Object> goodsDetail;

        // Getters and Setters
        public String getPromotionId() {
            return promotionId;
        }

        public void setPromotionId(String promotionId) {
            this.promotionId = promotionId;
        }

        public String getScope() {
            return scope;
        }

        public void setScope(String scope) {
            this.scope = scope;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }

        public Integer getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(Integer refundAmount) {
            this.refundAmount = refundAmount;
        }

        public List<Object> getGoodsDetail() {
            return goodsDetail;
        }

        public void setGoodsDetail(List<Object> goodsDetail) {
            this.goodsDetail = goodsDetail;
        }
    }

    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }
    /**
     * 判断退款是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 判断退款是否处理中
     */
    public boolean isProcessing() {
        return "PROCESSING".equals(status);
    }

    /**
     * 判断退款是否关闭
     */
    public boolean isClosed() {
        return "CLOSED".equals(status);
    }

    /**
     * 判断退款是否异常
     */
    public boolean isAbnormal() {
        return "ABNORMAL".equals(status);
    }

    // Getters and Setters
    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getUserReceivedAccount() {
        return userReceivedAccount;
    }

    public void setUserReceivedAccount(String userReceivedAccount) {
        this.userReceivedAccount = userReceivedAccount;
    }

    public String getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(String successTime) {
        this.successTime = successTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFundsAccount() {
        return fundsAccount;
    }

    public void setFundsAccount(String fundsAccount) {
        this.fundsAccount = fundsAccount;
    }

    public RefundAmount getAmount() {
        return amount;
    }

    public void setAmount(RefundAmount amount) {
        this.amount = amount;
    }

    public List<PromotionDetail> getPromotionDetail() {
        return promotionDetail;
    }

    public void setPromotionDetail(List<PromotionDetail> promotionDetail) {
        this.promotionDetail = promotionDetail;
    }

    @Override
    public String toString() {
        return "WechatRefundResult{" +
                "refundId='" + refundId + '\'' +
                ", outRefundNo='" + outRefundNo + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", outTradeNo='" + outTradeNo + '\'' +
                ", channel='" + channel + '\'' +
                ", userReceivedAccount='" + userReceivedAccount + '\'' +
                ", successTime='" + successTime + '\'' +
                ", createTime='" + createTime + '\'' +
                ", status='" + status + '\'' +
                ", fundsAccount='" + fundsAccount + '\'' +
                ", amount=" + amount +
                ", promotionDetail=" + promotionDetail +
                '}';
    }
}
