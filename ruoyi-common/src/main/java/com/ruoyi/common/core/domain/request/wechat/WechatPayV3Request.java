package com.ruoyi.common.core.domain.request.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 微信支付V3版本下单请求
 * 
 * <AUTHOR>
 */
public class WechatPayV3Request
{
    /** 小程序ID */
    @NotBlank(message = "小程序ID不能为空")
    private String appid;

    /** 商户号 */
    @NotBlank(message = "商户号不能为空")
    @JsonProperty("mchid")
    private String mchid;

    /** 商品描述 */
    @NotBlank(message = "商品描述不能为空")
    private String description;

    /** 商户订单号 */
    @NotBlank(message = "商户订单号不能为空")
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /** 交易结束时间 */
    @JsonProperty("time_expire")
    private String timeExpire;

    /** 附加数据 */
    private String attach;

    /** 通知地址 */
    @NotBlank(message = "通知地址不能为空")
    @JsonProperty("notify_url")
    private String notifyUrl;

    /** 订单优惠标记 */
    @JsonProperty("goods_tag")
    private String goodsTag;

    /** 订单金额信息 */
    @NotNull(message = "订单金额信息不能为空")
    private Amount amount;

    /** 支付者信息 */
    @NotNull(message = "支付者信息不能为空")
    private Payer payer;

    /** 商品详情 */
    private Detail detail;

    /** 场景信息 */
    @JsonProperty("scene_info")
    private SceneInfo sceneInfo;

    /**
     * 订单金额信息
     */
    public static class Amount {
        /** 订单总金额，单位为分 */
        @NotNull(message = "订单金额不能为空")
        @Positive(message = "订单金额必须大于0")
        private Integer total;

        /** 货币类型 */
        private String currency = "CNY";

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }

    /**
     * 支付者信息
     */
    public static class Payer {
        /** 用户标识 */
        @NotBlank(message = "用户openid不能为空")
        private String openid;

        public String getOpenid() {
            return openid;
        }

        public void setOpenid(String openid) {
            this.openid = openid;
        }
    }

    /**
     * 商品详情
     */
    public static class Detail {
        /** 订单原价 */
        @JsonProperty("cost_price")
        private Integer costPrice;

        /** 商品小票ID */
        @JsonProperty("invoice_id")
        private String invoiceId;

        /** 商品列表 */
        @JsonProperty("goods_detail")
        private java.util.List<GoodsDetail> goodsDetail;

        public Integer getCostPrice() {
            return costPrice;
        }

        public void setCostPrice(Integer costPrice) {
            this.costPrice = costPrice;
        }

        public String getInvoiceId() {
            return invoiceId;
        }

        public void setInvoiceId(String invoiceId) {
            this.invoiceId = invoiceId;
        }

        public java.util.List<GoodsDetail> getGoodsDetail() {
            return goodsDetail;
        }

        public void setGoodsDetail(java.util.List<GoodsDetail> goodsDetail) {
            this.goodsDetail = goodsDetail;
        }
    }

    /**
     * 商品详情
     */
    public static class GoodsDetail {
        /** 商户侧商品编码 */
        @JsonProperty("merchant_goods_id")
        private String merchantGoodsId;

        /** 微信侧商品编码 */
        @JsonProperty("wechatpay_goods_id")
        private String wechatpayGoodsId;

        /** 商品名称 */
        @JsonProperty("goods_name")
        private String goodsName;

        /** 商品数量 */
        private Integer quantity;

        /** 商品单价 */
        @JsonProperty("unit_price")
        private Integer unitPrice;

        // Getters and Setters
        public String getMerchantGoodsId() {
            return merchantGoodsId;
        }

        public void setMerchantGoodsId(String merchantGoodsId) {
            this.merchantGoodsId = merchantGoodsId;
        }

        public String getWechatpayGoodsId() {
            return wechatpayGoodsId;
        }

        public void setWechatpayGoodsId(String wechatpayGoodsId) {
            this.wechatpayGoodsId = wechatpayGoodsId;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public Integer getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(Integer unitPrice) {
            this.unitPrice = unitPrice;
        }
    }

    /**
     * 场景信息
     */
    public static class SceneInfo {
        /** 用户终端IP */
        @JsonProperty("payer_client_ip")
        private String payerClientIp;

        /** 商户端设备号 */
        @JsonProperty("device_id")
        private String deviceId;

        /** 商户门店信息 */
        @JsonProperty("store_info")
        private StoreInfo storeInfo;

        public String getPayerClientIp() {
            return payerClientIp;
        }

        public void setPayerClientIp(String payerClientIp) {
            this.payerClientIp = payerClientIp;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public StoreInfo getStoreInfo() {
            return storeInfo;
        }

        public void setStoreInfo(StoreInfo storeInfo) {
            this.storeInfo = storeInfo;
        }
    }

    /**
     * 商户门店信息
     */
    public static class StoreInfo {
        /** 门店编号 */
        private String id;

        /** 门店名称 */
        private String name;

        /** 地区编码 */
        @JsonProperty("area_code")
        private String areaCode;

        /** 详细地址 */
        private String address;

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAreaCode() {
            return areaCode;
        }

        public void setAreaCode(String areaCode) {
            this.areaCode = areaCode;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }
    }

    // Main class getters and setters
    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTimeExpire() {
        return timeExpire;
    }

    public void setTimeExpire(String timeExpire) {
        this.timeExpire = timeExpire;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getGoodsTag() {
        return goodsTag;
    }

    public void setGoodsTag(String goodsTag) {
        this.goodsTag = goodsTag;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public Payer getPayer() {
        return payer;
    }

    public void setPayer(Payer payer) {
        this.payer = payer;
    }

    public Detail getDetail() {
        return detail;
    }

    public void setDetail(Detail detail) {
        this.detail = detail;
    }

    public SceneInfo getSceneInfo() {
        return sceneInfo;
    }

    public void setSceneInfo(SceneInfo sceneInfo) {
        this.sceneInfo = sceneInfo;
    }
}
