package com.ruoyi.common.core.domain.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户对象 user
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public class User extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickName;

    /**
     * 真实姓名
     */
    @Excel(name = "真实姓名")
    private String realName;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phoneNumber;

    /**
     * 用户性别
     */
    @Excel(name = "用户性别")
    private String gender;

    /**
     * 微信openid
     */
    @Excel(name = "微信openid")
    private String openid;

    /**
     * 用户在开放平台的唯一标识符
     */
    @Excel(name = "用户在开放平台的唯一标识符")
    private String unionid;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 用户所在公司
     */
    @Excel(name = "用户所在公司")
    private String company;

    /**
     * 职位
     */
    @Excel(name = "职位")
    private String position;

    /**
     * 身份号
     */
    @Excel(name = "身份号")
    private String idCard;

    /**
     * 注册来源
     */
    @Excel(name = "注册来源")
    private String regSource;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRealName() {
        return realName;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGender() {
        return gender;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCompany() {
        return company;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPosition() {
        return position;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setRegSource(String regSource) {
        this.regSource = regSource;
    }

    public String getRegSource() {
        return regSource;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("nickName", getNickName())
                .append("realName", getRealName())
                .append("phoneNumber", getPhoneNumber())
                .append("gender", getGender())
                .append("openid", getOpenid())
                .append("unionid", getUnionid())
                .append("email", getEmail())
                .append("company", getCompany())
                .append("position", getPosition())
                .append("idCard", getIdCard())
                .append("regSource", getRegSource())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
