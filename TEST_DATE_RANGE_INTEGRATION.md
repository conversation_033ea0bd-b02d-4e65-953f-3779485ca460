# 酒店预定页面日期范围集成测试

## 测试目标
验证酒店预定页面能够正确使用会议的 `bookingRangeStart` 和 `bookingRangeEnd` 字段来限制入住日期选择范围。

## 功能说明

### 1. 数据流程
```
会议详情API → Conference对象 → 日期范围设置 → 用户日期选择
     ↓              ↓              ↓              ↓
getConference  bookingRangeStart  minDate      验证范围
               bookingRangeEnd    maxDate      
```

### 2. 日期范围逻辑
- **最小日期**：`max(today, bookingRangeStart)`
- **最大日期**：`bookingRangeEnd`
- **验证规则**：选择的日期必须在 `[minDate, maxDate]` 范围内

## 测试用例

### 测试用例1：正常日期范围
**前置条件：**
- 会议设置了有效的 `bookingRangeStart` 和 `bookingRangeEnd`
- 日期范围在未来

**测试步骤：**
1. 进入酒店预定页面
2. 选择房型
3. 查看日期范围提示
4. 尝试选择入住日期

**预期结果：**
- 显示正确的日期范围提示
- 只能选择范围内的日期
- 范围外日期选择被拒绝并提示

### 测试用例2：日期范围为空
**前置条件：**
- 会议未设置 `bookingRangeStart` 或 `bookingRangeEnd`

**测试步骤：**
1. 进入酒店预定页面
2. 观察日期范围设置

**预期结果：**
- 使用默认日期范围（今天+1天 到 今天+3个月）
- 不显示日期范围提示
- 正常的日期选择功能

### 测试用例3：日期范围已过期
**前置条件：**
- 会议的 `bookingRangeEnd` 早于今天

**测试步骤：**
1. 进入酒店预定页面
2. 尝试选择日期

**预期结果：**
- 显示适当的错误提示
- 无法选择任何日期
- 引导用户联系管理员

### 测试用例4：跨日期的预订
**前置条件：**
- 会议日期范围正常
- 用户选择了入住日期

**测试步骤：**
1. 选择入住日期
2. 选择退房日期

**预期结果：**
- 退房日期必须晚于入住日期
- 退房日期不能超过 `bookingRangeEnd`
- 正确计算住宿天数和价格

## API接口测试

### 获取会议详情
```javascript
// 测试API调用
api.API.conference.getConference(conferenceId)
  .then(conference => {
    console.log('会议详情:', {
      id: conference.id,
      title: conference.conferenceTitle,
      bookingRangeStart: conference.bookingRangeStart,
      bookingRangeEnd: conference.bookingRangeEnd
    });
  });
```

### 预期返回数据
```json
{
  "id": 1,
  "conferenceTitle": "2024年度科技创新大会",
  "startTime": "2024-03-15 09:00:00",
  "endTime": "2024-03-17 18:00:00",
  "address": "北京国际会议中心",
  "bookingRangeStart": "2024-03-10",
  "bookingRangeEnd": "2024-03-20",
  "enable": "Y"
}
```

## 边界测试

### 1. 日期边界测试
- 选择 `bookingRangeStart` 当天
- 选择 `bookingRangeEnd` 当天
- 选择范围外的日期

### 2. 时区测试
- 验证日期格式转换正确
- 验证时区处理正确

### 3. 网络异常测试
- API调用失败时的处理
- 数据格式异常时的处理

## 用户体验验证

### 1. 提示信息
- [ ] 日期范围提示清晰明了
- [ ] 错误提示友好易懂
- [ ] 加载状态显示正常

### 2. 交互体验
- [ ] 日期选择流畅
- [ ] 范围限制生效
- [ ] 刷新功能正常

### 3. 视觉效果
- [ ] 日期范围提示样式美观
- [ ] 错误状态显示清晰
- [ ] 响应式布局正常

## 数据验证

### 1. 日期格式
- 确保日期格式为 `yyyy-MM-dd`
- 验证日期解析正确

### 2. 范围计算
- 验证最小日期计算正确
- 验证最大日期计算正确
- 验证范围验证逻辑正确

### 3. 状态同步
- 验证页面状态与数据同步
- 验证用户选择与限制同步

## 错误处理验证

### 1. 参数缺失
- 缺少会议ID时的处理
- 缺少识别码时的处理

### 2. 数据异常
- 会议不存在时的处理
- 日期范围无效时的处理

### 3. 网络异常
- API调用超时的处理
- 网络连接失败的处理

## 性能测试

### 1. 加载性能
- 页面初始化时间
- API调用响应时间
- 数据处理时间

### 2. 内存使用
- 页面内存占用
- 数据缓存策略
- 内存泄漏检查

## 兼容性测试

### 1. 设备兼容
- 不同屏幕尺寸
- 不同操作系统版本
- 不同微信版本

### 2. 数据兼容
- 旧版本数据格式
- 空值处理
- 异常数据处理
