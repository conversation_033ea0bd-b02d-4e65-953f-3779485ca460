-- 为酒店订单表添加分类码字段
-- 执行时间：2025-01-17
-- 说明：添加category_id字段用于记录订单关联的分类码（识别码）

-- 添加分类码字段
ALTER TABLE `hotel_order` ADD COLUMN `category_id` VARCHAR(8) COMMENT '分类码（识别码）' AFTER `conference_id`;

-- 添加索引以提高查询性能
ALTER TABLE `hotel_order` ADD INDEX `idx_category_id` (`category_id`);

-- 验证字段添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'hotel_order' AND COLUMN_NAME = 'category_id';
