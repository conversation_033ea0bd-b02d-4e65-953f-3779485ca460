-- 调试识别码数据的SQL脚本

-- 1. 检查识别码表的数据
SELECT '=== 识别码表数据 ===' as info;
SELECT category_id, conference, room_count, create_time 
FROM category_code 
ORDER BY create_time DESC 
LIMIT 10;

-- 2. 检查是否存在关联表
SELECT '=== 检查关联表是否存在 ===' as info;
SHOW TABLES LIKE 'category_code_room';

-- 3. 检查关联表的数据
SELECT '=== 关联表数据 ===' as info;
SELECT ccr.category_id, ccr.conference, ccr.room_id, ccr.room_count, r.room_title
FROM category_code_room ccr
LEFT JOIN room r ON ccr.room_id = r.id
ORDER BY ccr.create_time DESC
LIMIT 10;

-- 4. 检查是否有识别码没有关联数据
SELECT '=== 没有关联数据的识别码 ===' as info;
SELECT cc.category_id, cc.conference, cc.room_count
FROM category_code cc
LEFT JOIN category_code_room ccr ON cc.category_id = ccr.category_id AND cc.conference = ccr.conference
WHERE ccr.category_id IS NULL;

-- 5. 检查房型表数据
SELECT '=== 房型表数据 ===' as info;
SELECT id, conference_id, room_title, create_time
FROM room
ORDER BY create_time DESC
LIMIT 10;

-- 6. 统计数据
SELECT '=== 数据统计 ===' as info;
SELECT 
    (SELECT COUNT(*) FROM category_code) as category_code_count,
    (SELECT COUNT(*) FROM category_code_room) as category_code_room_count,
    (SELECT COUNT(*) FROM room) as room_count;
