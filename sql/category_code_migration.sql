-- 识别码管理多房型支持迁移脚本
-- 执行前请备份数据库

-- 1. 创建新的关联表
CREATE TABLE IF NOT EXISTS `category_code_room` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `category_id` VARCHAR(8) NOT NULL COMMENT '识别码',
    `conference` BIGINT NOT NULL COMMENT '会议ID',
    `room_id` BIGINT NOT NULL COMMENT '房间ID',
    `room_count` INTEGER DEFAULT 1 COMMENT '该房型的房间数量',
    `create_time` DATETIME COMMENT '创建时间',
    PRIMARY KEY(`id`),
    UNIQUE KEY `uk_category_room` (`category_id`, `conference`, `room_id`)
) COMMENT='识别码房型关联表';

-- 2. 迁移现有数据到关联表
INSERT INTO `category_code_room` (`category_id`, `conference`, `room_id`, `room_count`, `create_time`)
SELECT `category_id`, `conference`, `room_id`, 1, NOW()
FROM `category_code`
WHERE `room_id` IS NOT NULL;

-- 3. 删除原表中的 room_id 字段
ALTER TABLE `category_code` DROP COLUMN `room_id`;

-- 4. 验证数据迁移结果
-- SELECT cc.category_id, cc.conference, cc.room_count, 
--        GROUP_CONCAT(r.room_title) as room_titles
-- FROM category_code cc
-- LEFT JOIN category_code_room ccr ON cc.category_id = ccr.category_id AND cc.conference = ccr.conference
-- LEFT JOIN room r ON ccr.room_id = r.id
-- GROUP BY cc.category_id, cc.conference
-- ORDER BY cc.create_time DESC;
