-- 创建订单表
DROP TABLE IF EXISTS `hotel_order`;
CREATE TABLE `hotel_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `openid` varchar(128) DEFAULT NULL COMMENT '微信用户openid',
  `conference_id` bigint(20) NOT NULL COMMENT '会议ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `room_type` varchar(50) NOT NULL COMMENT '房间类型',
  `room_name` varchar(100) NOT NULL COMMENT '房间名称',
  `checkin_date` date NOT NULL COMMENT '入住日期',
  `checkout_date` date NOT NULL COMMENT '退房日期',
  `nights` int(11) NOT NULL COMMENT '住宿天数',
  `room_price` decimal(10,2) NOT NULL COMMENT '房间单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `deposit_amount` decimal(10,2) NOT NULL COMMENT '押金金额',
  `order_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态：PENDING-待支付,PAID-已支付,CONFIRMED-已确认,CANCELLED-已取消,REFUNDED-已退款',
  `payment_status` varchar(20) NOT NULL DEFAULT 'UNPAID' COMMENT '支付状态：UNPAID-未支付,PAID-已支付,REFUNDING-退款中,REFUNDED-已退款',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式：WECHAT-微信支付',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付交易号',
  `prepay_id` varchar(64) DEFAULT NULL COMMENT '微信预支付交易会话标识',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_reason` varchar(200) DEFAULT NULL COMMENT '退款原因',
  `guest_name` varchar(50) DEFAULT NULL COMMENT '入住人姓名',
  `guest_phone` varchar(20) DEFAULT NULL COMMENT '入住人电话',
  `guest_id_card` varchar(20) DEFAULT NULL COMMENT '入住人身份证号',
  `special_requirements` text COMMENT '特殊要求',
  `remark` text COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_conference_id` (`conference_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店订单表';

-- 创建订单状态变更记录表
DROP TABLE IF EXISTS `hotel_order_status_log`;
CREATE TABLE `hotel_order_status_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `old_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `new_status` varchar(20) NOT NULL COMMENT '新状态',
  `status_type` varchar(20) NOT NULL COMMENT '状态类型：ORDER-订单状态,PAYMENT-支付状态',
  `change_reason` varchar(200) DEFAULT NULL COMMENT '变更原因',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态变更记录表';

-- 插入测试数据
INSERT INTO `hotel_order` (
  `order_no`, `user_id`, `openid`, `conference_id`, `room_id`, `room_type`, `room_name`,
  `checkin_date`, `checkout_date`, `nights`, `room_price`, `total_amount`, `deposit_amount`,
  `order_status`, `payment_status`, `guest_name`, `guest_phone`, `create_by`
) VALUES 
('HT202501170001', 1, 'test_openid_001', 1, 1, 'DELUXE', '豪华大床房', 
 '2025-03-16', '2025-03-18', 2, 200.00, 400.00, 100.00, 
 'PENDING', 'UNPAID', '张三', '13800138001', 'system'),
('HT202501170002', 2, 'test_openid_002', 1, 2, 'SUITE', '商务套房', 
 '2025-03-17', '2025-03-19', 2, 300.00, 600.00, 150.00, 
 'PAID', 'PAID', '李四', '13800138002', 'system'),
('HT202501170003', 1, 'test_openid_001', 2, 3, 'STANDARD', '标准双床房', 
 '2025-03-20', '2025-03-22', 2, 150.00, 300.00, 80.00, 
 'CONFIRMED', 'PAID', '王五', '13800138003', 'system');
