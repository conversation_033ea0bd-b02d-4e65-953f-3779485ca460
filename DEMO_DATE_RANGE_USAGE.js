// DEMO_DATE_RANGE_USAGE.js - 演示日期范围功能的关键代码

// 1. 数据结构示例
const pageData = {
  conferenceId: '1',
  categoryId: 'TEST123',
  conferenceData: null, // 完整会议信息
  bookingRangeStart: '', // 会议预定开始日期
  bookingRangeEnd: '', // 会议预定结束日期
  minDate: '', // 实际可选最小日期
  maxDate: '', // 实际可选最大日期
};

// 2. 会议详情API调用示例
function loadConferenceData() {
  const conferenceId = this.data.conferenceId;
  
  api.API.conference.getConference(conferenceId)
    .then(conference => {
      console.log('获取会议详情:', conference);
      
      // 预期的会议数据格式
      const expectedConference = {
        id: 1,
        conferenceTitle: "2024年度科技创新大会",
        address: "北京国际会议中心",
        startTime: "2024-03-15 09:00:00",
        endTime: "2024-03-17 18:00:00",
        bookingRangeStart: "2024-03-10", // 关键字段
        bookingRangeEnd: "2024-03-20",   // 关键字段
        enable: "Y"
      };
      
      this.processConferenceData(conference);
      this.loadRoomTypes();
    })
    .catch(err => {
      console.error('获取会议详情失败:', err);
      // 降级处理
      this.initDateRange(); // 使用默认日期范围
      this.loadRoomTypes();
    });
}

// 3. 日期范围设置示例
function setBookingDateRange(conference) {
  const today = new Date();
  
  if (conference.bookingRangeStart && conference.bookingRangeEnd) {
    const rangeStart = new Date(conference.bookingRangeStart);
    const rangeEnd = new Date(conference.bookingRangeEnd);
    
    // 计算实际可选日期范围
    const minDate = rangeStart > today ? rangeStart : today;
    const maxDate = rangeEnd;
    
    this.setData({
      bookingRangeStart: this.formatDate(rangeStart),
      bookingRangeEnd: this.formatDate(rangeEnd),
      minDate: this.formatDate(minDate),
      maxDate: this.formatDate(maxDate)
    });
    
    console.log('设置日期范围:', {
      会议开始: conference.bookingRangeStart,
      会议结束: conference.bookingRangeEnd,
      实际最小: this.formatDate(minDate),
      实际最大: this.formatDate(maxDate)
    });
  } else {
    // 使用默认范围
    this.initDateRange();
  }
}

// 4. 日期验证示例
function validateDateInRange(dateString) {
  const selectedDate = new Date(dateString);
  const rangeStart = new Date(this.data.bookingRangeStart);
  const rangeEnd = new Date(this.data.bookingRangeEnd);
  
  // 验证日期是否在允许范围内
  if (selectedDate < rangeStart || selectedDate > rangeEnd) {
    wx.showToast({
      title: `请选择${this.data.bookingRangeStart}至${this.data.bookingRangeEnd}之间的日期`,
      icon: 'none',
      duration: 3000
    });
    return false;
  }
  
  return true;
}

// 5. 日期选择处理示例
function showDatePicker(e) {
  const type = e.currentTarget.dataset.type;
  
  // 检查日期范围是否已设置
  if (!this.data.bookingRangeStart || !this.data.bookingRangeEnd) {
    wx.showToast({
      title: '日期范围未设置，请稍后重试',
      icon: 'none'
    });
    return;
  }
  
  const rangeStart = new Date(this.data.bookingRangeStart);
  const rangeEnd = new Date(this.data.bookingRangeEnd);
  
  if (type === 'checkin') {
    // 选择入住日期
    const selectedDate = this.formatDate(rangeStart);
    if (this.validateDateInRange(selectedDate)) {
      this.setData({
        checkinDate: selectedDate,
        checkoutDate: ''
      });
    }
  } else if (type === 'checkout') {
    // 选择退房日期
    if (!this.data.checkinDate) {
      wx.showToast({
        title: '请先选择入住日期',
        icon: 'none'
      });
      return;
    }
    
    const checkinDate = new Date(this.data.checkinDate);
    const nextDay = new Date(checkinDate);
    nextDay.setDate(checkinDate.getDate() + 1);
    
    const checkoutDate = nextDay > rangeEnd ? rangeEnd : nextDay;
    const selectedDate = this.formatDate(checkoutDate);
    
    if (this.validateDateInRange(selectedDate)) {
      this.setData({
        checkoutDate: selectedDate
      });
    }
  }
}

// 6. 使用示例
const usageExample = {
  // 页面加载时
  onLoad: function(options) {
    // 设置基本参数
    this.setData({
      conferenceId: options.conferenceId,
      categoryId: options.categoryId
    });
    
    // 加载会议数据（包含日期范围）
    this.loadConferenceData();
  },
  
  // 用户选择日期时
  onUserSelectDate: function(selectedDate) {
    // 自动验证日期范围
    if (this.validateDateInRange(selectedDate)) {
      // 处理有效的日期选择
      this.processDateSelection(selectedDate);
    }
    // 无效日期会自动显示错误提示
  }
};

// 7. 测试用例
const testCases = [
  {
    name: "正常日期范围",
    conference: {
      bookingRangeStart: "2024-03-10",
      bookingRangeEnd: "2024-03-20"
    },
    testDates: ["2024-03-15"], // 应该通过
    expectedResult: true
  },
  {
    name: "超出范围日期",
    conference: {
      bookingRangeStart: "2024-03-10", 
      bookingRangeEnd: "2024-03-20"
    },
    testDates: ["2024-03-25"], // 应该被拒绝
    expectedResult: false
  },
  {
    name: "空日期范围",
    conference: {
      bookingRangeStart: null,
      bookingRangeEnd: null
    },
    testDates: ["2024-03-15"], // 使用默认范围
    expectedResult: true
  }
];

module.exports = {
  pageData,
  loadConferenceData,
  setBookingDateRange,
  validateDateInRange,
  showDatePicker,
  usageExample,
  testCases
};
