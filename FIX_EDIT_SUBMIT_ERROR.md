# 识别码编辑提交错误修复

## 问题描述
编辑识别码时提交数据报错：
```
{"msg":"Request method 'POST' not supported","code":500}
```
调用的接口地址是：`http://localhost/hotel/categoryCode`

## 问题原因
前端提交时使用的URL路径不正确：
- **前端调用**: `/hotel/categoryCode` (POST)
- **后端期望**: `/hotel/categoryCode/edit` (POST)

## 修复内容

### 1. 修复编辑页面提交URL
**文件**: `ruoyi-admin/src/main/resources/templates/hotel/categoryCode/edit.html`

**修改前**:
```javascript
$.operate.save(prefix, formData);
```

**修改后**:
```javascript
$.operate.save(prefix + "/edit", formData);
```

### 2. 修复新增页面提交URL
**文件**: `ruoyi-admin/src/main/resources/templates/hotel/categoryCode/add.html`

**修改前**:
```javascript
$.operate.save(prefix, formData);
```

**修改后**:
```javascript
$.operate.save(prefix + "/add", formData);
```

### 3. 清理重复代码
删除了新增页面中重复的 `submitHandler` 函数定义。

## 后端路由配置
确认后端路由配置正确：

```java
@RequestMapping("/hotel/categoryCode")
public class CategoryCodeController {
    
    // 新增保存
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CategoryCode categoryCode, @RequestParam(required = false) String roomCountMapJson) {
        // ...
    }
    
    // 修改保存
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CategoryCode categoryCode, @RequestParam(required = false) String roomCountMapJson) {
        // ...
    }
}
```

## 完整的URL映射

| 操作 | 前端调用 | 后端映射 | HTTP方法 |
|------|----------|----------|----------|
| 列表查询 | `/hotel/categoryCode/list` | `@PostMapping("/list")` | POST |
| 新增页面 | `/hotel/categoryCode/add` | `@GetMapping("/add")` | GET |
| 新增保存 | `/hotel/categoryCode/add` | `@PostMapping("/add")` | POST |
| 编辑页面 | `/hotel/categoryCode/edit/{id}/{conf}` | `@GetMapping("/edit/{categoryId}/{conference}")` | GET |
| 编辑保存 | `/hotel/categoryCode/edit` | `@PostMapping("/edit")` | POST |
| 删除 | `/hotel/categoryCode/remove` | `@PostMapping("/remove")` | POST |

## 测试验证

### 1. 新增识别码测试
1. 访问新增页面：`/hotel/categoryCode/add`
2. 填写表单数据
3. 点击提交，应该调用 `POST /hotel/categoryCode/add`

### 2. 编辑识别码测试
1. 访问编辑页面：`/hotel/categoryCode/edit/{categoryId}/{conference}`
2. 修改表单数据
3. 点击提交，应该调用 `POST /hotel/categoryCode/edit`

### 3. 验证请求URL
在浏览器开发者工具的Network标签中查看：
- 请求URL应该包含正确的路径
- 请求方法应该是POST
- 请求应该返回成功响应

## 常见问题排查

### 1. 仍然报错 "Request method 'POST' not supported"
- 检查前端JavaScript是否正确加载
- 确认浏览器缓存已清理
- 验证后端Controller类的@RequestMapping注解

### 2. 房型数量配置丢失
- 确认前端正确构建了 `roomCountMapJson` 参数
- 检查后端是否正确解析JSON数据

### 3. 权限错误
- 确认用户具有 `hotel:categoryCode:add` 和 `hotel:categoryCode:edit` 权限

## 相关文件清单

### 前端文件
- `ruoyi-admin/src/main/resources/templates/hotel/categoryCode/add.html`
- `ruoyi-admin/src/main/resources/templates/hotel/categoryCode/edit.html`

### 后端文件
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/hotel/CategoryCodeController.java`

## 注意事项

1. **清理浏览器缓存**: 修改前端文件后需要清理浏览器缓存
2. **权限检查**: 确保用户具有相应的操作权限
3. **数据格式**: 房型数量映射以JSON格式传递，确保格式正确
4. **错误处理**: 后端已添加JSON解析错误处理
