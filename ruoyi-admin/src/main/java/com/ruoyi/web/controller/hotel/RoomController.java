package com.ruoyi.web.controller.hotel;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.service.IRoomService;
import com.ruoyi.system.service.IConferenceService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;

/**
 * 房型Controller
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Controller
@RequestMapping("/hotel/room")
public class RoomController extends BaseController
{
    private String prefix = "hotel/room";

    @Autowired
    private IRoomService roomService;

    @Autowired
    private IConferenceService conferenceService;

    @RequiresPermissions("hotel:room:view")
    @GetMapping()
    public String room()
    {
        return prefix + "/room";
    }

    /**
     * 查询房型列表
     */
    @RequiresPermissions("hotel:room:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Room room)
    {
        startPage();
        List<Room> list = roomService.selectRoomList(room);
        return getDataTable(list);
    }

    /**
     * 导出房型列表
     */
    @RequiresPermissions("hotel:room:export")
    @Log(title = "房型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Room room)
    {
        List<Room> list = roomService.selectRoomList(room);
        ExcelUtil<Room> util = new ExcelUtil<Room>(Room.class);
        return util.exportExcel(list, "房型数据");
    }

    /**
     * 新增房型
     */
    @RequiresPermissions("hotel:room:add")
    @GetMapping("/add")
    public String add(ModelMap mmap)
    {
        // 获取会议列表供选择
        List<Conference> conferences = conferenceService.selectConferenceList(new Conference());
        mmap.put("conferences", conferences);
        return prefix + "/add";
    }

    /**
     * 新增保存房型
     */
    @RequiresPermissions("hotel:room:add")
    @Log(title = "房型", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Room room)
    {
        room.setOperator(getLoginName());
        return toAjax(roomService.insertRoom(room));
    }

    /**
     * 修改房型
     */
    @RequiresPermissions("hotel:room:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        Room room = roomService.selectRoomById(id);
        mmap.put("room", room);
        // 获取会议列表供选择
        List<Conference> conferences = conferenceService.selectConferenceList(new Conference());
        mmap.put("conferences", conferences);
        return prefix + "/edit";
    }

    /**
     * 修改保存房型
     */
    @RequiresPermissions("hotel:room:edit")
    @Log(title = "房型", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Room room)
    {
        room.setOperator(getLoginName());
        return toAjax(roomService.updateRoom(room));
    }

    /**
     * 删除房型
     */
    @RequiresPermissions("hotel:room:remove")
    @Log(title = "房型", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(roomService.deleteRoomByIds(Convert.toLongArray(ids)));
    }

    /**
     * 查看房型详情
     */
    @RequiresPermissions("hotel:room:view")
    @GetMapping("/view/{id}")
    public String view(@PathVariable("id") Long id, ModelMap mmap)
    {
        Room room = roomService.selectRoomById(id);
        mmap.put("room", room);
        return prefix + "/view";
    }

    /**
     * 根据会议ID获取房型列表
     */
    @PostMapping("/listByConference")
    @ResponseBody
    public AjaxResult listByConference(Long conferenceId)
    {
        List<Room> rooms = roomService.selectRoomListByConferenceId(conferenceId);
        return AjaxResult.success(rooms);
    }
}
