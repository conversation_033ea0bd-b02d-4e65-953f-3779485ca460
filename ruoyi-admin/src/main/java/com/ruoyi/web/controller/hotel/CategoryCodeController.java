package com.ruoyi.web.controller.hotel;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CategoryCode;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.service.ICategoryCodeService;
import com.ruoyi.system.service.IConferenceService;
import com.ruoyi.system.service.IRoomService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 识别码Controller
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Controller
@RequestMapping("/hotel/categoryCode")
public class CategoryCodeController extends BaseController {
    private String prefix = "hotel/categoryCode";

    @Autowired
    private ICategoryCodeService categoryCodeService;

    @Autowired
    private IConferenceService conferenceService;

    @Autowired
    private IRoomService roomService;

    @RequiresPermissions("hotel:categoryCode:view")
    @GetMapping()
    public String categoryCode() {
        return prefix + "/categoryCode";
    }

    /**
     * 查询识别码列表
     */
    @RequiresPermissions("hotel:categoryCode:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CategoryCode categoryCode) {
        startPage();
        List<CategoryCode> list = categoryCodeService.selectCategoryCodeList(categoryCode);
        return getDataTable(list);
    }

    /**
     * 导出识别码列表
     */
    @RequiresPermissions("hotel:categoryCode:export")
    @Log(title = "识别码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CategoryCode categoryCode) {
        List<CategoryCode> list = categoryCodeService.selectCategoryCodeList(categoryCode);
        ExcelUtil<CategoryCode> util = new ExcelUtil<CategoryCode>(CategoryCode.class);
        return util.exportExcel(list, "识别码数据");
    }

    /**
     * 新增识别码
     */
    @RequiresPermissions("hotel:categoryCode:add")
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        // 获取会议列表供选择
        List<Conference> conferences = conferenceService.selectConferenceList(new Conference());
        mmap.put("conferences", conferences);
        return prefix + "/add";
    }

    /**
     * 新增保存识别码
     */
    @RequiresPermissions("hotel:categoryCode:add")
    @Log(title = "识别码", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CategoryCode categoryCode, @RequestParam(required = false) String roomCountMapJson) {
        // 检查识别码是否已存在
        CategoryCode existingCode = categoryCodeService.selectCategoryCodeById(
                categoryCode.getCategoryId(), categoryCode.getConference());
        if (existingCode != null) {
            return error("识别码已存在");
        }

        // 处理房型数量映射
        if (roomCountMapJson != null && !roomCountMapJson.isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Integer> roomCountStrMap = mapper.readValue(roomCountMapJson, Map.class);
                Map<Long, Integer> roomCountMap = new HashMap<>();
                for (Map.Entry<String, Integer> entry : roomCountStrMap.entrySet()) {
                    roomCountMap.put(Long.parseLong(entry.getKey()), entry.getValue());
                }
                categoryCode.setRoomCountMap(roomCountMap);
            } catch (Exception e) {
                return error("房型数量配置格式错误");
            }
        }

        return toAjax(categoryCodeService.insertCategoryCode(categoryCode));
    }

    /**
     * 修改识别码
     */
    @RequiresPermissions("hotel:categoryCode:edit")
    @GetMapping("/edit/{categoryId}/{conference}")
    public String edit(@PathVariable("categoryId") String categoryId,
                       @PathVariable("conference") Long conference, ModelMap mmap) {
        CategoryCode categoryCode = categoryCodeService.selectCategoryCodeById(categoryId, conference);
        mmap.put("categoryCode", categoryCode);
        // 获取会议列表供选择
        List<Conference> conferences = conferenceService.selectConferenceList(new Conference());
        mmap.put("conferences", conferences);
        // 获取对应会议的房型列表
        if (categoryCode.getConference() != null) {
            List<Room> rooms = roomService.selectRoomListByConferenceId(categoryCode.getConference());
            mmap.put("rooms", rooms);
        }
        return prefix + "/edit";
    }

    /**
     * 修改保存识别码
     */
    @RequiresPermissions("hotel:categoryCode:edit")
    @Log(title = "识别码", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CategoryCode categoryCode, @RequestParam(required = false) String roomCountMapJson) {
        // 处理房型数量映射
        if (roomCountMapJson != null && !roomCountMapJson.isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Integer> roomCountStrMap = mapper.readValue(roomCountMapJson, Map.class);
                Map<Long, Integer> roomCountMap = new HashMap<>();
                for (Map.Entry<String, Integer> entry : roomCountStrMap.entrySet()) {
                    roomCountMap.put(Long.parseLong(entry.getKey()), entry.getValue());
                }
                categoryCode.setRoomCountMap(roomCountMap);
            } catch (Exception e) {
                return error("房型数量配置格式错误");
            }
        }

        return toAjax(categoryCodeService.updateCategoryCode(categoryCode));
    }

    /**
     * 删除识别码
     */
    @RequiresPermissions("hotel:categoryCode:remove")
    @Log(title = "识别码", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(categoryCodeService.deleteCategoryCodeByIds(ids.split(",")));
    }

    /**
     * 查看识别码详情
     */
    @RequiresPermissions("hotel:categoryCode:view")
    @GetMapping("/view/{categoryId}/{conference}")
    public String view(@PathVariable("categoryId") String categoryId,
                       @PathVariable("conference") Long conference, ModelMap mmap) {
        CategoryCode categoryCode = categoryCodeService.selectCategoryCodeById(categoryId, conference);
        mmap.put("categoryCode", categoryCode);
        return prefix + "/view";
    }

    /**
     * 验证识别码
     */
    @PostMapping("/validate")
    @ResponseBody
    public AjaxResult validateCode(String categoryId, Long conference) {
        CategoryCode categoryCode = categoryCodeService.validateCategoryCode(categoryId, conference);
        if (categoryCode != null) {
            return AjaxResult.success("识别码验证成功", categoryCode);
        } else {
            return AjaxResult.error("识别码不存在或已失效");
        }
    }

    /**
     * 根据会议ID获取房型列表
     */
    @GetMapping("/getRoomsByConference/{conferenceId}")
    @ResponseBody
    public AjaxResult getRoomsByConference(@PathVariable("conferenceId") Long conferenceId) {
        List<Room> rooms = roomService.selectRoomListByConferenceId(conferenceId);
        return AjaxResult.success(rooms);
    }
}
