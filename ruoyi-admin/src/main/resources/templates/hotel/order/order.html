<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>订单号：</label>
                                <input type="text" name="orderNo"/>
                            </li>
                            <li>
                                <label>入住人姓名：</label>
                                <input type="text" name="guestName"/>
                            </li>
                            <li>
                                <label>入住人电话：</label>
                                <input type="text" name="guestPhone"/>
                            </li>
                            <li>
                                <label>订单状态：</label>
                                <select name="orderStatus">
                                    <option value="">所有</option>
                                    <option value="PENDING">待支付</option>
                                    <option value="PAID">已支付</option>
                                    <option value="CONFIRMED">已确认</option>
                                    <option value="CANCELLED">已取消</option>
                                    <option value="REFUNDED">已退款</option>
                                </select>
                            </li>
                            <li>
                                <label>支付状态：</label>
                                <select name="paymentStatus">
                                    <option value="">所有</option>
                                    <option value="UNPAID">未支付</option>
                                    <option value="PAID">已支付</option>
                                    <option value="REFUNDED">已退款</option>
                                </select>
                            </li>
                            <li>
                                <label>关联会议：</label>
                                <select name="conferenceId">
                                    <option value="">所有会议</option>
                                    <option th:each="conference : ${conferenceList}"
                                            th:value="${conference.id}"
                                            th:text="${conference.conferenceTitle}"></option>
                                </select>
                            </li>
                            <li>
                                <label>入住日期：</label>
                                <input type="text" class="time-input" placeholder="请选择入住日期" name="checkinDate"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-info" onclick="showStatistics()" shiro:hasPermission="hotel:order:list">
                    <i class="fa fa-bar-chart"></i> 统计
                </a>

                <a class="btn btn-primary single disabled" onclick="refreshOrderStatus()" shiro:hasPermission="hotel:order:edit">
                    <i class="fa fa-refresh"></i> 刷新状态
                </a>
                <a class="btn btn-warning single disabled" onclick="refundOrder()" shiro:hasPermission="hotel:order:edit">
                    <i class="fa fa-undo"></i> 退款订单
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="hotel:order:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('hotel:order:edit')}]];
        var prefix = ctx + "hotel/order";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                viewUrl: prefix + "/view/{id}",
                exportUrl: prefix + "/export",
                modalName: "订单",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'orderId',
                    title: '订单ID',
                    visible: false
                },
                {
                    field: 'orderNo',
                    title: '订单号',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs btn-outline btn-default" href="javascript:void(0)" onclick="$.operate.view(\'' + row.orderId + '\')">' + value + '</a>');
                        return actions.join('');
                    }
                },
                {
                    field: 'guestName',
                    title: '入住人'
                },
                {
                    field: 'guestPhone',
                    title: '联系电话'
                },
                {
                    field: 'conferenceTitle',
                    title: '关联会议'
                },
                {
                    field: 'roomName',
                    title: '房间名称'
                },
                {
                    field: 'checkinDate',
                    title: '入住日期'
                },
                {
                    field: 'checkoutDate',
                    title: '退房日期'
                },
                {
                    field: 'nights',
                    title: '住宿天数'
                },
                {
                    field: 'totalAmount',
                    title: '订单金额',
                    formatter: function(value, row, index) {
                        return '¥' + parseFloat(value).toFixed(2);
                    }
                },
                {
                    field: 'orderStatus',
                    title: '订单状态',
                    formatter: function(value, row, index) {
                        var statusMap = {
                            'PENDING': '<span class="label label-warning">待支付</span>',
                            'PAID': '<span class="label label-primary">已支付</span>',
                            'CONFIRMED': '<span class="label label-success">已确认</span>',
                            'CANCELLED': '<span class="label label-danger">已取消</span>',
                            'REFUNDED': '<span class="label label-info">已退款</span>'
                        };
                        return statusMap[value] || value;
                    }
                },
                {
                    field: 'paymentStatus',
                    title: '支付状态',
                    formatter: function(value, row, index) {
                        var statusMap = {
                            'UNPAID': '<span class="label label-warning">未支付</span>',
                            'PAID': '<span class="label label-success">已支付</span>',
                            'REFUNDED': '<span class="label label-info">已退款</span>'
                        };
                        return statusMap[value] || value;
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.view(\'' + row.orderId + '\')"><i class="fa fa-search"></i>查看</a> ');

                        if (editFlag && (row.orderStatus === 'PENDING' || row.orderStatus === 'PAID')) {
                            actions.push('<a class="btn btn-info btn-xs " href="javascript:void(0)" onclick="refreshSingleOrderStatus(\'' + row.orderId + '\')"><i class="fa fa-refresh"></i>刷新</a> ');
                        }
                        if (editFlag && (row.orderStatus === 'PAID' || row.orderStatus === 'CONFIRMED')) {
                            actions.push('<a class="btn btn-warning btn-xs " href="javascript:void(0)" onclick="showRefundDialog(\'' + row.orderId + '\')"><i class="fa fa-undo"></i>退款</a> ');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);


        });

        // 显示统计信息
        function showStatistics() {
            $.post(prefix + "/statistics", function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="row">' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.totalCount + '</h3><p>总订单</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.pendingCount + '</h3><p>待支付</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.paidCount + '</h3><p>已支付</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.confirmedCount + '</h3><p>已确认</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.cancelledCount + '</h3><p>已取消</p></div></div>' +
                        '<div class="col-sm-2"><div class="text-center"><h3>' + data.refundedCount + '</h3><p>已退款</p></div></div>' +
                        '</div>';
                    
                    $.modal.open("订单统计", content);
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }









        // 退款订单
        function refundOrder() {
            var rows = $.table.selectColumns("orderId");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择至少一条记录");
                return;
            }

            var orderId = rows[0];
            showRefundDialog(orderId);
        }

        // 退款单个订单
        function refundSingleOrder(orderId) {
            showRefundDialog(orderId);
        }

        // 显示退款对话框
        function showRefundDialog(orderId) {
            console.log("退款订单ID:", orderId);
            console.log("请求URL:", prefix + "/refund/" + orderId);

            // 从表格中获取订单数据
            var orderData = getOrderDataById(orderId);
            var totalAmount = orderData ? orderData.totalAmount : '';

            var content = '<div class="form-horizontal">' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">退款金额：</label>' +
                '<div class="col-sm-8">' +
                '<input type="number" step="0.01" min="0.01" max="' + totalAmount + '" class="form-control" id="refundAmount" value="' + totalAmount + '" placeholder="请输入退款金额">' +
                '<span class="help-block">订单总金额：¥' + totalAmount + '</span>' +
                '</div>' +
                '</div>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">退款原因：</label>' +
                '<div class="col-sm-8">' +
                '<textarea class="form-control" id="refundReason" rows="3" placeholder="请输入退款原因"></textarea>' +
                '</div>' +
                '</div>' +
                '</div>';

            layer.open({
                type: 1,
                title: "订单退款",
                content: content,
                area: ['500px', '400px'],
                btn: ['确认退款', '取消'],
                yes: function(index, layero) {
                    var refundAmount = $("#refundAmount").val();
                    var refundReason = $("#refundReason").val();

                    if (!refundAmount || parseFloat(refundAmount) <= 0) {
                        layer.msg("请输入有效的退款金额", {icon: 2});
                        return false;
                    }

                    if (!refundReason || refundReason.trim() === '') {
                        layer.msg("请输入退款原因", {icon: 2});
                        return false;
                    }

                    console.log("发送退款请求:", prefix + "/refund/" + orderId);
                    console.log("退款参数:", {refundAmount: refundAmount, refundReason: refundReason});

                    $.post(prefix + "/refund/" + orderId, {
                        refundAmount: refundAmount,
                        refundReason: refundReason
                    }, function(result) {
                        console.log("退款响应:", result);
                        if (result.code == 0) {
                            layer.msg("退款成功", {icon: 1});
                            $.table.refresh();
                            layer.close(index);
                        } else {
                            layer.msg(result.msg, {icon: 2});
                        }
                    }).fail(function(xhr, status, error) {
                        console.log("退款请求失败:", xhr.status, xhr.statusText);
                        console.log("响应内容:", xhr.responseText);
                        if (xhr.status === 404) {
                            layer.msg("退款接口不存在，请检查权限配置", {icon: 2});
                        } else {
                            layer.msg("退款请求失败: " + xhr.statusText, {icon: 2});
                        }
                    });
                },
                btn2: function(index, layero) {
                    layer.close(index);
                }
            });
        }

        // 刷新订单状态
        function refreshOrderStatus() {
            var rows = $.table.selectColumns("orderId");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择至少一条记录");
                return;
            }

            var orderId = rows[0];
            refreshSingleOrderStatus(orderId);
        }

        // 刷新单个订单状态
        function refreshSingleOrderStatus(orderId) {
            $.modal.confirm("确认要刷新此订单的状态吗？将从微信支付查询最新状态并更新。", function() {
                $.modal.loading("正在刷新订单状态...");
                $.post(prefix + "/refresh/" + orderId, function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }).fail(function(xhr, status, error) {
                    $.modal.closeLoading();
                    $.modal.alertError("刷新订单状态失败: " + xhr.statusText);
                });
            });
        }

        // 根据订单ID获取表格中的订单数据
        function getOrderDataById(orderId) {
            var tableData = $("#bootstrap-table").bootstrapTable('getData');
            for (var i = 0; i < tableData.length; i++) {
                if (tableData[i].orderId == orderId) {
                    return tableData[i];
                }
            }
            return null;
        }
    </script>
</body>
</html>
