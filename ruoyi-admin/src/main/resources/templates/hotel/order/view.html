<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('订单详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>订单基本信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">订单号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.orderNo}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">订单状态：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span th:if="${hotelOrder.orderStatus == 'PENDING'}" class="label label-warning">待支付</span>
                                            <span th:if="${hotelOrder.orderStatus == 'PAID'}" class="label label-primary">已支付</span>
                                            <span th:if="${hotelOrder.orderStatus == 'CONFIRMED'}" class="label label-success">已确认</span>
                                            <span th:if="${hotelOrder.orderStatus == 'CANCELLED'}" class="label label-danger">已取消</span>
                                            <span th:if="${hotelOrder.orderStatus == 'REFUNDED'}" class="label label-info">已退款</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">支付状态：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span th:if="${hotelOrder.paymentStatus == 'UNPAID'}" class="label label-warning">未支付</span>
                                            <span th:if="${hotelOrder.paymentStatus == 'PAID'}" class="label label-success">已支付</span>
                                            <span th:if="${hotelOrder.paymentStatus == 'REFUNDED'}" class="label label-info">已退款</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">支付方式：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.paymentMethod ?: '未支付'}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">创建时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${#dates.format(hotelOrder.createTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">支付时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.paymentTime != null ? #dates.format(hotelOrder.paymentTime, 'yyyy-MM-dd HH:mm:ss') : '未支付'}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>入住信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">入住人姓名：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.guestName}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">联系电话：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.guestPhone}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">身份证号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.guestIdCard ?: '未填写'}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">特殊要求：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.specialRequirements ?: '无'}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>房间信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房间名称：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.roomName}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房间类型：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.roomType}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">入住日期：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${#dates.format(hotelOrder.checkinDate, 'yyyy-MM-dd')}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">退房日期：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${#dates.format(hotelOrder.checkoutDate, 'yyyy-MM-dd')}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">住宿天数：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.nights} + '晚'"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房间单价：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="'¥' + ${hotelOrder.roomPrice} + '/晚'"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>费用信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">订单总金额：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" style="color: #e96656; font-size: 18px; font-weight: bold;" th:text="'¥' + ${hotelOrder.totalAmount}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">押金金额：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="'¥' + ${hotelOrder.depositAmount ?: 0}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${hotelOrder.refundAmount != null}">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">退款金额：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="'¥' + ${hotelOrder.refundAmount}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">退款时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.refundTime != null ? #dates.format(hotelOrder.refundTime, 'yyyy-MM-dd HH:mm:ss') : ''}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ibox float-e-margins" th:if="${hotelOrder.transactionId != null}">
                    <div class="ibox-title">
                        <h5>支付信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">微信交易号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.transactionId}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">预支付ID：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.prepayId ?: ''}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ibox float-e-margins" th:if="${hotelOrder.cancelReason != null or hotelOrder.refundReason != null}">
                    <div class="ibox-title">
                        <h5>取消/退款信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row" th:if="${hotelOrder.cancelReason != null}">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">取消原因：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.cancelReason}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">取消时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.cancelTime != null ? #dates.format(hotelOrder.cancelTime, 'yyyy-MM-dd HH:mm:ss') : ''}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${hotelOrder.refundReason != null}">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">退款原因：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${hotelOrder.refundReason}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ibox float-e-margins" th:if="${hotelOrder.remark != null}">
                    <div class="ibox-title">
                        <h5>备注信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">备注：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static" th:text="${hotelOrder.remark}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
