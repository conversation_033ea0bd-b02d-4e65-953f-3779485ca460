<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('订单退款')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-order-refund" th:object="${hotelOrder}">
            <input name="orderId" th:field="*{orderId}" type="hidden">
            
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>订单信息</h5>
                </div>
                <div class="ibox-content">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">订单号：</label>
                        <div class="col-sm-8">
                            <input name="orderNo" th:field="*{orderNo}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">入住人姓名：</label>
                        <div class="col-sm-8">
                            <input name="guestName" th:field="*{guestName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">房间名称：</label>
                        <div class="col-sm-8">
                            <input name="roomName" th:field="*{roomName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">订单总金额：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <span class="input-group-addon">¥</span>
                                <input name="totalAmount" th:field="*{totalAmount}" class="form-control" type="text" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">当前状态：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static">
                                <span th:if="${hotelOrder.orderStatus == 'PAID'}" class="label label-primary">已支付</span>
                                <span th:if="${hotelOrder.orderStatus == 'CONFIRMED'}" class="label label-success">已确认</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>退款信息</h5>
                </div>
                <div class="ibox-content">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">退款金额：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <span class="input-group-addon">¥</span>
                                <input name="refundAmount" class="form-control" type="number" step="0.01" min="0.01" 
                                       th:max="${hotelOrder.totalAmount}" required placeholder="请输入退款金额">
                            </div>
                            <span class="help-block m-b-none">
                                <i class="fa fa-info-circle"></i> 退款金额不能超过订单总金额 ¥<span th:text="${hotelOrder.totalAmount}"></span>
                            </span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">退款原因：</label>
                        <div class="col-sm-8">
                            <textarea name="refundReason" class="form-control" rows="4" required 
                                      placeholder="请详细说明退款原因，此信息将记录在订单日志中"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label"></label>
                        <div class="col-sm-8">
                            <div class="alert alert-warning">
                                <i class="fa fa-warning"></i>
                                <strong>退款提醒：</strong>
                                <ul class="m-t-xs">
                                    <li>退款操作不可撤销，请谨慎操作</li>
                                    <li>退款后订单状态将变更为"已退款"</li>
                                    <li>实际退款到账时间取决于支付渠道处理速度</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-8 col-sm-offset-3">
                    <button class="btn btn-warning" type="submit">
                        <i class="fa fa-undo"></i> 确认退款
                    </button>
                    <button onclick="$.modal.closeHandler()" class="btn btn-default" type="button">取消</button>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "hotel/order";
        $("#form-order-refund").validate({
            focusCleanup: true,
            rules: {
                refundAmount: {
                    required: true,
                    min: 0.01,
                    max: parseFloat($("input[name='totalAmount']").val())
                },
                refundReason: {
                    required: true,
                    minlength: 5
                }
            },
            messages: {
                refundAmount: {
                    required: "请输入退款金额",
                    min: "退款金额必须大于0",
                    max: "退款金额不能超过订单总金额"
                },
                refundReason: {
                    required: "请输入退款原因",
                    minlength: "退款原因至少需要5个字符"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                var orderId = $("input[name='orderId']").val();
                var refundAmount = $("input[name='refundAmount']").val();
                var refundReason = $("textarea[name='refundReason']").val();
                
                $.modal.confirm("确认要退款 ¥" + refundAmount + " 吗？此操作不可撤销！", function() {
                    $.post(prefix + "/refund/" + orderId, {
                        refundAmount: refundAmount,
                        refundReason: refundReason
                    }, function(result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess("退款成功");
                            $.modal.closeHandler();
                            if (parent.$.table) {
                                parent.$.table.refresh();
                            }
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    });
                });
            }
        }

        $("form").submit(function(e) {
            e.preventDefault();
            submitHandler();
        });

        // 快速填充退款金额
        $("input[name='refundAmount']").after(
            '<div class="m-t-xs">' +
            '<button type="button" class="btn btn-xs btn-outline btn-default" onclick="setFullRefund()">全额退款</button> ' +
            '<button type="button" class="btn btn-xs btn-outline btn-default" onclick="setHalfRefund()">退款50%</button>' +
            '</div>'
        );

        function setFullRefund() {
            var totalAmount = $("input[name='totalAmount']").val();
            $("input[name='refundAmount']").val(totalAmount);
        }

        function setHalfRefund() {
            var totalAmount = parseFloat($("input[name='totalAmount']").val());
            var halfAmount = (totalAmount / 2).toFixed(2);
            $("input[name='refundAmount']").val(halfAmount);
        }
    </script>
</body>
</html>
