<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改订单')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-order-edit" th:object="${hotelOrder}">
            <input name="orderId" th:field="*{orderId}" type="hidden">
            
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>订单基本信息</h5>
                </div>
                <div class="ibox-content">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">订单号：</label>
                        <div class="col-sm-8">
                            <input name="orderNo" th:field="*{orderNo}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">入住人姓名：</label>
                        <div class="col-sm-8">
                            <input name="guestName" th:field="*{guestName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">联系电话：</label>
                        <div class="col-sm-8">
                            <input name="guestPhone" th:field="*{guestPhone}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">房间名称：</label>
                        <div class="col-sm-8">
                            <input name="roomName" th:field="*{roomName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">入住日期：</label>
                        <div class="col-sm-8">
                            <input name="checkinDate" th:value="${#dates.format(hotelOrder.checkinDate, 'yyyy-MM-dd')}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">退房日期：</label>
                        <div class="col-sm-8">
                            <input name="checkoutDate" th:value="${#dates.format(hotelOrder.checkoutDate, 'yyyy-MM-dd')}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">订单金额：</label>
                        <div class="col-sm-8">
                            <input name="totalAmount" th:field="*{totalAmount}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>状态管理</h5>
                </div>
                <div class="ibox-content">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">订单状态：</label>
                        <div class="col-sm-8">
                            <select name="orderStatus" class="form-control m-b" required>
                                <option value="PENDING" th:selected="${hotelOrder.orderStatus == 'PENDING'}">待支付</option>
                                <option value="PAID" th:selected="${hotelOrder.orderStatus == 'PAID'}">已支付</option>
                                <option value="CONFIRMED" th:selected="${hotelOrder.orderStatus == 'CONFIRMED'}">已确认</option>
                                <option value="CANCELLED" th:selected="${hotelOrder.orderStatus == 'CANCELLED'}">已取消</option>
                                <option value="REFUNDED" th:selected="${hotelOrder.orderStatus == 'REFUNDED'}">已退款</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">支付状态：</label>
                        <div class="col-sm-8">
                            <select name="paymentStatus" class="form-control m-b" required>
                                <option value="UNPAID" th:selected="${hotelOrder.paymentStatus == 'UNPAID'}">未支付</option>
                                <option value="PAID" th:selected="${hotelOrder.paymentStatus == 'PAID'}">已支付</option>
                                <option value="REFUNDED" th:selected="${hotelOrder.paymentStatus == 'REFUNDED'}">已退款</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">备注：</label>
                        <div class="col-sm-8">
                            <textarea name="remark" class="form-control" rows="4" th:text="${hotelOrder.remark}"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>当前状态信息</h5>
                </div>
                <div class="ibox-content">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">当前订单状态：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static">
                                <span th:if="${hotelOrder.orderStatus == 'PENDING'}" class="label label-warning">待支付</span>
                                <span th:if="${hotelOrder.orderStatus == 'PAID'}" class="label label-primary">已支付</span>
                                <span th:if="${hotelOrder.orderStatus == 'CONFIRMED'}" class="label label-success">已确认</span>
                                <span th:if="${hotelOrder.orderStatus == 'CANCELLED'}" class="label label-danger">已取消</span>
                                <span th:if="${hotelOrder.orderStatus == 'REFUNDED'}" class="label label-info">已退款</span>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">当前支付状态：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static">
                                <span th:if="${hotelOrder.paymentStatus == 'UNPAID'}" class="label label-warning">未支付</span>
                                <span th:if="${hotelOrder.paymentStatus == 'PAID'}" class="label label-success">已支付</span>
                                <span th:if="${hotelOrder.paymentStatus == 'REFUNDED'}" class="label label-info">已退款</span>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">创建时间：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${#dates.format(hotelOrder.createTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                        </div>
                    </div>
                    <div class="form-group" th:if="${hotelOrder.paymentTime != null}">
                        <label class="col-sm-3 control-label">支付时间：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${#dates.format(hotelOrder.paymentTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                        </div>
                    </div>
                    <div class="form-group" th:if="${hotelOrder.confirmTime != null}">
                        <label class="col-sm-3 control-label">确认时间：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${#dates.format(hotelOrder.confirmTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                        </div>
                    </div>
                    <div class="form-group" th:if="${hotelOrder.cancelTime != null}">
                        <label class="col-sm-3 control-label">取消时间：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${#dates.format(hotelOrder.cancelTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                        </div>
                    </div>
                    <div class="form-group" th:if="${hotelOrder.cancelReason != null}">
                        <label class="col-sm-3 control-label">取消原因：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${hotelOrder.cancelReason}"></p>
                        </div>
                    </div>
                    <div class="form-group" th:if="${hotelOrder.transactionId != null}">
                        <label class="col-sm-3 control-label">微信交易号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${hotelOrder.transactionId}"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" type="submit">提交</button>
                    <button onclick="$.modal.closeHandler()" class="btn btn-default" type="button">取消</button>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "hotel/order";
        $("#form-order-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-order-edit').serialize());
            }
        }

        $("form").submit(function(e) {
            e.preventDefault();
            submitHandler();
        });

        // 状态联动逻辑
        $('select[name="orderStatus"]').change(function() {
            var orderStatus = $(this).val();
            var paymentStatusSelect = $('select[name="paymentStatus"]');
            
            // 根据订单状态自动调整支付状态选项
            if (orderStatus === 'PENDING') {
                paymentStatusSelect.val('UNPAID');
            } else if (orderStatus === 'PAID' || orderStatus === 'CONFIRMED') {
                if (paymentStatusSelect.val() === 'UNPAID') {
                    paymentStatusSelect.val('PAID');
                }
            } else if (orderStatus === 'REFUNDED') {
                paymentStatusSelect.val('REFUNDED');
            }
        });

        $('select[name="paymentStatus"]').change(function() {
            var paymentStatus = $(this).val();
            var orderStatusSelect = $('select[name="orderStatus"]');
            
            // 根据支付状态自动调整订单状态选项
            if (paymentStatus === 'UNPAID') {
                if (orderStatusSelect.val() !== 'PENDING' && orderStatusSelect.val() !== 'CANCELLED') {
                    orderStatusSelect.val('PENDING');
                }
            } else if (paymentStatus === 'PAID') {
                if (orderStatusSelect.val() === 'PENDING') {
                    orderStatusSelect.val('PAID');
                }
            } else if (paymentStatus === 'REFUNDED') {
                orderStatusSelect.val('REFUNDED');
            }
        });
    </script>
</body>
</html>
