<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增识别码')" />
</head>
<body>
    <div class="main-content">
        <form class="form-horizontal" id="form-categoryCode-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">识别码：</label>
                <div class="col-sm-8">
                    <input name="categoryId" class="form-control" type="text" required maxlength="8" placeholder="请输入6-8位识别码">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">会议：</label>
                <div class="col-sm-8">
                    <select name="conference" class="form-control" required id="conferenceSelect">
                        <option value="">请选择会议</option>
                        <option th:each="conference : ${conferences}" th:value="${conference.id}" th:text="${conference.conferenceTitle}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">房型配置：</label>
                <div class="col-sm-8">
                    <div id="roomConfigContainer">
                        <p class="text-muted">请先选择会议</p>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">总房间数量：</label>
                <div class="col-sm-8">
                    <input name="roomCount" class="form-control" type="number" min="0" placeholder="总房间数量（自动计算）" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 根据各房型数量自动计算</span>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "hotel/categoryCode";
        var roomPrefix = ctx + "hotel/room";

        $("#form-categoryCode-add").validate({
            focusCleanup: true
        });

        $(document).ready(function() {
            // 绑定会议选择事件
            $("#conferenceSelect").change(function() {
                var conferenceId = $(this).val();
                var container = $("#roomConfigContainer");

                if (conferenceId) {
                    container.html('<p class="text-info">正在加载房型...</p>');

                    $.post(ctx + "hotel/room/listByConference", {conferenceId: conferenceId}, function(result) {
                        if (result.code == 200 || result.code == 0) {
                            if (result.data && result.data.length > 0) {
                                var html = '<div class="panel panel-default">' +
                                          '<div class="panel-heading">房型配置</div>' +
                                          '<div class="panel-body">';

                                $.each(result.data, function(index, room) {
                                    html += '<div class="row" style="margin-bottom: 10px;">' +
                                           '<div class="col-sm-4">' +
                                           '<div class="checkbox">' +
                                           '<label><input type="checkbox" name="roomIds" value="' + room.id + '" onchange="updateRoomConfig()"> ' + room.roomTitle + '</label>' +
                                           '</div>' +
                                           '</div>' +
                                           '<div class="col-sm-4">' +
                                           '<input type="number" class="form-control room-count" data-room-id="' + room.id + '" min="1" value="1" placeholder="数量" disabled onchange="updateTotalCount()">' +
                                           '</div>' +
                                           '</div>';
                                });

                                html += '</div></div>';
                                container.html(html);
                            } else {
                                container.html('<p class="text-muted">该会议暂无房型</p>');
                            }
                        } else {
                            container.html('<p class="text-muted text-danger">获取房型失败: ' + (result.msg || "未知错误") + '</p>');
                        }
                    }).fail(function(xhr, status, error) {
                        container.html('<p class="text-muted text-danger">加载房型失败，请重试</p>');
                    });
                } else {
                    container.html('<p class="text-muted">请先选择会议</p>');
                }
            });
        });



        // 更新房型配置
        function updateRoomConfig() {
            $('input[name="roomIds"]').each(function() {
                var roomId = $(this).val();
                var countInput = $('.room-count[data-room-id="' + roomId + '"]');
                if ($(this).is(':checked')) {
                    countInput.prop('disabled', false);
                } else {
                    countInput.prop('disabled', true);
                    countInput.val(1);
                }
            });
            updateTotalCount();
        }

        // 更新总数量
        function updateTotalCount() {
            var total = 0;
            $('.room-count:not(:disabled)').each(function() {
                var count = parseInt($(this).val()) || 0;
                total += count;
            });
            $('input[name="roomCount"]').val(total);
        }

        // 表单提交前处理数据
        function submitHandler() {
            if ($.validate.form()) {
                // 构建房型数量映射
                var roomCountMap = {};
                $('input[name="roomIds"]:checked').each(function() {
                    var roomId = $(this).val();
                    var count = parseInt($('.room-count[data-room-id="' + roomId + '"]').val()) || 1;
                    roomCountMap[roomId] = count;
                });

                // 添加房型数量映射到表单数据
                var formData = $('#form-categoryCode-add').serialize();
                formData += '&roomCountMapJson=' + encodeURIComponent(JSON.stringify(roomCountMap));

                $.operate.save(prefix + "/add", formData);
            }
        }

        $("#form-categoryCode-add").validate({
            focusCleanup: true
        });

        function loadRooms(conferenceId) {
            var roomSelect = $("#roomSelect");
            roomSelect.empty();
            
            if (conferenceId) {
                $.ajax({
                    url: roomPrefix + "/listByConference",
                    type: "post",
                    data: { conferenceId: conferenceId },
                    success: function(result) {
                        if (result.code == 0) {
                            roomSelect.append('<option value="">请选择房间</option>');
                            $.each(result.data, function(index, room) {
                                roomSelect.append('<option value="' + room.id + '">' + room.roomTitle + '</option>');
                            });
                        } else {
                            roomSelect.append('<option value="">加载房间失败</option>');
                        }
                    },
                    error: function() {
                        roomSelect.append('<option value="">加载房间失败</option>');
                    }
                });
            } else {
                roomSelect.append('<option value="">请先选择会议</option>');
            }
        }
    </script>
</body>
</html>
