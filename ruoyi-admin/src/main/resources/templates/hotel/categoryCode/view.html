<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('识别码详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-categoryCode-view" th:object="${categoryCode}">
            <div class="form-group">    
                <label class="col-sm-3 control-label">识别码：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{categoryId}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">会议名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{conferenceTitle}" readonly>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">关联房型：</label>
                <div class="col-sm-8">
                    <div class="form-control-static">
                        <span th:if="${categoryCode.roomList != null and !categoryCode.roomList.isEmpty()}">
                            <div th:each="room,iterStat : ${categoryCode.roomList}" class="room-item" style="margin-bottom: 5px;">
                                <span class="label label-info" th:text="${room.roomTitle + ' (' + (room.roomCount != null ? room.roomCount : 1) + '间)'}"></span>
                            </div>
                        </span>
                        <span th:if="${categoryCode.roomList == null or categoryCode.roomList.isEmpty()}">无</span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间数量：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{roomCount}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(categoryCode.createTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">更新时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(categoryCode.updateTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
