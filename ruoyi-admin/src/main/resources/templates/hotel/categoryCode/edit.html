<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改识别码')" />
</head>
<body>
    <div class="main-content">
        <form class="form-horizontal" id="form-categoryCode-edit" th:object="${categoryCode}">
            <input name="categoryId" th:field="*{categoryId}" type="hidden">
            <input name="conference" th:field="*{conference}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">识别码：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="*{categoryId}" readonly>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">会议：</label>
                <div class="col-sm-8">
                    <select class="form-control" disabled>
                        <option th:value="*{conference}" th:text="*{conferenceTitle}" selected></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">房型配置：</label>
                <div class="col-sm-8">
                    <div id="roomConfigContainer">
                        <div class="panel panel-default">
                            <div class="panel-heading">房型配置</div>
                            <div class="panel-body">
                                <div th:each="room : ${rooms}" class="row" style="margin-bottom: 10px;">
                                    <div class="col-sm-3">
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="roomIds" th:value="${room.id}" th:text="${room.roomTitle}" onchange="updateRoomConfig()">
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <input type="number" class="form-control room-count" th:data-room-id="${room.id}" min="1" value="1" placeholder="数量" disabled onchange="updateTotalCount()">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">总房间数量：</label>
                <div class="col-sm-8">
                    <input name="roomCount" th:field="*{roomCount}" class="form-control" type="number" min="0" placeholder="总房间数量（自动计算）" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 根据各房型数量自动计算</span>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "hotel/categoryCode";

        $("#form-categoryCode-edit").validate({
            focusCleanup: true
        });

        // 更新房型配置
        function updateRoomConfig() {
            $('input[name="roomIds"]').each(function() {
                var roomId = $(this).val();
                var countInput = $('.room-count[data-room-id="' + roomId + '"]');
                if ($(this).is(':checked')) {
                    countInput.prop('disabled', false);
                } else {
                    countInput.prop('disabled', true);
                    countInput.val(1);
                }
            });
            updateTotalCount();
        }

        // 更新总数量
        function updateTotalCount() {
            var total = 0;
            $('.room-count:not(:disabled)').each(function() {
                var count = parseInt($(this).val()) || 0;
                total += count;
            });
            $('input[name="roomCount"]').val(total);
        }

        function submitHandler() {
            if ($.validate.form()) {
                // 构建房型数量映射
                var roomCountMap = {};
                $('input[name="roomIds"]:checked').each(function() {
                    var roomId = $(this).val();
                    var count = parseInt($('.room-count[data-room-id="' + roomId + '"]').val()) || 1;
                    roomCountMap[roomId] = count;
                });

                // 添加房型数量映射到表单数据
                var formData = $('#form-categoryCode-edit').serialize();
                formData += '&roomCountMapJson=' + encodeURIComponent(JSON.stringify(roomCountMap));

                $.operate.save(prefix + "/edit", formData);
            }
        }

        // 页面加载时设置已选中的房型和数量
        $(function() {
            // 设置已选中的房型
            /*[# th:each="roomRelation : ${categoryCode.roomList}"]*/
                var roomId = /*[[${roomRelation.id}]]*/ 0;
                var roomCount = /*[[${roomRelation.roomCount}]]*/ 1;

                $('input[name="roomIds"][value="' + roomId + '"]').prop('checked', true);
                $('.room-count[data-room-id="' + roomId + '"]').prop('disabled', false).val(roomCount);
            /*[/]*/

            updateTotalCount();
        });
    </script>
</body>
</html>
