<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('会议列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>会议名称：</label>
                                <input type="text" name="conferenceTitle"/>
                            </li>
                            <li>
                                <label>酒店名称：</label>
                                <input type="text" name="hotelName"/>
                            </li>
                            <li>
                                <label>是否启用：</label>
                                <select name="enable" th:with="type=${@dict.getType('sys_yes_no')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="hotel:conference:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="hotel:conference:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="hotel:conference:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="hotel:conference:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('hotel:conference:edit')}]];
        var removeFlag = [[${@permission.hasPermi('hotel:conference:remove')}]];
        var prefix = ctx + "hotel/conference";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "会议",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '会议ID',
                    visible: false
                },
                {
                    field: 'conferenceTitle',
                    title: '会议名称'
                },
                {
                    field: 'startTime',
                    title: '开始时间',
                    sortable: true
                },
                {
                    field: 'endTime',
                    title: '结束时间',
                    sortable: true
                },
                {
                    field: 'address',
                    title: '会议地址'
                },
                {
                    field: 'hotelName',
                    title: '酒店名称'
                },
                {
                    field: 'participantCount',
                    title: '参会人数'
                },
                {
                    field: 'enable',
                    title: '是否启用',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel([[${@dict.getType('sys_yes_no')}]], value);
                    }
                },
                {
                    field: 'operator',
                    title: '操作人'
                },
                {
                    field: 'photoPath',
                    title: '会议照片',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<img src="' + value + '" style="width: 60px; height: 40px; object-fit: cover; cursor: pointer;" class="img-responsive" onclick="$.modal.imageView(\'' + value + '\')">';
                        } else {
                            return '<span class="text-muted">无图片</span>';
                        }
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.view(\'' + row.id + '\')"><i class="fa fa-search"></i>查看</a> ');
                        if (editFlag) {
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        }
                        if (removeFlag) {
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
