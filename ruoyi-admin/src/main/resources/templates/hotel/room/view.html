<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('房型详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-room-view" th:object="${room}">
            <div class="form-group">    
                <label class="col-sm-3 control-label">会议名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{conferenceTitle}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{roomTitle}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间图片：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{roomImgUrl}" readonly>
                    <div th:if="${room.roomImgUrl}" style="margin-top: 10px;">
                        <img th:src="${room.roomImgUrl}" style="max-width: 200px; max-height: 200px;" />
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">操作人：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{operator}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(room.createTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">更新时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(room.updateTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
