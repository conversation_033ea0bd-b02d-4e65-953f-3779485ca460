<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改参数')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-config-edit" th:object="${config}">
            <input id="configId" name="configId" th:field="*{configId}"  type="hidden">
            <div class="form-group">	
                <label class="col-sm-3 control-label is-required">参数名称：</label>
                <div class="col-sm-8">
                    <input id="configName" name="configName" th:field="*{configName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">	
                <label class="col-sm-3 control-label is-required">参数键名：</label>
                <div class="col-sm-8">
                    <input id="configKey" name="configKey" th:field="*{configKey}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">	
                <label class="col-sm-3 control-label is-required">参数键值：</label>
                <div class="col-sm-8">
                    <textarea id="configValue" name="configValue" class="form-control" rows="4" required>[[*{configValue}]]</textarea>
                </div>
            </div>
			<div class="form-group">
				<label class="col-sm-3 control-label">系统内置：</label>
				<div class="col-sm-8">
				    <div class="radio-box" th:each="dict : ${@dict.getType('sys_yes_no')}">
						<input type="radio" th:id="${dict.dictCode}" name="configType" th:value="${dict.dictValue}" th:field="*{configType}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
			<div class="form-group">	
	            <label class="col-sm-3 control-label">备注：</label>
	            <div class="col-sm-8">
	                <textarea id="remark" name="remark" class="form-control">[[*{remark}]]</textarea>
	            </div>
	        </div>
    	</form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
	    var prefix = ctx + "system/config";
	    
	    $("#form-config-edit").validate({
	    	onkeyup: false,
	        rules: {
	            configKey: {
	                remote: {
	                    url: prefix + "/checkConfigKeyUnique",
	                    type: "post",
	                    dataType: "json",
	                    data: {
	                        "configId": function() {
	                        	return $("#configId").val();
	                        },
	                        "configKey": function() {
	                        	return $.common.trim($("#configKey").val());
	                        }
	                    }
	                }
	            },
	        },
	        messages: {
	            "configKey": {
	                remote: "参数键名已经存在"
	            }
	        },
	        focusCleanup: true
	    });
	    
	    function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/edit", $('#form-config-edit').serialize());
	        }
	    }
    </script>
</body>
</html>
