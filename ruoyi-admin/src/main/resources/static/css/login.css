html {
	height:100%
}
body.signin {
	height:auto;
	background:url(../img/login-background.jpg) no-repeat center fixed;
	-webkit-background-size:cover;
	-moz-background-size:cover;
	-o-background-size:cover;
	background-size:cover
}
.signinpanel {
	width:750px;
	margin:10% auto 0;
	color:rgba(255,255,255,.95)
}
.signinpanel .logopanel {
	float:none;
	width:auto;
	padding:0;
	background:0 0
}
.signinpanel .signin-info ul {
	list-style:none;
	padding:0;
	margin:20px 0
}
.signinpanel .form-control {
	display:block;
	margin-top:15px
}
.signinpanel .uname {
	background:#fff url(../img/user.png) no-repeat 95% center;
	color:#333
}
.signinpanel .pword {
	background:#fff url(../img/locked.png) no-repeat 95% center;
	color:#333
}
.signinpanel .code {
    background: #fff no-repeat 95% center;color:#333; margin:0 0 15px 0;
}
.signinpanel .btn {
	margin-top:15px
}
.signinpanel form {
	background:rgba(255,255,255,.2);
	border:1px solid rgba(255,255,255,.3);
	-moz-box-shadow:0 3px 0 rgba(12,12,12,.03);
	-webkit-box-shadow:0 3px 0 rgba(12,12,12,.03);
	box-shadow:0 3px 0 rgba(12,12,12,.03);
	-moz-border-radius:3px;
	-webkit-border-radius:3px;
	border-radius:3px;
	padding:30px
}
.signup-footer {
	border-top:solid 1px rgba(255,255,255,.3);
	margin:20px 0;
	padding-top:15px
}
@media screen and (max-width:768px) {
	.signinpanel,.signuppanel {
	margin:0 auto;
	width:380px!important;
	padding:20px
}
.signinpanel form {
	margin-top:20px
}
.signup-footer,.signuppanel .form-control {
	margin-bottom:10px
}
.signup-footer .pull-left,.signup-footer .pull-right {
	float:none!important;
	text-align:center
}
.signinpanel .signin-info ul {
	display:none
}
}@media screen and (max-width:320px) {
	.signinpanel,.signuppanel {
	margin:0 20px;
	width:auto
}
}
/*
登录界面check样式
*/
.checkbox-custom {
    position: relative;
    padding: 0 15px 0 25px;
    margin-bottom: 7px;
    display: inline-block;
}
/*
将初始的checkbox的样式改变
*/
.checkbox-custom input[type="checkbox"] {
    opacity: 0; /*将初始的checkbox隐藏起来*/
    position: absolute;
    cursor: pointer;
    z-index: 2;
    margin: -6px 0 0 0;
    top: 50%;
    left: 3px;
}
/*
设计新的checkbox，位置
*/
.checkbox-custom label:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -9px;
    width: 18px;
    height: 17px;
    display: inline-block;
    border-radius: 2px;
    border: 1px solid #bbb;
    background: #fff;
}
/*
点击初始的checkbox，将新的checkbox关联起来
*/
.checkbox-custom input[type="checkbox"]:checked +label:after {
    position: absolute;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    content: "\e013";
    top: 42%;
    left: 3px;
    margin-top: -5px;
    font-size: 11px;
    line-height: 1;
    width: 16px;
    height: 16px;
    color: #333;
}
.checkbox-custom label {
    cursor: pointer;
    line-height: 1.2;
    font-weight: normal; /*改变了rememberme的字体*/
    margin-bottom: 0;
    text-align: left;
}

.form-control, .form-control:focus, .has-error .form-control:focus,  .has-success .form-control:focus, .has-warning .form-control:focus,  .navbar-collapse, .navbar-form, .navbar-form-custom .form-control:focus,  .navbar-form-custom .form-control:hover, .open .btn.dropdown-toggle,  .panel, .popover, .progress, .progress-bar {
    box-shadow: none;
}

.form-control {
    border-radius: 1px!important;
    padding: 6px 12px!important;
    height: 34px!important;
}

.form-control:focus {
    border-color: #1ab394 !important;
}

body .layer-ext-moon-msg[type="dialog"]{
    min-width: 100px !important;
}
body .layer-ext-moon-msg { 
    background-color: rgba(0,0,0,0.6);
    color: #fff;
    border: none;
}

body .layer-ext-moon-msg .layui-layer-content{ 
    padding: 12px 25px;
    text-align: center;
}