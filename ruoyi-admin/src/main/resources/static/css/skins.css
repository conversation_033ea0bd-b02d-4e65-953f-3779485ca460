/*
 *
 *   SKIN blue 若依管理系统
 *   NAME - blue/green/purple/red/yellow
 *
*/
.nav-tabs {
    border-bottom: 0px;
}

.navbar .navbar-toolbar>li>a {
    border: none !important;
}

/** 蓝色主题 skin-blue **/
.navbar, .skin-blue .navbar {
	background-color: #3c8dbc
}

.skin-blue .navbar-default .nav>li.selected>a,
.skin-blue .navbar-default .nav>li.selected>a:focus {
	background-color: #1890ff;
    color: rgba(255,255,255,1);
}

.skin-blue .navbar .nav>li>a {
	color: #fff
}

.skin-blue .navbar .nav>li>a:hover,
.skin-blue .navbar .nav>li>a:active,
.skin-blue .navbar .nav>li>a:focus,
.skin-blue .navbar .nav .open>a,
.skin-blue .navbar .nav .open>a:hover,
.skin-blue .navbar .nav .open>a:focus,
.skin-blue .navbar .nav>.active>a {
	background: rgba(0, 0, 0, 0.1);
	color: #f6f6f6
}

.skin-blue .navbar .sidebar-toggle {
	color: #fff
}

.skin-blue .navbar .sidebar-toggle:hover {
	color: #f6f6f6;
	background: rgba(0, 0, 0, 0.1)
}

.skin-blue .navbar .sidebar-toggle {
	color: #fff
}

.skin-blue .navbar .sidebar-toggle:hover {
	background-color: #367fa9
}

@media ( max-width :767px) {
	.skin-blue .navbar .dropdown-menu li.divider {
		background-color: rgba(255, 255, 255, 0.1)
	}
	.skin-blue .navbar .dropdown-menu li a {
		color: #fff
	}
	.skin-blue .navbar .dropdown-menu li a:hover {
		background: #367fa9
	}
}

.skin-blue .logo {
	background-color: #367fa9;
	color: #fff;
	border-bottom: 0 solid transparent
}

.skin-blue .logo:hover {
	background-color: #357ca5
}

.skin-blue li.user-header {
	background-color: #3c8dbc
}

.skin-blue .content-header {
	background: transparent
}

.skin-blue .wrapper,
.skin-blue .main-sidebar,
.skin-blue .left-side {
	background-color: #222d32
}

.skin-blue .user-panel>.info,
.skin-blue .user-panel>.info>a {
	color: #fff
}

.skin-blue .nav>li.header {
	color: #4b646f;
	background: #1a2226
}

.skin-blue .nav:not(.navbar-toolbar)>li.active {
	color: #fff;
	background: #293846;
	border-left: 3px solid #3c8dbc;
}

.skin-blue .nav>li.active>ul li.active {
	border-left: none;
}

.skin-blue .dropdown-menu > .active > a, .skin-blue .dropdown-menu > .active > a:hover, .skin-blue .dropdown-menu > .active > a:focus {
    color: #fff !important;
    text-decoration: none;
    outline: 0;
    background-color: #3c8dbc;
}

.skin-blue .sidebar a {
	color: #b8c7ce
}

.skin-blue .sidebar a:hover {
	text-decoration: none
}

.skin-blue .sidebar-form {
	border-radius: 3px;
	border: 1px solid #374850;
	margin: 10px 10px
}

.skin-blue .sidebar-form input[type="text"],
.skin-blue .sidebar-form .btn {
	box-shadow: none;
	background-color: #374850;
	border: 1px solid transparent;
	height: 35px
}

.skin-blue .sidebar-form input[type="text"] {
	color: #666;
	border-top-left-radius: 2px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 2px
}

.skin-blue .sidebar-form input[type="text"]:focus,
.skin-blue .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	background-color: #fff;
	color: #666
}

.skin-blue .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	border-left-color: #fff
}

.skin-blue .sidebar-form .btn {
	color: #999;
	border-top-left-radius: 0;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 0
}

.skin-blue.layout-top-nav>.logo {
	background-color: #3c8dbc;
	color: #fff;
	border-bottom: 0 solid transparent
}

.skin-blue.layout-top-nav>.logo:hover {
	background-color: #3b8ab8
}

.skin-blue .content-tabs {
	border-bottom: 1px solid #e5e5e5;
}

.skin-blue.layout-top-nav>.logo {
	background-color: #3c8dbc;
	color: #fff;
	border-bottom: 0 solid transparent
}

.skin-blue.layout-top-nav>.logo:hover {
	background-color: #3b8ab8
}

/** 绿色主题 skin-green **/
.skin-green .navbar {
	background-color: #00a65a;
}

.skin-green .navbar-default .nav>li.selected>a,
.skin-green .navbar-default .nav>li.selected>a:focus {
	background-color: #52c41a;
    color: rgba(255,255,255,1);
}

.skin-green .content-tabs {
	border-bottom: 1px solid #e5e5e5;
}

.skin-green .navbar .nav>li>a {
	color: #fff
}

.skin-green .navbar .nav>li>a:hover,
.skin-green .navbar .nav>li>a:active,
.skin-green .navbar .nav>li>a:focus,
.skin-green .navbar .nav .open>a,
.skin-green .navbar .nav .open>a:hover,
.skin-green .navbar .nav .open>a:focus,
.skin-green .navbar .nav>.active>a {
	background: rgba(0, 0, 0, 0.1);
	color: #f6f6f6
}

.skin-green .navbar .sidebar-toggle {
	color: #fff
}

.skin-green .navbar .sidebar-toggle:hover {
	color: #f6f6f6;
	background: rgba(0, 0, 0, 0.1)
}

.skin-green .navbar .sidebar-toggle {
	color: #fff
}

.skin-green .navbar .sidebar-toggle:hover {
	background-color: #008d4c
}

@media ( max-width :767px) {
	.skin-green .navbar .dropdown-menu li.divider {
		background-color: rgba(255, 255, 255, 0.1)
	}
	.skin-green .navbar .dropdown-menu li a {
		color: #fff
	}
	.skin-green .navbar .dropdown-menu li a:hover {
		background: #008d4c
	}
}

.skin-green .logo {
	background-color: #008d4c;
	color: #fff;
	border-bottom: 0 solid transparent
}

.skin-green .logo:hover {
	background-color: #008749
}

.skin-green li.user-header {
	background-color: #00a65a
}

.skin-green .content-header {
	background: transparent
}

.skin-green .wrapper,
.skin-green .main-sidebar,
.skin-green .left-side {
	background-color: #222d32
}

.skin-green .user-panel>.info,
.skin-green .user-panel>.info>a {
	color: #fff
}

.skin-green .nav>li.header {
	color: #4b646f;
	background: #1a2226;
}

.skin-green .nav:not(.navbar-toolbar)>li.active {
	color: #fff;
	background: #293846;
	border-left: 3px solid #00a65a;
}

.skin-green .nav>li.active>ul li.active {
	border-left: none;
}

.skin-green .dropdown-menu > .active > a, .skin-green .dropdown-menu > .active > a:hover, .skin-green .dropdown-menu > .active > a:focus {
    color: #fff !important;
    text-decoration: none;
    outline: 0;
    background-color: #00a65a;
}

.skin-green .sidebar a {
	color: #b8c7ce
}

.skin-green .sidebar a:hover {
	text-decoration: none
}

.skin-green .sidebar-form {
	border-radius: 3px;
	border: 1px solid #374850;
	margin: 10px 10px
}

.skin-green .sidebar-form input[type="text"],
.skin-green .sidebar-form .btn {
	box-shadow: none;
	background-color: #374850;
	border: 1px solid transparent;
	height: 35px
}

.skin-green .sidebar-form input[type="text"] {
	color: #666;
	border-top-left-radius: 2px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 2px
}

.skin-green .sidebar-form input[type="text"]:focus,
.skin-green .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	background-color: #fff;
	color: #666
}

.skin-green .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	border-left-color: #fff
}

.skin-green .sidebar-form .btn {
	color: #999;
	border-top-left-radius: 0;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 0
}

/** 红色主题 skin-red **/
.skin-red .navbar {
	background-color: #dd4b39
}

.skin-red .navbar-default .nav>li.selected>a,
.skin-red .navbar-default .nav>li.selected>a:focus {
	background-color: #f5222d;
    color: rgba(255,255,255,1);
}

.skin-red .navbar .nav>li>a {
	color: #fff
}

.skin-red .navbar .nav>li>a:hover,
.skin-red .navbar .nav>li>a:active,
.skin-red .navbar .nav>li>a:focus,
.skin-red .navbar .nav .open>a,
.skin-red .navbar .nav .open>a:hover,
.skin-red .navbar .nav .open>a:focus,
.skin-red .navbar .nav>.active>a {
	background: rgba(0, 0, 0, 0.1);
	color: #f6f6f6
}

.skin-red .navbar .sidebar-toggle {
	color: #fff
}

.skin-red .navbar .sidebar-toggle:hover {
	color: #f6f6f6;
	background: rgba(0, 0, 0, 0.1)
}

.skin-red .navbar .sidebar-toggle {
	color: #fff
}

.skin-red .navbar .sidebar-toggle:hover {
	background-color: #d73925
}

@media ( max-width :767px) {
	.skin-red .navbar .dropdown-menu li.divider {
		background-color: rgba(255, 255, 255, 0.1)
	}
	.skin-red .navbar .dropdown-menu li a {
		color: #fff
	}
	.skin-red .navbar .dropdown-menu li a:hover {
		background: #d73925
	}
}

.skin-red .logo {
	background-color: #d73925;
	color: #fff;
	border-bottom: 0 solid transparent
}

.skin-red .logo:hover {
	background-color: #d33724
}

.skin-red li.user-header {
	background-color: #dd4b39
}

.skin-red .content-header {
	background: transparent
}

.skin-red .wrapper,
.skin-red .main-sidebar,
.skin-red .left-side {
	background-color: #222d32
}

.skin-red .user-panel>.info,
.skin-red .user-panel>.info>a {
	color: #fff
}

.skin-red .nav>li.header {
	color: #4b646f;
	background: #1a2226
}

.skin-red .nav:not(.navbar-toolbar)>li.active {
	color: #fff;
	border-left: 3px solid #dd4b39;
	background: #293846;
}

.skin-red .nav>li.active>ul li.active {
	border-left: none;
}

.skin-red .dropdown-menu > .active > a, .skin-red .dropdown-menu > .active > a:hover, .skin-red .dropdown-menu > .active > a:focus {
    color: #fff !important;
    text-decoration: none;
    outline: 0;
    background-color: #dd4b39;
}

.skin-red .content-tabs {
	border-bottom: 1px solid #e5e5e5;
}

.skin-red .sidebar a {
	color: #b8c7ce
}

.skin-red .sidebar a:hover {
	text-decoration: none
}

.skin-red .sidebar-form {
	border-radius: 3px;
	border: 1px solid #374850;
	margin: 10px 10px
}

.skin-red .sidebar-form input[type="text"],
.skin-red .sidebar-form .btn {
	box-shadow: none;
	background-color: #374850;
	border: 1px solid transparent;
	height: 35px
}

.skin-red .sidebar-form input[type="text"] {
	color: #fff;
	border-top-left-radius: 2px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 2px
}

.skin-red .sidebar-form input[type="text"]:focus,
.skin-red .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	background-color: #fff;
	color: #666
}

.skin-red .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	border-left-color: #fff
}

.skin-red .sidebar-form .btn {
	color: #999;
	border-top-left-radius: 0;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 0
}

/** 黄色主题 skin-red **/
.skin-yellow .navbar {
	background-color: #f39c12
}

.skin-yellow .navbar-default .nav>li.selected>a,
.skin-yellow .navbar-default .nav>li.selected>a:focus {
	background-color: #faad14;
    color: rgba(255,255,255,1);
}

.skin-yellow .navbar .nav>li>a {
	color: #fff
}

.skin-yellow .navbar .nav>li>a:hover,
.skin-yellow .navbar .nav>li>a:active,
.skin-yellow .navbar .nav>li>a:focus,
.skin-yellow .navbar .nav .open>a,
.skin-yellow .navbar .nav .open>a:hover,
.skin-yellow .navbar .nav .open>a:focus,
.skin-yellow .navbar .nav>.active>a {
	background: rgba(0, 0, 0, 0.1);
	color: #f6f6f6
}

.skin-yellow .navbar .sidebar-toggle {
	color: #fff
}

.skin-yellow .navbar .sidebar-toggle:hover {
	color: #f6f6f6;
	background: rgba(0, 0, 0, 0.1)
}

.skin-yellow .navbar .sidebar-toggle {
	color: #fff
}

.skin-yellow .navbar .sidebar-toggle:hover {
	background-color: #e08e0b
}

@media ( max-width :767px) {
	.skin-yellow .navbar .dropdown-menu li.divider {
		background-color: rgba(255, 255, 255, 0.1)
	}
	.skin-yellow .navbar .dropdown-menu li a {
		color: #fff
	}
	.skin-yellow .navbar .dropdown-menu li a:hover {
		background: #e08e0b
	}
}

.skin-yellow .logo {
	background-color: #e08e0b;
	color: #fff;
	border-bottom: 0 solid transparent
}

.skin-yellow .logo:hover {
	background-color: #db8b0b
}

.skin-yellow li.user-header {
	background-color: #f39c12
}

.skin-yellow .content-header {
	background: transparent
}

.skin-yellow .wrapper,
.skin-yellow .main-sidebar,
.skin-yellow .left-side {
	background-color: #222d32
}

.skin-yellow .user-panel>.info,
.skin-yellow .user-panel>.info>a {
	color: #fff
}

.skin-yellow .nav>li.header {
	color: #4b646f;
	background: #1a2226
}

.skin-yellow .nav:not(.navbar-toolbar)>li.active {
	color: #fff;
	background: #293846;
	border-left: 3px solid #f39c12;
}

.skin-yellow .nav>li.active>ul li.active {
	border-left: none;
}

.skin-yellow .dropdown-menu > .active > a, .skin-yellow .dropdown-menu > .active > a:hover, .skin-yellow .dropdown-menu > .active > a:focus {
    color: #fff !important;
    text-decoration: none;
    outline: 0;
    background-color: #f39c12;
}

.skin-yellow .content-tabs {
	 border-bottom: 1px solid #e5e5e5;
}

.skin-yellow .sidebar a {
	color: #b8c7ce
}

.skin-yellow .sidebar a:hover {
	text-decoration: none
}

.skin-yellow .sidebar-form {
	border-radius: 3px;
	border: 1px solid #374850;
	margin: 10px 10px
}

.skin-yellow .sidebar-form input[type="text"],
.skin-yellow .sidebar-form .btn {
	box-shadow: none;
	background-color: #374850;
	border: 1px solid transparent;
	height: 35px
}

.skin-yellow .sidebar-form input[type="text"] {
	color: #666;
	border-top-left-radius: 2px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 2px
}

.skin-yellow .sidebar-form input[type="text"]:focus,
.skin-yellow .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	background-color: #fff;
	color: #666
}

.skin-yellow .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	border-left-color: #fff
}

.skin-yellow .sidebar-form .btn {
	color: #999;
	border-top-left-radius: 0;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 0
}

/** 紫色主题 skin-purple **/
.skin-purple .navbar {
	background-color: #605ca8
}

.skin-purple .navbar-default .nav>li.selected>a,
.skin-purple .navbar-default .nav>li.selected>a:focus {
	background-color: #722ed1;
    color: rgba(255,255,255,1);
}

.skin-purple .navbar .nav>li>a {
	color: #fff
}

.skin-purple .navbar .nav>li>a:hover,
.skin-purple .navbar .nav>li>a:active,
.skin-purple .navbar .nav>li>a:focus,
.skin-purple .navbar .nav .open>a,
.skin-purple .navbar .nav .open>a:hover,
.skin-purple .navbar .nav .open>a:focus,
.skin-purple .navbar .nav>.active>a {
	background: rgba(0, 0, 0, 0.1);
	color: #f6f6f6
}

.skin-purple .navbar .sidebar-toggle {
	color: #fff
}

.skin-purple .navbar .sidebar-toggle:hover {
	color: #f6f6f6;
	background: rgba(0, 0, 0, 0.1)
}

.skin-purple .navbar .sidebar-toggle {
	color: #fff
}

.skin-purple .navbar .sidebar-toggle:hover {
	background-color: #555299
}

@media ( max-width :767px) {
	.skin-purple .navbar .dropdown-menu li.divider {
		background-color: rgba(255, 255, 255, 0.1)
	}
	.skin-purple .navbar .dropdown-menu li a {
		color: #fff
	}
	.skin-purple .navbar .dropdown-menu li a:hover {
		background: #555299
	}
}

.skin-purple .logo {
	background-color: #555299;
	color: #fff;
	border-bottom: 0 solid transparent
}

.skin-purple .logo:hover {
	background-color: #545096
}

.skin-purple li.user-header {
	background-color: #605ca8
}

.skin-purple .content-header {
	background: transparent
}

.skin-purple .wrapper,
.skin-purple .main-sidebar,
.skin-purple .left-side {
	background-color: #222d32
}

.skin-purple .user-panel>.info,
.skin-purple .user-panel>.info>a {
	color: #fff
}

.skin-purple .nav>li.header {
	color: #4b646f;
	background: #1a2226
}

.skin-purple .nav:not(.navbar-toolbar)>li.active {
	color: #fff;
	background: #293846;
	border-left: 3px solid #605ca8;
}

.skin-purple .nav>li.active>ul li.active {
	border-left: none;
}

.skin-purple .dropdown-menu > .active > a, .skin-purple .dropdown-menu > .active > a:hover, .skin-purple .dropdown-menu > .active > a:focus {
    color: #fff !important;
    text-decoration: none;
    outline: 0;
    background-color: #605ca8;
}

.skin-purple .content-tabs {
	 border-bottom: 1px solid #e5e5e5;
}

.skin-purple .sidebar a {
	color: #b8c7ce
}

.skin-purple .sidebar a:hover {
	text-decoration: none
}

.skin-purple .sidebar-form {
	border-radius: 3px;
	border: 1px solid #374850;
	margin: 10px 10px
}

.skin-purple .sidebar-form input[type="text"],
.skin-purple .sidebar-form .btn {
	box-shadow: none;
	background-color: #374850;
	border: 1px solid transparent;
	height: 35px
}

.skin-purple .sidebar-form input[type="text"] {
	color: #666;
	border-top-left-radius: 2px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 2px
}

.skin-purple .sidebar-form input[type="text"]:focus,
.skin-purple .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	background-color: #fff;
	color: #666
}

.skin-purple .sidebar-form input[type="text"]:focus+.input-group-btn .btn {
	border-left-color: #fff
}

.skin-purple .sidebar-form .btn {
	color: #999;
	border-top-left-radius: 0;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border-bottom-left-radius: 0
}

/*
 *
 *   侧边栏主题 若依管理系统
 *   NAME - .theme-dark/theme-light
 *
*/
/** 深黑主题 .theme-dark **/
.theme-dark .user-panel>.info>p, .theme-dark .user-panel>.info, .theme-dark .user-panel>.info>a{
	color: #fff
}

.theme-dark .nav:not(.navbar-toolbar)>li.active {
	background: #293846;
}

.theme-dark .navbar-static-side {
	background-color: #2f4050;
}

.theme-dark .user-panel {
	background-color: #2f4050;
}

.theme-dark .navbar-default .nav>li>a:hover,
.theme-dark .navbar-default .nav>li>a:focus {
	background: #293846;
}

/** 浅色主题 theme-light **/
.theme-light .user-panel>.info>p, .theme-light .user-panel>.info, .theme-light .user-panel>.info>a{
	color: #555
}

.theme-light .nav:not(.navbar-toolbar)>li.active{
	background: #f9fafc;
}

.theme-light .navbar-static-side {
	background-color: #f9fafc;
	box-shadow: 2px 0 2px 0 rgba(29,35,41,.05);
}

.theme-light .user-panel {
	background-color: #f9fafc;
}

.theme-light .navbar-default .nav>li>a {
	color: #777;
}

.theme-light.skin-blue .navbar-default .nav>li.active>a {
	color: #1890ff;
}

.theme-light.skin-blue .navbar-default .nav>li.selected>a,
.theme-light.skin-blue .navbar-default .nav>li.selected>a:focus {
	background-color: rgb(240, 245, 255);
	color: rgb(47, 84, 235);
}

.theme-light.skin-green .navbar-default .nav>li.active>a {
	color: #52c41a;
}

.theme-light.skin-green .navbar-default .nav>li.selected>a,
.theme-light.skin-green .navbar-default .nav>li.selected>a:focus {
	background-color: rgb(246, 255, 237);
	color: rgb(82, 196, 26);
}

.theme-light.skin-purple .navbar-default .nav>li.active>a {
	color: rgb(114, 46, 209);
}

.theme-light.skin-purple .navbar-default .nav>li.selected>a,
.theme-light.skin-purple .navbar-default .nav>li.selected>a:focus {
	background-color: #f9f0ff;
	color: #722ed1;
}

.theme-light.skin-red .navbar-default .nav>li.active>a {
	color: rgb(245, 34, 45);
}

.theme-light.skin-red .navbar-default .nav>li.selected>a,
.theme-light.skin-red .navbar-default .nav>li.selected>a:focus {
	background-color: rgb(255, 241, 240);
	color: rgb(245, 34, 45);
}

.theme-light.skin-yellow .navbar-default .nav>li.active>a {
	color: rgb(250, 173, 20);
}

.theme-light.skin-yellow .navbar-default .nav>li.selected>a,
.theme-light.skin-yellow .navbar-default .nav>li.selected>a:focus {
	background-color: #fffbe6;
	color: #faad14;
}

.theme-light .navbar-default .nav>li>a:hover,
.theme-light .navbar-default .nav>li>a:focus {
	background-color: rgb(240, 245, 255);
}

.fixed-sidebar.theme-light.mini-navbar .nav li:hover>a> span.nav-label {
    background-color: #f9fafc;
}

.fixed-sidebar.theme-light.mini-navbar .nav li:hover>.nav-second-level {
    background-color: #f9fafc;
}

.theme-light.skin-blue .nav:not(.navbar-toolbar)>li.selected {
	border-right: 3px solid #1890ff;
	margin-right: 2px;
}

.theme-light.skin-purple .nav:not(.navbar-toolbar)>li.selected{
	border-right: 3px solid #722ed1;
	margin-right: 2px;
}

.theme-light.skin-green .nav:not(.navbar-toolbar)>li.selected{
	border-right: 3px solid #52c41a;
	margin-right: 2px;
}

.theme-light.skin-red .nav:not(.navbar-toolbar)>li.selected{
	border-right: 3px solid #f5222d;
	margin-right: 2px;
}

.theme-light.skin-yellow .nav:not(.navbar-toolbar)>li.selected{
	border-right: 3px solid #faad14;
	margin-right: 2px;
}

/** 深蓝主题 theme-light **/
/**
.skin-blue.theme-blue .logo, .skin-white.theme-blue .logo {
	background-color: rgba(15,41,80,1) !important;
	color: #fff;
}
**/
.theme-blue .user-panel>.info>p, .theme-blue .user-panel>.info, .theme-blue .user-panel>.info>a{
	color: #a3b1cc
}

.theme-blue .nav:not(.navbar-toolbar)>li.active{
	background-color: rgba(15,41,80,1);
}

.theme-blue .navbar-static-side {
	background-color: rgba(15,41,80,1);
	box-shadow: 2px 0 2px 0 rgba(29,35,41,.05);
}

.theme-blue .user-panel {
	background-color: rgba(15,41,80,1);
}

.theme-blue .navbar-default .nav>li>a {
	color: #a3b1cc;
}

.theme-blue.skin-blue .navbar-default .nav>li.active>a {
	color: #1890ff;
}

.theme-blue.skin-blue .navbar-default .nav>li.selected>a,
.theme-blue.skin-blue .navbar-default .nav>li.selected>a:focus {
	background-color: #1890ff;
	color: rgba(255,255,255,1);
}

.theme-blue.skin-green .navbar-default .nav>li.active>a {
	color: #52c41a;
}

.theme-blue.skin-green .navbar-default .nav>li.selected>a,
.theme-blue.skin-green .navbar-default .nav>li.selected>a:focus {
	background-color: #52c41a;
    color: rgba(255,255,255,1);
}

.theme-blue.skin-purple .navbar-default .nav>li.active>a {
	color: rgb(114, 46, 209);
}

.theme-blue.skin-purple .navbar-default .nav>li.selected>a,
.theme-blue.skin-purple .navbar-default .nav>li.selected>a:focus {
	background-color: #722ed1;
    color: rgba(255,255,255,1);
}

.theme-blue.skin-red .navbar-default .nav>li.active>a {
	color: rgb(245, 34, 45);
}

.theme-blue.skin-red .navbar-default .nav>li.selected>a,
.theme-blue.skin-red .navbar-default .nav>li.selected>a:focus {
	background-color: #f5222d;
    color: rgba(255,255,255,1);
}

.theme-blue.skin-yellow .navbar-default .nav>li.active>a {
	color: rgb(250, 173, 20);
}

.theme-blue.skin-yellow .navbar-default .nav>li.selected>a,
.theme-blue.skin-yellow .navbar-default .nav>li.selected>a:focus {
	background-color: #faad14;
    color: rgba(255,255,255,1);
}

.theme-blue .navbar-default .nav>li>a:hover,
.theme-blue .navbar-default .nav>li>a:focus {
	background-color: rgba(15,41,80,1);
	box-shadow: 2px 0 2px 0 rgba(29,35,41,.05);
}

.fixed-sidebar.theme-blue.mini-navbar .nav li:hover>a> span.nav-label {
    background-color: rgba(15,41,80,1);
}

.fixed-sidebar.theme-blue.mini-navbar .nav li:hover>.nav-second-level {
    background-color: rgba(15,41,80,1);
}

/* tab页签样式 */
.page-tabs a {
	color: rgba(0,0,0,0.65)!important;
	padding: 0 8px 0 8px;
	min-width: 50px;
	text-align: center;
	line-height: 26px;
	border-radius: 2px;
	margin:5px 0 0 4px;
	border: 1px solid #d9d9d9;
}

.page-tabs a.active:hover, .page-tabs a.active i:hover {
	color: #000000!important;
	background: #f5f5f5!important;
}

.page-tabs a.active:hover i {
	color: #ffffff!important;
}

.page-tabs a.active i:hover {
	color: #ffffff!important;
	background: transparent!important;
}

.page-tabs a.active i {
	color: #ffffff;
}

/* tab页签主题样式 */
.skin-blue .page-tabs a.active {
	color: #ffffff!important;
	background: #1890ff!important;
	border: 1px solid #1890ff;
}

.skin-purple .page-tabs a.active {
	color: #ffffff!important;
	background: #722ed1!important;
	border: 1px solid #722ed1;
}

.skin-green .page-tabs a.active {
	color: #ffffff!important;
	background: #52c41a!important;
	border: 1px solid #52c41a;
}

.skin-red .page-tabs a.active {
	color: #ffffff!important;
	background: #dd4b39!important;
	border: 1px solid #dd4b39;
}

.skin-yellow .page-tabs a.active {
	color: #ffffff!important;
	background: #f39c12!important;
	border: 1px solid #f39c12;
}
