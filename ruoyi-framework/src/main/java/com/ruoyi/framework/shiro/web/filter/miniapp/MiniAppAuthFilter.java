package com.ruoyi.framework.shiro.web.filter.miniapp;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.shiro.token.MiniAppToken;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 小程序认证过滤器
 * 
 * <AUTHOR>
 */
public class MiniAppAuthFilter extends AuthenticatingFilter {
    private static final Logger log = LoggerFactory.getLogger(MiniAppAuthFilter.class);

    /** Authorization请求头名称 */
    private static final String AUTHORIZATION_HEADER = "Authorization";
    
    /** Bearer token前缀 */
    private static final String BEARER_PREFIX = "Bearer ";

    /**
     * 创建认证Token
     */
    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String token = getToken(httpRequest);
        
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        
        return new MiniAppToken(token);
    }

    /**
     * 从请求中获取token
     */
    private String getToken(HttpServletRequest request) {
        // 1. 从Authorization请求头获取
        String authHeader = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.isNotEmpty(authHeader) && authHeader.startsWith(BEARER_PREFIX)) {
            return authHeader.substring(BEARER_PREFIX.length());
        }
        
        // 2. 从请求参数获取
        String token = request.getParameter("token");
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        
        return null;
    }

    /**
     * 判断是否允许访问
     */
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        // 如果已经认证过，直接允许访问
        Subject subject = getSubject(request, response);
        return subject.isAuthenticated();
    }

    /**
     * 访问拒绝时的处理
     */
    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String token = getToken(httpRequest);
        
        if (StringUtils.isEmpty(token)) {
            log.debug("小程序认证失败：未找到token，请求路径：{}", httpRequest.getRequestURI());
            return onLoginFailure(null, new AuthenticationException("未找到认证token"), request, response);
        }
        
        // 尝试认证
        try {
            AuthenticationToken authToken = createToken(request, response);
            if (authToken == null) {
                return onLoginFailure(null, new AuthenticationException("创建认证token失败"), request, response);
            }
            
            Subject subject = getSubject(request, response);
            subject.login(authToken);
            
            log.debug("小程序认证成功，请求路径：{}", httpRequest.getRequestURI());
            return true;
            
        } catch (AuthenticationException e) {
            log.error("小程序认证失败：{}，请求路径：{}", e.getMessage(), httpRequest.getRequestURI());
            return onLoginFailure(null, e, request, response);
        }
    }

    /**
     * 认证失败处理
     */
    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e, 
                                   ServletRequest request, ServletResponse response) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        try {
            // 设置响应状态码为401
            httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            httpResponse.setContentType("application/json;charset=utf-8");
            
            // 构建错误响应
            AjaxResult result = AjaxResult.error(
                    "认证失败：" + (e != null ? e.getMessage() : "未知错误"));
            
            String jsonResult = JSON.toJSONString(result);
            ServletUtils.renderString(httpResponse, jsonResult);
            
        } catch (Exception ex) {
            log.error("返回认证失败响应异常", ex);
        }
        
        return false;
    }

    /**
     * 认证成功后的处理
     */
    @Override
    protected boolean onLoginSuccess(AuthenticationToken token, Subject subject, 
                                   ServletRequest request, ServletResponse response) throws Exception {
        // 认证成功，继续执行后续过滤器
        return true;
    }
}
