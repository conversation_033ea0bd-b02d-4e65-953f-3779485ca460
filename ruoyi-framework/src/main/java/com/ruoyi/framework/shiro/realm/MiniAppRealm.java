package com.ruoyi.framework.shiro.realm;

import com.ruoyi.common.core.domain.entity.User;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.wechat.MiniAppTokenUtils;
import com.ruoyi.framework.shiro.token.MiniAppToken;
import com.ruoyi.system.service.IUserService;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.Set;

/**
 * 小程序认证Realm
 * 
 * <AUTHOR>
 */
public class MiniAppRealm extends AuthorizingRealm {
    private static final Logger log = LoggerFactory.getLogger(MiniAppRealm.class);

    @Autowired
    private IUserService userService;

    /**
     * 必须重写此方法，不然Shiro会报错
     */
    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof MiniAppToken;
    }

    /**
     * 授权
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        String openid = (String) principals.getPrimaryPrincipal();
        log.debug("小程序用户授权，openid：{}", openid);

        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        
        // 小程序用户默认角色和权限
        Set<String> roles = new HashSet<>();
        Set<String> permissions = new HashSet<>();
        
        // 添加小程序用户基础角色
        roles.add("miniapp_user");
        
        // 添加小程序用户基础权限
        permissions.add("miniapp:user:view");
        permissions.add("miniapp:hotel:view");
        permissions.add("miniapp:hotel:book");
        
        authorizationInfo.setRoles(roles);
        authorizationInfo.setStringPermissions(permissions);
        
        log.debug("小程序用户授权完成，openid：{}，角色：{}，权限：{}", openid, roles, permissions);
        return authorizationInfo;
    }

    /**
     * 认证
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        MiniAppToken miniAppToken = (MiniAppToken) token;
        String tokenStr = miniAppToken.getToken();

        if (StringUtils.isEmpty(tokenStr)) {
            log.error("小程序认证失败：token为空");
            throw new AuthenticationException("token不能为空");
        }

        // 验证token
        MiniAppTokenUtils.TokenValidationResult validationResult = MiniAppTokenUtils.validateToken(tokenStr);
        if (!validationResult.isValid()) {
            log.error("小程序认证失败：token验证失败，{}", validationResult.getMessage());
            throw new AuthenticationException("token验证失败：" + validationResult.getMessage());
        }

        String openid = validationResult.getOpenid();
        Long userId = validationResult.getUserId();

        // 根据openid查询用户信息
        User user = userService.selectUserByOpenid(openid);
        if (user == null) {
            log.error("小程序认证失败：用户不存在，openid：{}", openid);
            throw new UnknownAccountException("用户不存在");
        }

        // 如果token需要刷新，生成新token
        if (validationResult.isNeedRefresh()) {
            String newToken = MiniAppTokenUtils.refreshToken(tokenStr);
            if (StringUtils.isNotEmpty(newToken)) {
                log.info("小程序token已自动刷新，openid：{}", openid);
                // 这里可以通过某种方式通知客户端更新token，比如在响应头中返回新token
                // 由于Shiro的限制，这里暂时只记录日志
            }
        }

        log.info("小程序认证成功，openid：{}，用户ID：{}", openid, user.getId());
        
        // 设置token中的用户信息
        miniAppToken.setOpenid(openid);
        miniAppToken.setUserId(user.getId());

        // 返回认证信息，principal为用户对象，credentials为token
        return new SimpleAuthenticationInfo(user, tokenStr, getName());
    }

    /**
     * 清理指定用户授权信息缓存
     */
    public void clearCachedAuthorizationInfo(Object principal) {
        if (principal != null) {
            clearCachedAuthorizationInfo(principal);
        }
    }

    /**
     * 清理所有用户授权信息缓存
     */
    public void clearAllCachedAuthorizationInfo() {
        getAuthorizationCache().clear();
    }
}
