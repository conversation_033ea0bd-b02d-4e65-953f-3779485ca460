package com.ruoyi.framework.shiro.token;

import org.apache.shiro.authc.AuthenticationToken;

/**
 * 小程序认证Token
 * 
 * <AUTHOR>
 */
public class MiniAppToken implements AuthenticationToken {
    private static final long serialVersionUID = 1L;

    /** 小程序token */
    private String token;

    /** 用户openid */
    private String openid;

    /** 用户ID */
    private Long userId;

    public MiniAppToken() {
    }

    public MiniAppToken(String token) {
        this.token = token;
    }

    public MiniAppToken(String token, String openid) {
        this.token = token;
        this.openid = openid;
    }

    public MiniAppToken(String token, String openid, Long userId) {
        this.token = token;
        this.openid = openid;
        this.userId = userId;
    }

    @Override
    public Object getPrincipal() {
        return openid;
    }

    @Override
    public Object getCredentials() {
        return token;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "MiniAppToken{" +
                "openid='" + openid + '\'' +
                ", userId=" + userId +
                '}';
    }
}
