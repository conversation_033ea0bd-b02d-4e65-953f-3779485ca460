# 识别码管理 - 每个房型单独设置数量功能

## 功能概述
在原有的识别码多房型绑定基础上，进一步优化为每个房型都可以单独设置数量，实现更精细化的房型数量管理。

## 主要改进内容

### 1. 数据库结构优化
- **修改表**: `category_code_room` - 添加 `room_count` 字段，用于存储每个房型的数量
- **字段说明**: `room_count INTEGER DEFAULT 1 COMMENT '该房型的房间数量'`

### 2. 后端代码改进

#### 修改的实体类
- `CategoryCodeRoom.java` - 添加 `roomCount` 属性
- `CategoryCode.java` - 添加 `roomCountMap` 属性用于前端传参
- `Room.java` - 添加临时 `roomCount` 字段用于存储关联数量信息

#### 修改的Mapper
- `CategoryCodeRoomMapper.xml` - 添加 `room_count` 字段的增删改查操作

#### 修改的Service
- `CategoryCodeServiceImpl.java` - 处理房型数量映射逻辑
- 在新增和修改时，根据前端传入的房型数量映射设置每个房型的数量

#### 修改的Controller
- `CategoryCodeController.java` - 处理前端传入的房型数量配置JSON
- `HotelApiController.java` - 返回详细的房型数量信息

### 3. 前端页面改进

#### 新增页面 (`add.html`)
- **房型配置区域**: 替换原来的多选下拉框，改为复选框+数量输入框的组合
- **动态配置**: 选择会议后动态加载房型列表，每个房型都有独立的数量设置
- **自动计算**: 总房间数量根据各房型数量自动计算
- **数据提交**: 将房型数量映射以JSON格式提交到后端

#### 编辑页面 (`edit.html`)
- **数据回显**: 正确显示已配置的房型和对应数量
- **修改支持**: 支持修改房型选择和数量配置

#### 列表页面 (`categoryCode.html`)
- **显示优化**: 房型列表显示格式为 "房型名称(数量间)"
- **信息完整**: 一目了然地看到每个房型的配置数量

#### 详情页面 (`view.html`)
- **标签显示**: 使用标签样式显示房型和数量信息
- **美观布局**: 每个房型独立显示，信息清晰

### 4. API接口改进

#### `/getRoomByCode` 接口
- **返回数据**: 包含每个房型的详细数量信息
- **兼容性**: 保持原有字段，新增详细数量信息
- **计算字段**: 自动计算总数量用于验证

## 使用说明

### 1. 数据库迁移
```sql
-- 为现有的关联表添加房间数量字段
ALTER TABLE `category_code_room` ADD COLUMN `room_count` INTEGER DEFAULT 1 COMMENT '该房型的房间数量';

-- 为现有数据设置默认数量
UPDATE `category_code_room` SET `room_count` = 1 WHERE `room_count` IS NULL;
```

### 2. 新增识别码操作
1. 选择会议后，系统自动加载该会议的房型列表
2. 勾选需要的房型，为每个房型设置具体数量
3. 系统自动计算总房间数量
4. 提交保存

### 3. 编辑识别码操作
1. 页面自动回显已配置的房型和数量
2. 可以修改房型选择和数量配置
3. 保存时更新配置

### 4. API调用示例
```javascript
// 调用获取房型接口
fetch('/api/getRoomByCode', {
    method: 'POST',
    body: 'categoryId=ABC123&conferenceId=1'
})
.then(response => response.json())
.then(data => {
    // data.data 包含房型列表，每个房型都有 roomCount 字段
    // data.totalRoomCount 总房间数量
    // data.calculatedTotalCount 计算得出的总数量
});
```

## 数据结构说明

### 前端提交数据格式
```javascript
{
    categoryId: "ABC123",
    conference: 1,
    roomIds: [1, 2, 3],  // 选中的房型ID列表
    roomCountMapJson: '{"1":5,"2":3,"3":2}',  // 房型数量映射JSON
    roomCount: 10  // 总数量（自动计算）
}
```

### API返回数据格式
```json
{
    "code": 200,
    "msg": "获取房型成功",
    "data": [
        {
            "id": 1,
            "roomTitle": "标准间",
            "roomCount": 5,
            "roomImgUrl": "..."
        },
        {
            "id": 2,
            "roomTitle": "豪华间", 
            "roomCount": 3,
            "roomImgUrl": "..."
        }
    ],
    "totalRoomCount": 10,
    "calculatedTotalCount": 8
}
```

## 优势特点

1. **精细化管理**: 每个房型都可以独立设置数量
2. **用户友好**: 界面直观，操作简单
3. **数据完整**: 保存和显示完整的房型数量信息
4. **自动计算**: 总数量自动计算，减少错误
5. **向后兼容**: 保持原有API接口的兼容性

## 注意事项

1. **数据迁移**: 升级前请备份数据库
2. **前端适配**: 调用API的前端代码需要适配新的数据结构
3. **数量验证**: 建议在前端和后端都添加数量的合理性验证
4. **性能考虑**: 大量房型时注意页面加载性能
