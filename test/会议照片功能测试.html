<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议照片功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; }
        .test-title { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-step { margin: 10px 0; padding: 10px; background-color: #f8f9fa; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        img { max-width: 200px; max-height: 150px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>会议照片功能测试指南</h1>
    
    <div class="test-section">
        <h2 class="test-title">1. 数据库准备</h2>
        <div class="test-step">
            <h3>执行SQL语句：</h3>
            <code>ALTER TABLE `conference` ADD COLUMN `photo_path` VARCHAR(255) COMMENT '会议照片路径' AFTER `operator`;</code>
        </div>
        <div class="test-step">
            <h3>验证字段添加：</h3>
            <code>DESCRIBE conference;</code>
            <p class="info">应该能看到 photo_path 字段</p>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">2. 新增会议测试</h2>
        <div class="test-step">
            <strong>步骤1：</strong> 访问 <code>http://localhost/hotel/conference</code>
        </div>
        <div class="test-step">
            <strong>步骤2：</strong> 点击"添加"按钮
        </div>
        <div class="test-step">
            <strong>步骤3：</strong> 填写会议基本信息
        </div>
        <div class="test-step">
            <strong>步骤4：</strong> 在"会议照片"字段选择一张图片文件
        </div>
        <div class="test-step">
            <strong>步骤5：</strong> 点击"保存"按钮
        </div>
        <div class="test-step">
            <strong>预期结果：</strong>
            <ul>
                <li class="success">保存成功提示</li>
                <li class="success">返回列表页面</li>
                <li class="success">列表中显示新增的会议及其缩略图</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">3. 编辑会议测试</h2>
        <div class="test-step">
            <strong>步骤1：</strong> 在会议列表中点击"编辑"按钮
        </div>
        <div class="test-step">
            <strong>步骤2：</strong> 查看是否显示当前会议照片
        </div>
        <div class="test-step">
            <strong>步骤3：</strong> 选择新的图片文件或保持不变
        </div>
        <div class="test-step">
            <strong>步骤4：</strong> 点击"保存"按钮
        </div>
        <div class="test-step">
            <strong>预期结果：</strong>
            <ul>
                <li class="success">修改成功提示</li>
                <li class="success">图片更新或保持不变</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">4. 查看功能测试</h2>
        <div class="test-step">
            <strong>列表页面：</strong> 检查缩略图显示是否正常
        </div>
        <div class="test-step">
            <strong>详情页面：</strong> 点击"查看"按钮，检查完整图片显示
        </div>
        <div class="test-step">
            <strong>图片点击：</strong> 点击列表中的缩略图，查看大图功能
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">5. 文件存储验证</h2>
        <div class="test-step">
            <strong>检查目录：</strong> 查看服务器上传目录
            <br><code>Windows: D:/ruoyi/uploadPath/upload/</code>
            <br><code>Linux: /home/<USER>/uploadPath/upload/</code>
        </div>
        <div class="test-step">
            <strong>文件结构：</strong> 文件按日期目录组织
            <br><code>upload/2025/01/29/文件名</code>
        </div>
        <div class="test-step">
            <strong>直接访问：</strong> 通过浏览器访问图片URL
            <br><code>http://localhost/profile/upload/2025/01/29/文件名</code>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">6. 常见问题排查</h2>
        <div class="test-step">
            <h3 class="error">文件上传失败</h3>
            <ul>
                <li>检查文件大小是否超过10MB</li>
                <li>检查文件格式是否为图片格式</li>
                <li>检查服务器磁盘空间</li>
                <li>检查上传目录权限</li>
            </ul>
        </div>
        <div class="test-step">
            <h3 class="error">图片显示异常</h3>
            <ul>
                <li>检查静态资源映射配置</li>
                <li>检查图片路径是否正确</li>
                <li>查看浏览器控制台错误信息</li>
            </ul>
        </div>
        <div class="test-step">
            <h3 class="error">数据保存失败</h3>
            <ul>
                <li>检查数据库字段是否存在</li>
                <li>检查字段长度是否足够</li>
                <li>查看服务器日志错误信息</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">7. 测试检查清单</h2>
        <div class="test-step">
            <input type="checkbox"> 数据库字段添加成功<br>
            <input type="checkbox"> 新增会议可以上传图片<br>
            <input type="checkbox"> 编辑会议可以修改图片<br>
            <input type="checkbox"> 编辑时显示当前图片<br>
            <input type="checkbox"> 列表页面显示缩略图<br>
            <input type="checkbox"> 详情页面显示完整图片<br>
            <input type="checkbox"> 图片文件正确保存到服务器<br>
            <input type="checkbox"> 图片URL可以直接访问<br>
            <input type="checkbox"> 不选择文件时保持原图片不变<br>
            <input type="checkbox"> 文件格式验证正常<br>
        </div>
    </div>
</body>
</html>
