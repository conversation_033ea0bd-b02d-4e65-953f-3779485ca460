# 会议照片功能部署指南

## 快速部署步骤

### 1. 数据库更新
执行以下SQL语句添加新字段：
```sql
ALTER TABLE `conference` ADD COLUMN `photo_path` VARCHAR(255) COMMENT '会议照片路径' AFTER `operator`;
```

### 2. 代码部署
确保以下文件已更新到服务器：

#### 后端文件
- `ruoyi-system/src/main/java/com/ruoyi/system/domain/Conference.java`
- `ruoyi-system/src/main/resources/mapper/system/ConferenceMapper.xml`
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/hotel/ConferenceController.java`

#### 前端文件
- `ruoyi-admin/src/main/resources/templates/hotel/conference/add.html`
- `ruoyi-admin/src/main/resources/templates/hotel/conference/edit.html`
- `ruoyi-admin/src/main/resources/templates/hotel/conference/view.html`
- `ruoyi-admin/src/main/resources/templates/hotel/conference/conference.html`

### 3. 配置检查
确认`application.yml`中的配置：
```yaml
ruoyi:
  # 文件上传路径
  profile: D:/ruoyi/uploadPath  # Windows
  # profile: /home/<USER>/uploadPath  # Linux

spring:
  servlet:
    multipart:
      # 单个文件大小限制
      max-file-size: 10MB
      # 总上传大小限制
      max-request-size: 20MB
```

### 4. 目录权限
确保上传目录有写权限：
```bash
# Linux系统
chmod 755 /home/<USER>/uploadPath
chmod 755 /home/<USER>/uploadPath/upload
```

### 5. 重启应用
重启Spring Boot应用使配置生效。

## 功能验证

### 基本功能测试
1. **新增会议**：访问会议管理页面，点击添加，上传图片并保存
2. **编辑会议**：编辑现有会议，修改图片
3. **查看功能**：检查列表和详情页面的图片显示

### 文件存储验证
1. 检查服务器上传目录是否有新文件
2. 文件路径格式：`upload/yyyy/MM/dd/filename`
3. 通过浏览器访问：`http://域名/profile/upload/yyyy/MM/dd/filename`

### 数据库验证
```sql
-- 查看新增的字段
DESCRIBE conference;

-- 查看数据是否正确保存
SELECT id, conference_title, photo_path FROM conference WHERE photo_path IS NOT NULL;
```

## 故障排除

### 常见问题

#### 1. 文件上传失败
**现象**：点击保存后提示上传失败
**排查**：
- 检查文件大小是否超过限制
- 检查文件格式是否为图片
- 查看服务器日志错误信息
- 检查磁盘空间是否充足

#### 2. 图片显示异常
**现象**：图片无法显示或显示错误
**排查**：
- 检查静态资源映射配置
- 确认图片文件是否存在
- 检查图片路径是否正确
- 查看浏览器控制台错误

#### 3. 数据保存失败
**现象**：表单提交后数据未保存
**排查**：
- 确认数据库字段是否添加成功
- 检查字段长度是否足够
- 查看服务器后台日志

### 日志查看
```bash
# 查看应用日志
tail -f logs/ruoyi.log

# 查看错误日志
tail -f logs/error.log
```

## 性能优化建议

### 1. 文件大小控制
- 建议限制单个文件大小为5MB以内
- 可以添加图片压缩功能

### 2. 存储优化
- 定期清理无用的图片文件
- 考虑使用CDN存储图片
- 实现图片缓存策略

### 3. 用户体验
- 添加上传进度显示
- 实现图片预览功能
- 支持拖拽上传

## 安全注意事项

1. **文件类型验证**：严格限制上传文件类型
2. **文件大小限制**：防止大文件攻击
3. **路径安全**：防止路径遍历攻击
4. **权限控制**：确保只有授权用户可以上传文件

## 备份建议

1. **定期备份上传目录**
2. **数据库定期备份**
3. **配置文件备份**

## 监控建议

1. **磁盘空间监控**
2. **上传失败率监控**
3. **文件访问日志监控**
