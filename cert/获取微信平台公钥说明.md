# 获取微信平台公钥说明

## 重要提示

⚠️ **cert/wechat_platform_public_key_example.pem** 文件中的公钥仅为示例，不是真实的微信平台公钥！

在生产环境中，您必须使用真实的微信平台公钥进行验签。

## 获取真实微信平台公钥的方法

### 方法一：从微信商户平台下载证书并提取公钥

1. **登录微信商户平台**
   - 访问：https://pay.weixin.qq.com/
   - 使用商户账号登录

2. **下载平台证书**
   - 进入"账户中心" → "API安全"
   - 点击"下载证书"
   - 下载平台证书文件

3. **提取公钥**
   ```bash
   # 从平台证书中提取公钥
   openssl x509 -pubkey -noout -in wechatpay_platform_cert.pem > wechat_platform_public_key.pem
   ```

### 方法二：通过微信API获取证书（推荐）

1. **调用证书下载API**
   ```bash
   curl -X GET \
     'https://api.mch.weixin.qq.com/v3/certificates' \
     -H 'Authorization: WECHATPAY2-SHA256-RSA2048 mchid="YOUR_MCH_ID",nonce_str="NONCE",timestamp="TIMESTAMP",serial_no="SERIAL_NO",signature="SIGNATURE"' \
     -H 'Accept: application/json'
   ```

2. **解密证书内容**
   - API返回的证书内容是加密的
   - 需要使用您的APIv3密钥进行解密

3. **提取公钥**
   - 解密后得到PEM格式的证书
   - 使用OpenSSL提取公钥

### 方法三：使用微信官方SDK

如果您使用微信官方提供的SDK，可以通过SDK自动获取和管理平台证书。

## 配置真实公钥

### 1. 文件方式配置

将获取到的真实公钥保存为文件，然后在配置中指定路径：

```yaml
wechat:
  pay:
    wechatPlatformPublicKeyPath: /path/to/real_wechat_platform_public_key.pem
```

### 2. 字符串方式配置

直接在配置文件中配置公钥内容：

```yaml
wechat:
  pay:
    wechatPlatformPublicKey: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      （这里是真实的公钥内容）
      ...
      -----END PUBLIC KEY-----
```

## 验证公钥有效性

### 使用OpenSSL验证

```bash
# 验证公钥文件格式
openssl rsa -pubin -in wechat_platform_public_key.pem -text -noout

# 检查公钥信息
openssl rsa -pubin -in wechat_platform_public_key.pem -noout -modulus
```

### 测试验签功能

在获取到真实公钥后，建议：

1. **沙箱测试**：在微信支付沙箱环境中测试验签功能
2. **模拟回调**：使用微信提供的回调测试工具
3. **日志检查**：查看验签成功/失败的日志

## 安全注意事项

### 1. 公钥管理

- **定期更新**：关注微信平台公钥的更新通知
- **备份存储**：保存公钥的多个备份
- **版本管理**：记录公钥的版本和更新时间

### 2. 文件安全

- **权限设置**：公钥文件权限设置为600
- **路径安全**：使用绝对路径，避免路径遍历攻击
- **访问控制**：限制对公钥文件的访问

### 3. 配置安全

- **环境隔离**：不同环境使用不同的公钥
- **敏感信息**：避免在日志中输出完整公钥
- **版本控制**：不要将真实公钥提交到代码仓库

## 常见问题

### Q: 如何知道公钥是否需要更新？

A: 微信会通过以下方式通知：
- 商户平台消息通知
- 邮件通知
- API返回证书过期信息

### Q: 公钥更新后如何处理？

A: 
1. 下载新的平台证书
2. 提取新的公钥
3. 更新配置文件
4. 重启应用或调用重新加载接口

### Q: 验签失败如何排查？

A:
1. 检查公钥是否为最新版本
2. 验证公钥格式是否正确
3. 确认验签字符串构建是否符合规范
4. 检查签名算法是否正确（SHA256withRSA）

## 联系支持

如果在获取或使用微信平台公钥过程中遇到问题，可以：

1. **查看微信支付官方文档**：https://pay.weixin.qq.com/wiki/doc/apiv3/
2. **联系微信支付技术支持**
3. **查看商户平台的帮助文档**

---

**再次提醒**：示例公钥仅用于代码测试，生产环境必须使用真实的微信平台公钥！
