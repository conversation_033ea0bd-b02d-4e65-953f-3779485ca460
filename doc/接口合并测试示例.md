# 接口简化测试示例

## 简化后的接口说明

原来的两个接口：
- `GET /api/hotel/payment/query` - 查询支付状态
- `POST /api/hotel/order/refresh-status` - 刷新订单状态

现在简化为一个接口：
- `GET /api/hotel/payment/query?orderNo={orderNo}`

## 接口参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 是 | 订单号 |

## 返回数据格式

```json
{
  "code": 0,
  "msg": "查询并刷新订单状态成功",
  "data": {
    "paymentResult": {
      "tradeState": "SUCCESS",
      "transactionId": "4200001234567890123",
      "outTradeNo": "HT20250113123456789",
      "successTime": "2025-01-13T10:30:00+08:00",
      // ... 其他微信支付返回字段
    },
    "orderInfo": {
      "orderId": 123,
      "orderNo": "HT20250113123456789",
      "orderStatus": "PAID",
      "paymentStatus": "PAID",
      // ... 其他订单字段
    },
    "refreshed": true
  }
}
```

## 小程序端调用示例

### 查询并刷新订单状态

```javascript
// 统一的查询并刷新接口，默认执行刷新操作
api.payment.queryAndRefreshPayment('HT20250113123456789')
  .then(result => {
    console.log('支付状态:', result.paymentResult.tradeState);
    console.log('订单信息:', result.orderInfo);
    console.log('是否已刷新:', result.refreshed);
  })
  .catch(err => {
    console.error('查询刷新失败:', err);
  });
```

### 支付成功后的完整处理流程

```javascript
// 在支付成功回调中使用
handlePaymentSuccess: function(orderData, paymentResult) {
  wx.showLoading({ title: '正在确认支付...' });
  
  // 使用合并接口进行验证和刷新
  PaymentUtils.verifyPaymentAndRefreshOrder(orderData.orderNo)
    .then(updatedOrder => {
      // 支付验证成功，订单状态已同步
      this.updateLocalOrderData(orderData, updatedOrder);
      this.navigateToSuccessPage();
    })
    .catch(err => {
      // 验证失败，但给用户友好提示
      this.handlePaymentSuccessWithWarning(orderData, err);
    })
    .finally(() => {
      wx.hideLoading();
    });
}
```

## 接口优势对比

### 简化前（两个接口）
```javascript
// 需要两次API调用
Promise.all([
  api.payment.queryPayment(orderNo),
  api.payment.refreshOrderStatus(orderNo)
]).then(([payResult, orderResult]) => {
  // 处理两个结果
}).catch(err => {
  // 错误处理复杂
});
```

### 简化后（一个接口）
```javascript
// 只需一次API调用，默认刷新
api.payment.queryAndRefreshPayment(orderNo)
  .then(result => {
    // 统一处理结果
    const { paymentResult, orderInfo, refreshed } = result;
  })
  .catch(err => {
    // 统一错误处理
  });
```

## 测试场景

### 1. 正常支付成功场景
```bash
# 请求
GET /api/hotel/payment/query?orderNo=HT20250113123456789

# 预期响应
{
  "code": 0,
  "msg": "查询并刷新订单状态成功",
  "data": {
    "paymentResult": { "tradeState": "SUCCESS" },
    "orderInfo": { "orderStatus": "PAID" },
    "refreshed": true
  }
}
```

### 2. 支付未完成场景
```bash
# 请求
GET /api/hotel/payment/query?orderNo=HT20250113123456789

# 预期响应
{
  "code": 0,
  "msg": "查询并刷新订单状态成功",
  "data": {
    "paymentResult": { "tradeState": "NOTPAY" },
    "orderInfo": { "orderStatus": "PENDING" },
    "refreshed": true
  }
}
```

### 3. 权限验证场景
```bash
# 请求（用户A尝试查询用户B的订单）
GET /api/hotel/payment/query?orderNo=HT20250113123456789

# 预期响应
{
  "code": 500,
  "msg": "无权限操作此订单"
}
```

## 性能优化

1. **减少网络请求**：从2次请求减少到1次
2. **减少服务器负载**：避免重复的权限验证和订单查询
3. **提高响应速度**：减少网络往返时间
4. **简化错误处理**：统一的错误处理逻辑

## 简化说明

- 去掉了可选参数，简化接口设计
- 默认执行刷新操作，确保状态同步
- 返回数据结构保持不变
- 小程序端调用更加简洁

## 注意事项

1. **参数验证**：确保orderNo参数不为空
2. **权限控制**：验证用户只能操作自己的订单
3. **异常处理**：网络异常时的重试机制
4. **日志记录**：详细记录操作日志便于排查问题
5. **性能监控**：监控接口响应时间和成功率
