# Shiro多Realm认证问题修复说明

## 问题描述

在实现小程序Shiro统一认证后，管理后台登录出现以下错误：

```
Authentication token of type [class org.apache.shiro.authc.UsernamePasswordToken] could not be authenticated by any configured realms. Please ensure that at least one realm can authenticate these tokens.
```

## 问题原因

当配置多个Realm时，Shiro默认使用`AllSuccessfulStrategy`认证策略，要求所有Realm都能成功认证同一个token。但是：

1. **UserRealm** 只能处理 `UsernamePasswordToken`（管理后台登录）
2. **MiniAppRealm** 只能处理 `MiniAppToken`（小程序登录）

由于两个Realm无法同时认证同一个token，导致认证失败。

## 解决方案

### 1. 配置认证策略

修改`ShiroConfig.java`，使用`AtLeastOneSuccessfulStrategy`策略：

```java
@Bean
public SecurityManager securityManager(UserRealm userRealm, MiniAppRealm miniAppRealm) {
    DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
    
    // 配置认证器，使用"至少一个成功"策略
    ModularRealmAuthenticator authenticator = new ModularRealmAuthenticator();
    authenticator.setAuthenticationStrategy(new AtLeastOneSuccessfulStrategy());
    securityManager.setAuthenticator(authenticator);
    
    // 设置多个realm，支持Web用户和小程序用户认证
    securityManager.setRealms(java.util.Arrays.asList(userRealm, miniAppRealm));
    
    // 其他配置...
    return securityManager;
}
```

### 2. 添加supports方法

为每个Realm明确指定支持的Token类型：

#### UserRealm.java
```java
/**
 * 必须重写此方法，指定该Realm只处理UsernamePasswordToken
 */
@Override
public boolean supports(AuthenticationToken token) {
    return token instanceof UsernamePasswordToken;
}
```

#### MiniAppRealm.java
```java
/**
 * 必须重写此方法，不然Shiro会报错
 */
@Override
public boolean supports(AuthenticationToken token) {
    return token instanceof MiniAppToken;
}
```

### 3. 添加必要的导入

在`ShiroConfig.java`中添加：

```java
import org.apache.shiro.authc.pam.AtLeastOneSuccessfulStrategy;
import org.apache.shiro.authc.pam.ModularRealmAuthenticator;
```

## 认证策略说明

### AtLeastOneSuccessfulStrategy（至少一个成功）
- 只要有一个Realm认证成功即可
- 适用于多种认证方式并存的场景
- **推荐使用**

### AllSuccessfulStrategy（全部成功）
- 要求所有Realm都认证成功
- 默认策略
- 适用于需要多重验证的场景

### FirstSuccessfulStrategy（第一个成功）
- 只要第一个Realm认证成功即可，不再尝试其他Realm
- 适用于有优先级的认证场景

## 修复后的工作流程

### 管理后台登录流程
1. 用户提交用户名密码
2. Shiro创建`UsernamePasswordToken`
3. `UserRealm.supports()`返回true，`MiniAppRealm.supports()`返回false
4. 只有`UserRealm`参与认证
5. 认证成功

### 小程序登录流程
1. 小程序提交token
2. Shiro创建`MiniAppToken`
3. `UserRealm.supports()`返回false，`MiniAppRealm.supports()`返回true
4. 只有`MiniAppRealm`参与认证
5. 认证成功

## 验证步骤

### 1. 验证管理后台登录
1. 启动项目
2. 访问 `http://localhost/login`
3. 输入管理员账号密码
4. 确认能够正常登录

### 2. 验证小程序认证
1. 调用小程序登录接口获取token
2. 使用token访问酒店API接口
3. 确认认证正常工作

### 3. 检查日志
启动时应该看到类似日志：
```
INFO  - 配置了2个Realm: UserRealm, MiniAppRealm
INFO  - 使用认证策略: AtLeastOneSuccessfulStrategy
```

## 其他修复

### 1. 修复User实体类型问题
- 确认`User.getId()`返回`Long`类型
- 移除不必要的类型转换

### 2. 清理无效代码
- 移除MiniAppRealm中被注释的delFlag检查代码
- 小程序User实体没有delFlag字段

## 最佳实践

### 1. Realm设计原则
- 每个Realm只处理特定类型的Token
- 明确实现supports方法
- 避免在一个Realm中处理多种Token类型

### 2. 认证策略选择
- 多种认证方式并存：使用`AtLeastOneSuccessfulStrategy`
- 需要多重验证：使用`AllSuccessfulStrategy`
- 有优先级认证：使用`FirstSuccessfulStrategy`

### 3. 调试技巧
- 开启Shiro调试日志
- 检查supports方法返回值
- 验证Token类型匹配

## 总结

通过配置正确的认证策略和明确的Token支持类型，成功解决了多Realm认证冲突问题。现在系统可以同时支持：

- ✅ 管理后台用户名密码登录
- ✅ 小程序token认证
- ✅ 两种认证方式互不干扰
- ✅ 统一的权限管理

这种设计模式可以轻松扩展支持更多的认证方式，如API Key认证、OAuth认证等。
