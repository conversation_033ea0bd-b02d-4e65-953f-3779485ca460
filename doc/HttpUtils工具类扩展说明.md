# HttpUtils工具类扩展说明

## 概述

为了支持微信支付V3版本的自定义请求头需求，我们对原有的HttpUtils工具类进行了扩展，新增了支持自定义请求头的方法，并创建了HttpResponse响应对象来更好地处理HTTP响应结果。

## 扩展功能

### 1. 新增支持自定义请求头的方法

#### 1.1 GET请求（支持自定义请求头）
```java
/**
 * 向指定 URL 发送GET方法的请求（支持自定义请求头）
 */
public static String sendGet(String url, String param, Map<String, String> headers)
```

#### 1.2 POST请求（支持自定义请求头）
```java
/**
 * 向指定 URL 发送POST方法的请求（支持自定义请求头）
 */
public static String sendPost(String url, String param, String contentType, Map<String, String> headers)
```

#### 1.3 HTTPS POST请求（支持自定义请求头）
```java
/**
 * 向指定 URL 发送HTTPS POST方法的请求（支持自定义请求头）
 */
public static String sendSSLPost(String url, String param, String contentType, Map<String, String> headers)
```

#### 1.4 HTTPS GET请求（支持自定义请求头）
```java
/**
 * 向指定 URL 发送HTTPS GET方法的请求（支持自定义请求头）
 */
public static String sendSSLGet(String url, String param, Map<String, String> headers)
```

### 2. 新增返回HttpResponse对象的方法

#### 2.1 POST请求（返回HttpResponse对象）
```java
/**
 * 向指定 URL 发送POST方法的请求，返回HttpResponse对象（支持自定义请求头）
 */
public static HttpResponse sendPostWithResponse(String url, String param, String contentType, Map<String, String> headers)
```

#### 2.2 HTTPS POST请求（返回HttpResponse对象）
```java
/**
 * 向指定 URL 发送HTTPS POST方法的请求，返回HttpResponse对象（支持自定义请求头）
 */
public static HttpResponse sendSSLPostWithResponse(String url, String param, String contentType, Map<String, String> headers)
```

#### 2.3 HTTPS GET请求（返回HttpResponse对象）
```java
/**
 * 向指定 URL 发送HTTPS GET方法的请求，返回HttpResponse对象（支持自定义请求头）
 */
public static HttpResponse sendSSLGetWithResponse(String url, String param, Map<String, String> headers)
```

### 3. HttpResponse响应对象

新增的HttpResponse类用于封装HTTP响应结果：

```java
public class HttpResponse {
    private int statusCode;        // 响应状态码
    private String body;           // 响应体
    private boolean success;       // 是否成功
    private String errorMessage;   // 错误信息
    
    // 静态工厂方法
    public static HttpResponse success(int statusCode, String body)
    public static HttpResponse error(int statusCode, String errorMessage)
    public static HttpResponse exception(String errorMessage)
}
```

## 使用示例

### 1. 基本用法（带自定义请求头）

```java
// 构建自定义请求头
Map<String, String> headers = new HashMap<>();
headers.put("Authorization", "Bearer your-token");
headers.put("Content-Type", "application/json");
headers.put("User-Agent", "YourApp/1.0");

// 发送GET请求
String response = HttpUtils.sendSSLGet("https://api.example.com/data", "param=value", headers);

// 发送POST请求
String postData = "{\"name\":\"test\",\"value\":\"data\"}";
String response = HttpUtils.sendSSLPost("https://api.example.com/create", postData, "application/json", headers);
```

### 2. 使用HttpResponse对象

```java
// 发送请求并获取详细响应信息
HttpResponse response = HttpUtils.sendSSLPostWithResponse(url, requestBody, "application/json", headers);

// 检查响应状态
if (response.isSuccess()) {
    System.out.println("请求成功，状态码: " + response.getStatusCode());
    System.out.println("响应内容: " + response.getBody());
} else {
    System.out.println("请求失败，状态码: " + response.getStatusCode());
    System.out.println("错误信息: " + response.getErrorMessage());
}
```

### 3. 微信支付V3版本中的应用

```java
// 在WechatPayV3Utils中的使用示例
private Map<String, String> buildHttpHeaders(String method, String url, String body) throws Exception {
    // 构建微信支付V3版本的请求头
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept", "application/json");
    headers.put("User-Agent", "WechatPay-Java-SDK");
    headers.put("Authorization", buildAuthorizationHeader(method, url, body));
    return headers;
}

public WechatPayV3Result jsapiPay(WechatPayRequest payRequest) {
    String requestBody = JSON.toJSONString(v3Request);
    Map<String, String> headers = buildHttpHeaders("POST", url, requestBody);
    
    // 使用扩展的HttpUtils发送请求
    HttpResponse response = HttpUtils.sendSSLPostWithResponse(url, requestBody, 
        "application/json", headers);
    
    if (response.isSuccess()) {
        return JSON.parseObject(response.getBody(), WechatPayV3Result.class);
    } else {
        return createErrorResult("请求失败：" + response.getErrorMessage());
    }
}
```

## 扩展优势

### 1. 统一的HTTP工具
- 所有HTTP请求都使用统一的工具类
- 减少了对第三方HTTP库的依赖
- 保持了代码风格的一致性

### 2. 灵活的请求头支持
- 支持任意自定义请求头
- 满足各种API接口的认证需求
- 支持微信支付V3版本的复杂签名头

### 3. 完善的响应处理
- HttpResponse对象提供了完整的响应信息
- 统一的成功/失败判断逻辑
- 详细的错误信息记录

### 4. 向后兼容
- 保留了所有原有的方法
- 新增方法不影响现有代码
- 渐进式升级支持

## 错误处理

### 1. 网络异常处理
```java
HttpResponse response = HttpUtils.sendSSLPostWithResponse(url, data, contentType, headers);

if (!response.isSuccess()) {
    if (response.getStatusCode() == -1) {
        // 网络连接异常
        log.error("网络连接失败: {}", response.getErrorMessage());
    } else if (response.getStatusCode() >= 400) {
        // HTTP错误状态码
        log.error("HTTP错误，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
    }
}
```

### 2. 超时处理
工具类会自动处理连接超时和读取超时异常，并在HttpResponse中返回相应的错误信息。

### 3. SSL证书处理
对于HTTPS请求，工具类使用了信任所有证书的策略，适用于开发和测试环境。生产环境建议配置正确的SSL证书验证。

## 性能考虑

### 1. 连接复用
当前实现为每次请求创建新连接，对于高频调用场景，建议考虑连接池优化。

### 2. 超时设置
可以根据实际需求调整连接超时和读取超时时间。

### 3. 内存使用
对于大文件传输，建议使用流式处理避免内存溢出。

## 测试验证

项目中包含了完整的测试用例：

1. **HttpUtilsTest.java** - HttpUtils工具类功能测试
2. **WechatPayTest.java** - 微信支付集成测试

运行测试：
```bash
# 测试HttpUtils功能
mvn test -Dtest=HttpUtilsTest

# 测试微信支付集成
mvn test -Dtest=WechatPayTest
```

## 总结

通过扩展HttpUtils工具类，我们实现了：

1. ✅ **统一的HTTP请求处理** - 所有HTTP请求使用同一套工具
2. ✅ **灵活的请求头支持** - 满足微信支付V3版本等复杂场景
3. ✅ **完善的响应处理** - HttpResponse对象提供详细的响应信息
4. ✅ **向后兼容性** - 不影响现有代码的使用
5. ✅ **易于维护** - 统一的错误处理和日志记录

这些扩展为微信支付V3版本的实现提供了强有力的支持，同时也为其他需要自定义请求头的场景提供了便利。
