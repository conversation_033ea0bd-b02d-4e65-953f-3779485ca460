# 微信支付V3版本部署配置指南

## 前期准备

### 1. 微信商户平台配置

#### 1.1 开通微信支付
1. 登录[微信商户平台](https://pay.weixin.qq.com/)
2. 完成商户认证和资质审核
3. 开通"JSAPI支付"产品功能
4. 获取商户号（mch_id）

#### 1.2 配置小程序支付
1. 在商户平台绑定小程序AppID
2. 设置支付目录（如：https://your-domain.com/）
3. 配置支付回调URL

#### 1.3 获取API密钥
1. **APIv3密钥**：商户平台 → 账户中心 → API安全 → 设置APIv3密钥
   - 密钥长度：32位
   - 包含大小写字母和数字
   - 示例：`Abc123Def456Ghi789Jkl012Mno345Pq`

2. **API证书**：下载商户API证书
   - 下载证书压缩包
   - 解压获得：`apiclient_cert.pem`、`apiclient_key.pem`、`apiclient_cert.p12`

#### 1.4 获取证书序列号
```bash
# 使用OpenSSL查看证书序列号
openssl x509 -in apiclient_cert.pem -noout -serial
# 输出示例：serial=1234567890ABCDEF1234567890ABCDEF12345678
```

### 2. 服务器环境准备

#### 2.1 HTTPS证书
- 微信支付V3要求使用HTTPS
- 确保SSL证书有效且未过期
- 配置强制HTTPS重定向

#### 2.2 防火墙配置
```bash
# 开放HTTPS端口
sudo ufw allow 443/tcp
# 开放HTTP端口（用于重定向）
sudo ufw allow 80/tcp
```

#### 2.3 域名解析
- 确保域名正确解析到服务器IP
- 配置回调域名可被微信服务器访问

## 配置步骤

### 1. 证书文件部署

#### 1.1 创建证书目录
```bash
# 创建证书存放目录
sudo mkdir -p /opt/wechat-pay/certs
sudo chmod 700 /opt/wechat-pay/certs
```

#### 1.2 上传证书文件
```bash
# 上传私钥文件
sudo cp apiclient_key.pem /opt/wechat-pay/certs/
sudo chmod 600 /opt/wechat-pay/certs/apiclient_key.pem
sudo chown app:app /opt/wechat-pay/certs/apiclient_key.pem
```

#### 1.3 验证证书
```bash
# 验证私钥文件格式
openssl rsa -in /opt/wechat-pay/certs/apiclient_key.pem -check -noout
# 应该输出：RSA key ok
```

### 2. 应用配置

#### 2.1 更新application.yml
```yaml
# 微信配置
wechat:
  miniapp:
    # 小程序AppID（从微信小程序后台获取）
    appId: wx1234567890abcdef
    # 小程序AppSecret（从微信小程序后台获取）
    appSecret: abcdef1234567890abcdef1234567890ab
    # access_token缓存时间（秒）
    tokenCacheTime: 7200
  
  pay:
    # 商户号（从微信商户平台获取）
    mchId: 1234567890
    
    # V2版本配置（兼容性保留）
    apiSecret: your_v2_api_secret_32_characters
    
    # V3版本配置
    apiV3Key: Abc123Def456Ghi789Jkl012Mno345Pq
    privateKeyPath: /opt/wechat-pay/certs/apiclient_key.pem
    merchantSerialNumber: 1234567890ABCDEF1234567890ABCDEF12345678
    
    # 回调配置
    notifyUrl: https://your-domain.com/api/wechat/pay/notify
    
    # 其他配置
    timeoutMinutes: 30
    apiVersion: V3
```

#### 2.2 环境变量配置（推荐）
```bash
# 创建环境变量文件
cat > /opt/app/.env << EOF
WECHAT_MINIAPP_APPID=wx1234567890abcdef
WECHAT_MINIAPP_APPSECRET=abcdef1234567890abcdef1234567890ab
WECHAT_PAY_MCH_ID=1234567890
WECHAT_PAY_API_V3_KEY=Abc123Def456Ghi789Jkl012Mno345Pq
WECHAT_PAY_PRIVATE_KEY_PATH=/opt/wechat-pay/certs/apiclient_key.pem
WECHAT_PAY_MERCHANT_SERIAL_NUMBER=1234567890ABCDEF1234567890ABCDEF12345678
WECHAT_PAY_NOTIFY_URL=https://your-domain.com/api/wechat/pay/notify
EOF

# 设置文件权限
chmod 600 /opt/app/.env
```

#### 2.3 Spring Boot配置引用环境变量
```yaml
wechat:
  miniapp:
    appId: ${WECHAT_MINIAPP_APPID}
    appSecret: ${WECHAT_MINIAPP_APPSECRET}
  pay:
    mchId: ${WECHAT_PAY_MCH_ID}
    apiV3Key: ${WECHAT_PAY_API_V3_KEY}
    privateKeyPath: ${WECHAT_PAY_PRIVATE_KEY_PATH}
    merchantSerialNumber: ${WECHAT_PAY_MERCHANT_SERIAL_NUMBER}
    notifyUrl: ${WECHAT_PAY_NOTIFY_URL}
    apiVersion: V3
```

### 3. Nginx配置

#### 3.1 HTTPS配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers on;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 微信支付回调专用配置
    location /api/wechat/pay/notify {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 保持原始请求头
        proxy_pass_request_headers on;
        proxy_set_header Wechatpay-Signature $http_wechatpay_signature;
        proxy_set_header Wechatpay-Timestamp $http_wechatpay_timestamp;
        proxy_set_header Wechatpay-Nonce $http_wechatpay_nonce;
        proxy_set_header Wechatpay-Serial $http_wechatpay_serial;
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### 4. 应用部署

#### 4.1 构建应用
```bash
# 编译打包
mvn clean package -DskipTests

# 检查JAR文件
ls -la ruoyi-admin/target/ruoyi-admin.jar
```

#### 4.2 创建启动脚本
```bash
cat > /opt/app/start.sh << 'EOF'
#!/bin/bash

APP_NAME="ruoyi-admin"
APP_JAR="/opt/app/ruoyi-admin.jar"
APP_LOG="/opt/app/logs/application.log"

# 加载环境变量
source /opt/app/.env

# 启动应用
nohup java -jar \
  -Dspring.profiles.active=prod \
  -Xms512m -Xmx1024m \
  $APP_JAR > $APP_LOG 2>&1 &

echo "应用启动中，PID: $!"
EOF

chmod +x /opt/app/start.sh
```

#### 4.3 创建系统服务
```bash
cat > /etc/systemd/system/ruoyi-app.service << 'EOF'
[Unit]
Description=RuoYi Application
After=network.target

[Service]
Type=forking
User=app
Group=app
WorkingDirectory=/opt/app
ExecStart=/opt/app/start.sh
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
systemctl enable ruoyi-app
systemctl start ruoyi-app
```

## 测试验证

### 1. 配置验证

#### 1.1 检查配置加载
```bash
# 查看应用日志
tail -f /opt/app/logs/application.log | grep -i wechat

# 应该看到类似输出：
# 2025-01-17 10:00:00.123 INFO  - 微信支付配置加载成功，API版本: V3
# 2025-01-17 10:00:00.124 INFO  - 商户私钥加载成功
```

#### 1.2 测试API接口
```bash
# 测试健康检查
curl -k https://your-domain.com/actuator/health

# 测试支付接口（需要有效token）
curl -X POST https://your-domain.com/api/hotel/payment/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "outTradeNo": "TEST123456789",
    "body": "测试订单",
    "totalFee": 1
  }'
```

### 2. 支付流程测试

#### 2.1 沙箱环境测试
1. 使用微信支付沙箱环境
2. 配置沙箱商户号和密钥
3. 测试完整支付流程

#### 2.2 生产环境测试
1. 使用小额金额测试（如0.01元）
2. 验证支付成功和失败场景
3. 测试回调通知处理

### 3. 监控和日志

#### 3.1 日志配置
```yaml
logging:
  level:
    com.ruoyi.common.utils.wechat: DEBUG
    com.ruoyi.system.service.impl.WechatPayServiceImpl: INFO
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: /opt/app/logs/wechat-pay.log
    max-size: 100MB
    max-history: 30
```

#### 3.2 监控指标
- 支付成功率
- 支付响应时间
- 回调处理成功率
- 异常错误统计

## 安全注意事项

### 1. 密钥安全
- 私钥文件权限设置为600
- 定期轮换APIv3密钥
- 使用环境变量存储敏感信息
- 不要将密钥提交到代码仓库

### 2. 网络安全
- 使用HTTPS协议
- 配置防火墙规则
- 限制回调IP白名单
- 启用访问日志记录

### 3. 应用安全
- 验证回调签名
- 防止重复通知处理
- 限制支付金额范围
- 记录所有支付操作

## 故障排查

### 1. 常见问题

#### 1.1 签名验证失败
```bash
# 检查私钥文件
openssl rsa -in /opt/wechat-pay/certs/apiclient_key.pem -check -noout

# 检查证书序列号
openssl x509 -in apiclient_cert.pem -noout -serial

# 检查应用日志
grep -i "signature" /opt/app/logs/wechat-pay.log
```

#### 1.2 回调通知问题
```bash
# 检查回调URL可访问性
curl -I https://your-domain.com/api/wechat/pay/notify

# 检查Nginx日志
tail -f /var/log/nginx/access.log | grep notify

# 检查应用回调处理日志
grep -i "notify" /opt/app/logs/wechat-pay.log
```

#### 1.3 证书相关问题
```bash
# 检查证书文件权限
ls -la /opt/wechat-pay/certs/

# 检查证书有效期
openssl x509 -in apiclient_cert.pem -noout -dates

# 检查证书内容
openssl x509 -in apiclient_cert.pem -noout -text
```

### 2. 日志分析

#### 2.1 关键日志关键词
- `微信支付V3下单成功`：下单成功
- `签名验证失败`：签名问题
- `证书加载失败`：证书问题
- `回调通知处理成功`：回调成功

#### 2.2 性能监控
```bash
# 监控支付接口响应时间
grep "创建支付订单" /opt/app/logs/wechat-pay.log | awk '{print $1, $2, $NF}'

# 统计支付成功率
grep -c "支付成功" /opt/app/logs/wechat-pay.log
grep -c "支付失败" /opt/app/logs/wechat-pay.log
```

## 维护建议

### 1. 定期维护
- 每月检查证书有效期
- 每季度更新APIv3密钥
- 定期清理过期日志文件
- 监控支付成功率趋势

### 2. 备份策略
- 备份证书文件
- 备份配置文件
- 备份支付相关数据
- 制定灾难恢复计划

### 3. 升级计划
- 关注微信支付API更新
- 定期更新依赖库版本
- 测试新功能兼容性
- 制定平滑升级方案

通过以上配置和部署步骤，您可以成功部署微信支付V3版本功能。记住在生产环境部署前，务必在测试环境充分验证所有功能。
