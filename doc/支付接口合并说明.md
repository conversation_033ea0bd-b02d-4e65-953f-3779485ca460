# 支付接口合并说明

## 背景

系统中存在两个创建支付订单的接口：
1. `/payment/create` - 纯支付接口，只处理微信支付订单创建
2. `/order/create` - 完整的订单创建接口，包含订单创建和支付

小程序中调用的是 `/payment/create`，但该接口缺少订单业务逻辑，不会创建酒店订单记录，导致业务流程不完整。

## 合并方案

保留 `/order/create` 作为统一的创建订单并发起支付接口，废弃 `/payment/create` 接口。

## 变更内容

### 1. 前端变更

#### 1.1 小程序支付工具类 (galleno_miniprogram/utils/payment.js)
- 修改 `createPayment` 方法的接口调用地址：从 `/hotel/payment/create` 改为 `/hotel/order/create`
- 更新方法注释，说明现在需要传入完整的订单数据
- 参数格式从简单的支付参数改为完整的酒店订单数据

#### 1.2 API工具类 (galleno_miniprogram/utils/api.js)
- 移除 `payment.createPayment` 方法
- 保留 `payment.queryPayment` 方法用于查询支付状态
- 更新 `order.create` 方法的注释

#### 1.3 预订页面 (galleno_miniprogram/pages/booking/booking.js)
- 修改 `requestPayment` 方法，将参数格式从支付参数改为完整的订单创建参数
- 参数映射：
  - `guestName` <- `orderData.guestInfo.name`
  - `guestPhone` <- `orderData.guestInfo.phone`
  - `guestIdCard` <- `orderData.guestInfo.idCard`
  - 其他字段直接映射

### 2. 后端变更

#### 2.1 控制器 (ruoyi-admin/src/main/java/com/ruoyi/web/controller/api/HotelApiController.java)
- 移除 `createPayment` 方法（`/payment/create` 接口）
- 保留 `createOrder` 方法（`/order/create` 接口）
- 保留 `queryPayment` 方法（`/payment/query` 接口）

### 3. 文档更新

#### 3.1 微信支付下单功能使用说明.md
- 更新API接口说明，将创建支付订单接口改为创建酒店订单并发起支付接口
- 更新请求参数示例，使用完整的订单数据格式
- 更新响应示例，包含订单号和订单ID

#### 3.2 微信支付V3版本使用说明.md
- 更新小程序端调用示例，使用完整的订单数据格式

## 接口对比

### 原 /payment/create 接口
```json
{
    "outTradeNo": "HT202501170001",
    "body": "酒店预订-豪华大床房",
    "totalFee": 20000,
    "detail": "2024-03-16至2024-03-18，共2晚",
    "attach": "conferenceId:1,roomId:1"
}
```

### 新 /order/create 接口
```json
{
    "conferenceId": 1,
    "roomId": 1,
    "roomType": "豪华大床房",
    "roomName": "豪华大床房",
    "checkinDate": "2024-03-16",
    "checkoutDate": "2024-03-18",
    "nights": 2,
    "roomPrice": 300.00,
    "totalAmount": 600.00,
    "depositAmount": 200.00,
    "guestName": "张三",
    "guestPhone": "13800138000",
    "guestIdCard": "110101199001011234",
    "specialRequirements": "需要无烟房间",
    "remark": "会议期间住宿"
}
```

## 优势

1. **业务完整性**：统一接口包含完整的订单创建和支付流程
2. **数据一致性**：确保订单记录和支付记录的一致性
3. **维护简化**：减少接口数量，降低维护成本
4. **功能增强**：支持更丰富的订单信息和业务逻辑

## 注意事项

1. 前端调用方式发生变化，需要传入完整的订单数据
2. 响应数据增加了订单号和订单ID字段
3. 原有的 `/payment/create` 接口已被移除
4. 支付查询接口 `/payment/query` 保持不变

## 测试建议

1. 测试完整的订单创建和支付流程
2. 验证订单数据的正确性
3. 测试支付成功后的订单状态更新
4. 测试支付失败的异常处理
