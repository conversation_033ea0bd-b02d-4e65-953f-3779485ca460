# 微信支付V3版本回调验签功能说明

## 概述

本文档说明微信支付V3版本回调通知的签名验证功能实现。V3版本使用RSA-SHA256签名算法，相比V2版本的MD5签名更加安全。

## 实现功能

### 1. 签名验证流程

1. **提取验签参数**：从HTTP头部获取验签所需的参数
   - `Wechatpay-Timestamp`：时间戳
   - `Wechatpay-Nonce`：随机字符串
   - `Wechatpay-Signature`：微信签名
   - `Wechatpay-Serial`：微信平台证书序列号

2. **构建验签字符串**：按照微信规范构建验签字符串
   ```
   timestamp + "\n" + nonce + "\n" + body + "\n"
   ```

3. **获取微信平台公钥**：根据证书序列号获取对应的公钥

4. **执行RSA验签**：使用SHA256withRSA算法验证签名

### 2. 核心方法

#### WechatPayV3Utils.verifyNotifySignature()

```java
public boolean verifyNotifySignature(String timestamp, String nonce, 
    String body, String signature, String serialNumber)
```

**参数说明**：
- `timestamp`：微信回调的时间戳
- `nonce`：微信回调的随机字符串
- `body`：回调请求的原始JSON数据
- `signature`：微信的签名（Base64编码）
- `serialNumber`：微信平台证书序列号

**返回值**：
- `true`：验签成功
- `false`：验签失败

### 3. 证书管理

#### 证书缓存机制

- 使用`ConcurrentHashMap`缓存微信平台证书
- 避免重复获取证书，提高性能
- 支持多个证书序列号的并发访问

#### 证书获取方式

**开发环境**：
- 自动跳过验签（通过`spring.profiles.active`判断）
- 便于开发调试，无需配置真实证书

**生产环境**：
- 需要实现完整的证书获取逻辑
- 调用微信API获取平台证书
- 解密证书内容并缓存公钥

## 使用方法

### 1. 回调接口实现

在`WechatApiController`中：

```java
@PostMapping("/pay/notify")
public String payNotify(@RequestBody String requestBody, HttpServletRequest request) {
    try {
        // 提取验签头部信息
        Map<String, String> headers = new HashMap<>();
        headers.put("Wechatpay-Signature", request.getHeader("Wechatpay-Signature"));
        headers.put("Wechatpay-Timestamp", request.getHeader("Wechatpay-Timestamp"));
        headers.put("Wechatpay-Nonce", request.getHeader("Wechatpay-Nonce"));
        headers.put("Wechatpay-Serial", request.getHeader("Wechatpay-Serial"));

        return wechatPayService.handlePayNotify(requestBody, headers);
    } catch (Exception e) {
        return "{\"code\":\"FAIL\",\"message\":\"系统异常\"}";
    }
}
```

### 2. 服务层处理

在`WechatPayServiceImpl`中：

```java
@Override
public String handlePayNotify(String requestBody, Map<String, String> headers) {
    // 1. 验证签名
    boolean isSignatureValid = wechatPayV3Utils.verifyNotifySignature(
        headers.get("Wechatpay-Timestamp"),
        headers.get("Wechatpay-Nonce"),
        requestBody,
        headers.get("Wechatpay-Signature"),
        headers.get("Wechatpay-Serial")
    );

    if (!isSignatureValid) {
        return buildNotifyV3Response("FAIL", "签名验证失败");
    }

    // 2. 处理业务逻辑
    // ...
}
```

## 配置说明

### 1. 开发环境配置

在`application-dev.yml`中：

```yaml
spring:
  profiles:
    active: dev  # 开发环境自动跳过验签
```

### 2. 生产环境配置

在`application-prod.yml`中：

```yaml
spring:
  profiles:
    active: prod

wechat:
  pay:
    # 必须配置正确的证书信息
    mchId: 1234567890
    apiV3Key: your_32_character_api_v3_key
    privateKeyPath: /path/to/apiclient_key.pem
    merchantSerialNumber: YOUR_MERCHANT_SERIAL_NUMBER
```

## 安全注意事项

### 1. 验签的重要性

- **防止伪造**：确保回调通知确实来自微信
- **数据完整性**：验证数据在传输过程中未被篡改
- **防重放攻击**：结合时间戳验证防止重放攻击

### 2. 证书安全

- **私钥保护**：商户私钥文件权限设置为600
- **证书更新**：定期更新证书和APIv3密钥
- **环境隔离**：开发和生产环境使用不同的证书

### 3. 日志记录

- **验签结果**：记录每次验签的结果
- **异常信息**：记录验签失败的详细原因
- **敏感信息**：避免在日志中记录完整的签名和密钥

## 故障排查

### 1. 常见问题

#### 验签失败
- 检查HTTP头部信息是否完整
- 确认证书序列号是否正确
- 验证时间戳是否在有效范围内

#### 证书问题
- 检查商户私钥文件路径和权限
- 确认APIv3密钥配置正确
- 验证证书序列号格式

### 2. 调试方法

#### 开启调试日志
```yaml
logging:
  level:
    com.ruoyi.common.utils.wechat.WechatPayV3Utils: DEBUG
    com.ruoyi.system.service.impl.WechatPayServiceImpl: DEBUG
```

#### 查看验签详情
```bash
# 查看验签相关日志
grep -i "验签" /path/to/logs/application.log

# 查看微信回调日志
grep -i "微信支付V3回调" /path/to/logs/application.log
```

## 后续优化

### 1. 完整证书管理

- 实现自动获取微信平台证书
- 添加证书过期检查和自动更新
- 支持证书轮换机制

### 2. 性能优化

- 优化证书缓存策略
- 添加证书预加载机制
- 实现异步证书更新

### 3. 监控告警

- 添加验签成功率监控
- 设置证书过期告警
- 监控回调处理性能

通过以上实现，微信支付V3版本的回调验签功能已经基本完成，可以有效保证支付回调的安全性和可靠性。
