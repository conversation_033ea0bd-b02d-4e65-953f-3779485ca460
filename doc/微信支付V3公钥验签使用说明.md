# 微信支付V3版本公钥验签使用说明

## 概述

本文档说明如何使用微信平台公钥直接进行微信支付V3版本回调通知的签名验证，无需复杂的证书管理。

## 实现方式

### 1. 直接使用公钥验签

相比使用平台证书的方式，直接使用公钥验签具有以下优势：
- **简化配置**：无需管理复杂的证书文件
- **易于维护**：公钥相对稳定，更新频率较低
- **部署简单**：只需配置公钥字符串或文件路径

### 2. 支持的配置方式

#### 方式一：配置公钥文件路径
```yaml
wechat:
  pay:
    # 微信平台公钥文件路径
    wechatPlatformPublicKeyPath: /path/to/wechat_platform_public_key.pem
```

#### 方式二：直接配置公钥字符串
```yaml
wechat:
  pay:
    # 微信平台公钥字符串（PEM格式）
    wechatPlatformPublicKey: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END PUBLIC KEY-----
```

## 获取微信平台公钥

### 1. 从微信商户平台获取

1. 登录[微信商户平台](https://pay.weixin.qq.com/)
2. 进入"账户中心" → "API安全"
3. 下载"平台证书"
4. 使用OpenSSL提取公钥：

```bash
# 从平台证书中提取公钥
openssl x509 -pubkey -noout -in wechatpay_platform_cert.pem > wechat_platform_public_key.pem
```

### 2. 通过API获取（推荐）

```bash
# 调用微信API获取平台证书
curl -X GET \
  'https://api.mch.weixin.qq.com/v3/certificates' \
  -H 'Authorization: WECHATPAY2-SHA256-RSA2048 ...' \
  -H 'Accept: application/json'
```

## 配置示例

### 完整配置示例

```yaml
# 微信配置
wechat:
  miniapp:
    appId: wx1234567890abcdef
    appSecret: abcdef1234567890abcdef1234567890ab
    tokenCacheTime: 7200
  
  pay:
    # 商户号
    mchId: 1234567890
    
    # APIv3密钥
    apiV3Key: your_32_character_api_v3_key
    
    # 商户私钥文件路径
    privateKeyPath: /path/to/apiclient_key.pem
    
    # 商户证书序列号
    merchantSerialNumber: YOUR_MERCHANT_SERIAL_NUMBER
    
    # 微信平台公钥配置（二选一）
    wechatPlatformPublicKeyPath: /path/to/wechat_platform_public_key.pem
    # wechatPlatformPublicKey: |
    #   -----BEGIN PUBLIC KEY-----
    #   MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
    #   -----END PUBLIC KEY-----
    
    # 回调配置
    notifyUrl: https://your-domain.com/api/wechat/pay/notify
    timeoutMinutes: 30
```

## 验签流程

### 1. 自动验签

系统会自动进行以下验签流程：

1. **提取验签参数**
   - 从HTTP头部获取时间戳、随机数、签名等
   
2. **构建验签字符串**
   ```
   timestamp + "\n" + nonce + "\n" + body + "\n"
   ```

3. **加载公钥**
   - 优先从配置的公钥字符串加载
   - 其次从公钥文件路径加载
   - 支持公钥缓存，避免重复加载

4. **执行RSA验签**
   - 使用SHA256withRSA算法
   - 验证微信签名的有效性

### 2. 环境适配

- **开发环境**：自动跳过验签，便于调试
- **测试环境**：自动跳过验签，便于测试
- **生产环境**：执行完整验签流程

## 代码使用

### 1. 验签方法调用

```java
// 在WechatPayServiceImpl中
boolean isSignatureValid = wechatPayV3Utils.verifyNotifySignature(
    timestamp, nonce, requestBody, signature, serialNumber);

if (!isSignatureValid) {
    return buildNotifyV3Response("FAIL", "签名验证失败");
}
```

### 2. 动态设置公钥

```java
// 动态设置公钥（运行时）
wechatPayV3Utils.setWechatPlatformPublicKey(publicKeyString);

// 重新加载公钥
wechatPayV3Utils.reloadWechatPlatformPublicKey();
```

## 公钥文件格式

### 标准PEM格式

```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyWGaggb2BvfnOoXuJaAG
XU5is1XmSFXEg+FqidtsaQXQYSXwVhtrAyTtNP1N6+83hu8cJVxdnhcCr6VxyuUF
...更多公钥内容...
wIDAQAB
-----END PUBLIC KEY-----
```

### 注意事项

1. **格式要求**：必须是标准的PEM格式
2. **编码方式**：使用UTF-8编码
3. **文件权限**：建议设置为600（仅所有者可读写）
4. **路径配置**：使用绝对路径，避免相对路径问题

## 安全建议

### 1. 公钥管理

- **定期更新**：关注微信平台公钥的更新通知
- **备份存储**：保存公钥的备份文件
- **权限控制**：限制公钥文件的访问权限

### 2. 配置安全

- **环境隔离**：不同环境使用不同的配置
- **敏感信息**：避免在日志中输出完整公钥
- **版本控制**：不要将公钥文件提交到代码仓库

### 3. 监控告警

- **验签失败率**：监控验签失败的频率
- **公钥有效性**：定期验证公钥的有效性
- **异常告警**：设置验签异常的告警机制

## 故障排查

### 1. 常见问题

#### 验签失败
```
微信支付V3回调验签失败：签名验证不通过
```

**排查步骤**：
1. 检查公钥配置是否正确
2. 验证公钥文件格式是否标准
3. 确认公钥是否为最新版本
4. 检查验签字符串构建是否正确

#### 公钥加载失败
```
无法获取微信平台公钥
```

**排查步骤**：
1. 检查公钥文件路径是否存在
2. 验证文件权限是否正确
3. 确认公钥格式是否符合PEM标准
4. 检查配置项是否正确设置

### 2. 调试方法

#### 开启调试日志
```yaml
logging:
  level:
    com.ruoyi.common.utils.wechat.WechatPayV3Utils: DEBUG
```

#### 验证公钥格式
```bash
# 验证公钥文件格式
openssl rsa -pubin -in wechat_platform_public_key.pem -text -noout
```

## 总结

使用公钥进行微信支付V3版本回调验签具有以下优势：

✅ **配置简单**：只需配置公钥字符串或文件路径
✅ **维护方便**：无需复杂的证书管理流程
✅ **部署容易**：支持多种配置方式，适应不同部署环境
✅ **性能优化**：公钥缓存机制，避免重复加载
✅ **安全可靠**：使用RSA-SHA256算法，确保验签安全性

这种实现方式既保证了安全性，又简化了配置和维护工作，是微信支付V3版本验签的推荐方案。
