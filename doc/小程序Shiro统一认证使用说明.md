# 小程序Shiro统一认证使用说明

## 概述

本文档介绍了若依框架中小程序用户通过Shiro框架进行统一登录验证的实现方案。该方案避免了在每个接口中硬编码验证逻辑，实现了统一的认证管理。

## 功能特性

- ✅ 基于JWT的小程序token生成和验证
- ✅ Shiro框架统一认证管理
- ✅ 支持多Realm认证（Web用户 + 小程序用户）
- ✅ 自动token有效期控制和刷新
- ✅ 认证失败统一JSON错误响应
- ✅ 接口代码无需手动验证token

## 核心组件

### 1. MiniAppTokenUtils - 小程序Token工具类

**位置**: `ruoyi-common/src/main/java/com/ruoyi/common/utils/wechat/MiniAppTokenUtils.java`

**主要功能**:
- `generateToken(String openid, Long userId)` - 生成JWT token
- `validateToken(String token)` - 验证token有效性
- `refreshToken(String oldToken)` - 刷新token
- `removeToken(String openid)` - 删除token
- `hasValidToken(String openid)` - 检查token是否存在

**配置参数**:
- token有效期：默认2小时（7200秒）
- 刷新阈值：默认30分钟（剩余30分钟时可刷新）
- 密钥：`miniapp_secret_key_2025`

### 2. MiniAppToken - 小程序认证Token

**位置**: `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/token/MiniAppToken.java`

实现了Shiro的`AuthenticationToken`接口，包含：
- `token` - JWT token字符串
- `openid` - 用户openid
- `userId` - 用户ID

### 3. MiniAppRealm - 小程序认证Realm

**位置**: `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/realm/MiniAppRealm.java`

继承`AuthorizingRealm`，负责：
- 验证MiniAppToken
- 调用MiniAppTokenUtils验证token
- 查询用户信息
- 自动刷新即将过期的token
- 设置小程序用户权限

**默认权限**:
- 角色：`miniapp_user`
- 权限：`miniapp:user:view`, `miniapp:hotel:view`, `miniapp:hotel:book`

### 4. MiniAppAuthFilter - 小程序认证过滤器

**位置**: `ruoyi-framework/src/main/java/com/ruoyi/framework/shiro/web/filter/miniapp/MiniAppAuthFilter.java`

继承`AuthenticatingFilter`，负责：
- 从请求头`Authorization: Bearer <token>`获取token
- 从请求参数`token`获取token
- 创建MiniAppToken进行认证
- 认证失败返回401状态码和JSON错误信息

## 配置说明

### 1. Shiro配置修改

在`ShiroConfig.java`中已完成以下配置：

```java
// 添加小程序Realm
@Bean
public MiniAppRealm miniAppRealm(EhCacheManager cacheManager) {
    MiniAppRealm miniAppRealm = new MiniAppRealm();
    miniAppRealm.setAuthorizationCacheName("miniapp_auth_cache");
    miniAppRealm.setCacheManager(cacheManager);
    return miniAppRealm;
}

// 支持多Realm
@Bean
public SecurityManager securityManager(UserRealm userRealm, MiniAppRealm miniAppRealm) {
    DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
    securityManager.setRealms(Arrays.asList(userRealm, miniAppRealm));
    // ...
}

// 添加小程序认证过滤器
public MiniAppAuthFilter miniAppAuthFilter() {
    return new MiniAppAuthFilter();
}

// 配置过滤器链
filterChainDefinitionMap.put("/api/wechat/login", "anon");
filterChainDefinitionMap.put("/api/wechat/loginWithUserInfo", "anon");
filterChainDefinitionMap.put("/api/hotel/**", "miniAppAuth");
```

### 2. 依赖配置

在`ruoyi-common/pom.xml`中已添加JWT依赖：

```xml
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt</artifactId>
    <version>0.9.1</version>
</dependency>
```

## 使用方式

### 1. 小程序登录流程

1. 小程序调用`wx.login()`获取`jsCode`
2. 调用`/api/wechat/login`接口登录
3. 后端验证jsCode，创建/更新用户
4. 生成JWT token并返回
5. 小程序保存token用于后续请求

**登录接口响应**:
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "newUser": false,
        "user": {
            "id": 1,
            "nickName": "张三",
            "openid": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX"
        },
        "token": "eyJhbGciOiJIUzUxMiJ9.eyJvcGVuaWQiOiJvWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYIiwidXNlcklkIjoxLCJ0eXBlIjoibWluaWFwcCIsImlhdCI6MTY0MjU4NzYwMCwiZXhwIjoxNjQyNTk0ODAwfQ.xxx"
    }
}
```

### 2. 小程序API调用

**方式一：Authorization请求头（推荐）**
```javascript
wx.request({
    url: 'https://your-domain.com/api/hotel/validateCode',
    method: 'POST',
    header: {
        'Authorization': 'Bearer ' + token
    },
    data: {
        categoryId: 'ABC123',
        conferenceId: 1
    }
});
```

**方式二：请求参数**
```javascript
wx.request({
    url: 'https://your-domain.com/api/hotel/validateCode',
    method: 'POST',
    data: {
        token: token,
        categoryId: 'ABC123',
        conferenceId: 1
    }
});
```

### 3. 后端接口开发

在Controller中无需手动验证token，直接使用Shiro获取当前用户：

```java
@PostMapping("/validateCode")
public AjaxResult validateCode(@RequestParam String categoryId, @RequestParam Long conferenceId) {
    // 获取当前登录用户（Shiro已自动验证token）
    User currentUser = getCurrentUser();
    if (currentUser == null) {
        return AjaxResult.error("用户未登录");
    }
    
    // 业务逻辑处理
    // ...
    
    return AjaxResult.success("验证成功", result);
}

private User getCurrentUser() {
    Subject subject = SecurityUtils.getSubject();
    if (subject.isAuthenticated()) {
        Object principal = subject.getPrincipal();
        if (principal instanceof User) {
            return (User) principal;
        }
    }
    return null;
}
```

## 接口路径配置

### 匿名访问（无需认证）
- `/api/wechat/login` - 小程序登录
- `/api/wechat/loginWithUserInfo` - 带用户信息登录

### 小程序认证访问
- `/api/hotel/**` - 所有酒店预订相关接口

## 错误处理

### 认证失败响应
当token验证失败时，系统自动返回：

```json
{
    "code": 401,
    "msg": "认证失败：token验证失败：token已失效"
}
```

### 常见错误码
- `token不能为空` - 请求中未包含token
- `token格式不正确` - token格式错误
- `token已失效` - token过期或被删除
- `用户不存在` - token中的用户不存在
- `用户已删除` - 用户状态异常

## 测试接口

为了方便测试，提供了以下测试接口：

### 验证token
```
POST /api/wechat/validateToken
参数：token - 待验证的token
```

### 刷新token
```
POST /api/wechat/refreshToken
参数：token - 待刷新的token
```

## 安全特性

1. **JWT签名验证**：使用HS512算法签名，防止token篡改
2. **token缓存管理**：服务端缓存token，支持主动失效
3. **自动刷新机制**：剩余时间少于30分钟时自动刷新
4. **权限控制**：基于Shiro的细粒度权限控制
5. **统一错误处理**：认证失败统一返回JSON格式错误信息

## 版本信息

- **创建时间**：2025-07-24
- **版本**：1.0.0
- **适用范围**：若依框架 + 微信小程序
- **依赖版本**：
  - Spring Boot 2.5.15
  - Apache Shiro 1.13.0
  - JJWT 0.9.1

## 相关文档

- [小程序登录接口使用说明](./小程序登录接口使用说明.md)
- [微信小程序SessionKey安全管理说明](./微信小程序SessionKey安全管理说明.md)
- [Apache Shiro官方文档](https://shiro.apache.org/documentation.html)
