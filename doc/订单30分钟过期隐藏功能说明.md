# 订单30分钟过期隐藏功能说明

## 功能描述
在小程序个人页面和订单列表页面中，超过30分钟未支付的订单将不再展示给用户，以提升用户体验和减少界面混乱。

## 修改内容

### 1. 后端API修改

#### 文件：`ruoyi-admin/src/main/java/com/ruoyi/web/controller/api/HotelApiController.java`

在 `getOrderList` 方法中添加了过滤逻辑：

```java
// 过滤掉超过30分钟未支付的订单（不展示给用户）
List<HotelOrder> filteredOrderList = orderList.stream()
    .filter(order -> {
        // 如果不是待支付状态，直接显示
        if (!"PENDING".equals(order.getOrderStatus())) {
            return true;
        }
        
        // 如果是待支付状态，检查是否超过30分钟
        if (order.getCreateTime() != null) {
            long createTimeMillis = order.getCreateTime().getTime();
            long currentTimeMillis = System.currentTimeMillis();
            long diffMinutes = (currentTimeMillis - createTimeMillis) / (1000 * 60);
            
            // 超过30分钟的待支付订单不显示
            return diffMinutes <= 30;
        }
        
        // 如果没有创建时间，为了安全起见不显示
        return false;
    })
    .collect(java.util.stream.Collectors.toList());
```

### 2. 小程序端修改

#### 文件：`galleno_miniprogram/pages/order-list/order-list.js`

在 `processOrderData` 和 `loadLocalOrderData` 方法中添加了相同的过滤逻辑：

```javascript
// 过滤掉超过30分钟未支付的订单（小程序端双重保障）
const filteredOrders = processedOrders.filter(order => {
  // 如果不是待支付状态，直接显示
  if (order.status !== 'pending') {
    return true;
  }
  
  // 如果是待支付状态，检查是否超过30分钟
  if (order.createTime) {
    const createTime = new Date(order.createTime).getTime();
    const currentTime = new Date().getTime();
    const diffMinutes = (currentTime - createTime) / (1000 * 60);
    
    // 超过30分钟的待支付订单不显示
    return diffMinutes <= 30;
  }
  
  // 如果没有创建时间，为了安全起见不显示
  return false;
});
```

#### 文件：`galleno_miniprogram/pages/profile/profile.js`

在 `processOrderData` 和 `loadLocalBookingData` 方法中添加了相同的过滤逻辑。

## 实现原理

1. **时间计算**：通过比较订单创建时间和当前时间，计算出时间差（分钟）
2. **状态判断**：只对状态为 "PENDING"（待支付）的订单进行时间检查
3. **双重保障**：后端API和小程序端都实现了过滤逻辑，确保数据一致性
4. **安全处理**：对于没有创建时间的订单，为了安全起见选择不显示

## 影响范围

- ✅ 小程序订单列表页面
- ✅ 小程序个人页面的最近订单显示
- ✅ 后端订单列表API接口
- ❌ 管理后台（管理员仍可查看所有订单）
- ❌ 数据库（订单数据不会被删除，只是不展示）

## 注意事项

1. **数据保留**：订单数据在数据库中仍然保留，只是在用户界面不显示
2. **管理后台**：管理员在后台仍可以查看和管理所有订单，包括超时的待支付订单
3. **本地缓存**：小程序本地缓存的订单数据也会被过滤
4. **实时性**：每次加载订单列表时都会重新计算时间，确保过滤的准确性

## 测试建议

1. 创建一个测试订单，等待30分钟后检查是否在列表中消失
2. 验证已支付、已取消等其他状态的订单不受影响
3. 测试小程序刷新和重新进入页面的情况
4. 确认管理后台仍能正常查看所有订单
