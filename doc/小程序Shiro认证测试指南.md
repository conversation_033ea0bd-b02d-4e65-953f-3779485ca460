# 小程序Shiro认证测试指南

## 测试环境准备

### 1. 启动项目
确保若依项目已正常启动，访问地址：`http://localhost`

### 2. 配置微信小程序参数
在`application.yml`中配置正确的微信小程序AppID和AppSecret：

```yaml
wechat:
  miniapp:
    appId: your_mini_app_id
    appSecret: your_mini_app_secret
    tokenCacheTime: 7200
```

## 测试步骤

### 步骤1：小程序登录获取token

**接口**: `POST /api/wechat/login`

**请求参数**:
```json
{
    "jsCode": "小程序登录获取的code"
}
```

**预期响应**:
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "newUser": true,
        "user": {
            "id": 1,
            "nickName": "微信用户",
            "openid": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX"
        },
        "token": "eyJhbGciOiJIUzUxMiJ9.eyJvcGVuaWQiOiJvWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYIiwidXNlcklkIjoxLCJ0eXBlIjoibWluaWFwcCIsImlhdCI6MTY0MjU4NzYwMCwiZXhwIjoxNjQyNTk0ODAwfQ.xxx"
    }
}
```

**保存token**：将返回的token保存，用于后续接口调用。

### 步骤2：验证token有效性

**接口**: `POST /api/wechat/validateToken`

**请求参数**:
```json
{
    "token": "步骤1获取的token"
}
```

**预期响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "valid": true,
        "message": "验证成功",
        "openid": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
        "userId": 1,
        "needRefresh": false
    }
}
```

### 步骤3：测试酒店接口认证

#### 3.1 不带token访问（应该失败）

**接口**: `POST /api/hotel/validateCode`

**请求参数**:
```json
{
    "categoryId": "TEST123",
    "conferenceId": 1
}
```

**预期响应**:
```json
{
    "code": 401,
    "msg": "认证失败：未找到认证token"
}
```

#### 3.2 带token访问（应该成功）

**接口**: `POST /api/hotel/validateCode`

**请求头**:
```
Authorization: Bearer 步骤1获取的token
```

**请求参数**:
```json
{
    "categoryId": "TEST123",
    "conferenceId": 1
}
```

**预期响应**:
```json
{
    "code": 200,
    "msg": "验证成功",
    "data": {
        // 识别码验证结果
    }
}
```

或者使用参数方式：

**请求参数**:
```json
{
    "token": "步骤1获取的token",
    "categoryId": "TEST123",
    "conferenceId": 1
}
```

### 步骤4：测试token刷新

**接口**: `POST /api/wechat/refreshToken`

**请求参数**:
```json
{
    "token": "步骤1获取的token"
}
```

**预期响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": "新的token字符串"
}
```

### 步骤5：测试无效token

#### 5.1 使用错误的token

**接口**: `POST /api/hotel/validateCode`

**请求头**:
```
Authorization: Bearer invalid_token_string
```

**预期响应**:
```json
{
    "code": 401,
    "msg": "认证失败：token验证失败：JWT signature does not match locally computed signature"
}
```

#### 5.2 使用过期的token

等待token过期（默认2小时）或手动删除token缓存后测试：

**预期响应**:
```json
{
    "code": 401,
    "msg": "认证失败：token验证失败：token已失效"
}
```

## 使用Postman测试

### 1. 创建Collection
创建名为"小程序Shiro认证测试"的Collection

### 2. 设置环境变量
- `base_url`: `http://localhost`
- `token`: 登录后获取的token

### 3. 测试用例配置

#### 登录接口
- **Method**: POST
- **URL**: `{{base_url}}/api/wechat/login`
- **Body**: 
  ```json
  {
      "jsCode": "test_js_code"
  }
  ```
- **Tests脚本**:
  ```javascript
  if (pm.response.code === 200) {
      var jsonData = pm.response.json();
      if (jsonData.data && jsonData.data.token) {
          pm.environment.set("token", jsonData.data.token);
      }
  }
  ```

#### 酒店接口认证测试
- **Method**: POST
- **URL**: `{{base_url}}/api/hotel/validateCode`
- **Headers**: 
  - `Authorization`: `Bearer {{token}}`
- **Body**:
  ```json
  {
      "categoryId": "TEST123",
      "conferenceId": 1
  }
  ```

## 常见问题排查

### 1. 登录失败
- 检查微信小程序配置是否正确
- 确认jsCode是否有效（5分钟有效期）
- 查看后端日志确认错误原因

### 2. token验证失败
- 确认token格式正确
- 检查token是否过期
- 验证请求头格式：`Authorization: Bearer <token>`

### 3. 接口返回401
- 确认接口路径是否配置了`miniAppAuth`过滤器
- 检查token是否正确传递
- 查看Shiro配置是否正确

### 4. 用户信息获取失败
- 确认用户是否存在于数据库
- 检查用户状态是否正常
- 验证openid是否匹配

## 日志查看

### 1. 开启调试日志
在`application.yml`中设置：
```yaml
logging:
  level:
    com.ruoyi.framework.shiro: debug
    com.ruoyi.common.utils.wechat: debug
```

### 2. 关键日志信息
- 小程序登录成功：`小程序登录成功，openid：xxx，用户ID：xxx`
- Token生成成功：`生成token成功，openid：xxx，userId：xxx`
- Token验证成功：`验证token成功，openid：xxx，userId：xxx`
- 小程序认证成功：`小程序认证成功，请求路径：xxx`
- 小程序认证失败：`小程序认证失败：xxx，请求路径：xxx`

## 性能测试

### 1. 并发登录测试
使用JMeter或其他工具测试并发登录性能

### 2. Token验证性能
测试高并发下token验证的性能表现

### 3. 缓存性能
监控token缓存的命中率和性能

## 安全测试

### 1. Token篡改测试
尝试修改token内容，验证签名验证是否有效

### 2. 重放攻击测试
使用相同的jsCode多次登录，验证防重放机制

### 3. 权限测试
验证不同用户是否只能访问自己有权限的资源

## 测试报告模板

```
测试时间：2025-07-24
测试环境：开发环境
测试版本：1.0.0

测试结果：
✅ 小程序登录功能正常
✅ Token生成和验证正常
✅ Shiro认证过滤器正常
✅ 接口权限控制正常
✅ 错误处理机制正常
✅ Token刷新功能正常

发现问题：
- 无

建议：
- 建议在生产环境中使用更复杂的JWT密钥
- 建议添加更多的安全验证机制
```
