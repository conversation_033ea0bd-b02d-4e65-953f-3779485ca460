# 小程序登录接口使用说明

## 概述

本文档介绍了新增的微信小程序登录接口的使用方法。该接口实现了通过openid判断用户是否存在，如果不存在则在`user`表中新增用户数据的功能。

## 功能特性

- ✅ 微信小程序登录（通过jsCode获取openid）
- ✅ 自动用户注册（openid不存在时自动创建用户）
- ✅ 用户信息更新（支持昵称、性别等信息）
- ✅ 登录状态管理
- ✅ 完善的错误处理和日志记录
- ✅ 支持unionid绑定
- ✅ 区分新用户和老用户

## 数据库表结构

### user表字段说明

```sql
CREATE TABLE `user` (
    `id` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
    `nick_name` VARCHAR(64) COMMENT '用户昵称',
    `real_name` VARCHAR(12) COMMENT '真实姓名',
    `phone_number` VARCHAR(18) COMMENT '手机号',
    `gender` CHAR(1) COMMENT '用户性别',
    `openid` VARCHAR(64) COMMENT '微信openid',
    `unionid` VARCHAR(64) COMMENT '用户在开放平台的唯一标识符',
    `email` VARCHAR(64) COMMENT '邮箱',
    `company` VARCHAR(32) COMMENT '用户所在公司',
    `position` VARCHAR(32) COMMENT '职位',
    `id_card` VARCHAR(18) COMMENT '身份号',
    `reg_source` VARCHAR(12) COMMENT '注册来源',
    `create_time` DATETIME COMMENT '创建时间',
    `update_time` DATETIME COMMENT '更新时间',
    PRIMARY KEY(`id`)
) COMMENT='用户表';
```

## API接口说明

### 1. 基础登录接口

**接口地址：** `POST /api/wechat/login`

**请求参数：**
```json
{
    "jsCode": "小程序登录时获取的code"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "newUser": true,
        "user": {
            "id": 1,
            "nickName": "微信用户",
            "openid": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
            "unionid": "uXXXXXXXXXXXXXXXXXXXXXXXXXXX",
            "regSource": "miniapp",
            "createTime": "2025-07-23 10:30:00"
        },
        "token": null
    }
}
```

**注意：** 为了安全考虑，`sessionKey` 不会下发到客户端，而是安全存储在服务端，仅供服务端内部使用。

### 2. 带用户信息的登录接口

**接口地址：** `POST /api/wechat/loginWithUserInfo`

**请求参数：**
```json
{
    "jsCode": "小程序登录时获取的code",
    "nickName": "用户昵称",
    "avatarUrl": "用户头像URL",
    "gender": "1"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "newUser": false,
        "user": {
            "id": 1,
            "nickName": "张三",
            "gender": "1",
            "openid": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
            "unionid": "uXXXXXXXXXXXXXXXXXXXXXXXXXXX",
            "regSource": "miniapp",
            "createTime": "2025-07-23 10:30:00",
            "updateTime": "2025-07-23 11:00:00"
        },
        "token": null
    }
}
```

### 3. 获取微信用户信息接口（调试用）

**接口地址：** `POST /api/wechat/getUserInfo`

**请求参数：**
```json
{
    "jsCode": "小程序登录时获取的code"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "获取用户信息成功",
    "data": {
        "openId": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
        "unionId": "uXXXXXXXXXXXXXXXXXXXXXXXXXXX",
        "sessionKey": "session_key_from_wechat"
    }
}
```

## 登录流程说明

### 1. 用户首次登录流程

1. 小程序调用 `wx.login()` 获取 `jsCode`
2. 调用 `/api/wechat/login` 接口，传入 `jsCode`
3. 后端调用微信接口，通过 `jsCode` 获取 `openid` 和 `sessionKey`
4. 根据 `openid` 查询 `user` 表，发现用户不存在
5. 创建新用户记录，设置以下字段：
   - `openid`: 微信openid
   - `unionid`: 微信unionid（如果有）
   - `nick_name`: "微信用户"（默认昵称）
   - `reg_source`: "miniapp"
   - `create_time`: 当前时间
6. 返回登录结果，`isNewUser` 为 `true`

### 2. 用户再次登录流程

1. 小程序调用 `wx.login()` 获取 `jsCode`
2. 调用 `/api/wechat/login` 接口，传入 `jsCode`
3. 后端调用微信接口，通过 `jsCode` 获取 `openid` 和 `sessionKey`
4. 根据 `openid` 查询 `user` 表，找到已存在的用户
5. 更新用户的 `update_time` 字段
6. 返回登录结果，`isNewUser` 为 `false`

## 核心类说明

### 1. IMiniAppLoginService - 小程序登录服务接口

主要方法：
- `login(String jsCode)` - 基础登录
- `loginWithUserInfo(String jsCode, String nickName, String avatarUrl, String gender)` - 带用户信息登录

### 2. MiniAppLoginResult - 登录结果封装类

主要字段：
- `isNewUser` - 是否为新用户
- `user` - 用户信息
- `sessionKey` - 微信会话密钥
- `token` - 登录token（预留字段）

### 3. User - 用户实体类

对应 `user` 表的实体类，包含用户的基本信息。

### 4. WechatSessionManager - SessionKey安全管理器

用于服务端安全存储和管理用户的sessionKey，主要功能：
- `storeSessionKey(String openid, String sessionKey)` - 存储用户的sessionKey
- `getSessionKey(String openid)` - 获取用户的sessionKey（仅供服务端使用）
- `hasValidSessionKey(String openid)` - 检查用户是否有有效的sessionKey
- `cleanExpiredSessions()` - 清理过期的sessionKey

## 小程序端使用示例

### 1. 基础登录

```javascript
// 小程序端代码
wx.login({
  success: function(res) {
    if (res.code) {
      // 调用后端登录接口
      wx.request({
        url: 'https://your-domain.com/api/wechat/login',
        method: 'POST',
        data: {
          jsCode: res.code
        },
        success: function(response) {
          if (response.data.code === 200) {
            const loginResult = response.data.data;
            
            // 保存用户信息（注意：sessionKey已安全存储在服务端，不会下发到客户端）
            wx.setStorageSync('userInfo', loginResult.user);
            
            if (loginResult.isNewUser) {
              console.log('欢迎新用户！');
              // 可以引导用户完善个人信息
            } else {
              console.log('欢迎回来！');
            }
          } else {
            console.error('登录失败：', response.data.msg);
          }
        }
      });
    }
  }
});
```

### 2. 带用户信息登录

```javascript
// 小程序端代码
wx.login({
  success: function(res) {
    if (res.code) {
      // 获取用户信息
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: function(userRes) {
          // 调用后端登录接口
          wx.request({
            url: 'https://your-domain.com/api/wechat/loginWithUserInfo',
            method: 'POST',
            data: {
              jsCode: res.code,
              nickName: userRes.userInfo.nickName,
              avatarUrl: userRes.userInfo.avatarUrl,
              gender: userRes.userInfo.gender.toString()
            },
            success: function(response) {
              if (response.data.code === 200) {
                const loginResult = response.data.data;
                console.log('登录成功：', loginResult);
              }
            }
          });
        }
      });
    }
  }
});
```

## 错误处理

### 常见错误码

- `jsCode不能为空` - 请求参数缺失
- `登录失败` - 微信接口调用失败或用户创建失败
- `登录异常` - 系统异常

### 日志记录

系统会记录以下关键日志：
- 用户登录成功/失败
- 新用户创建
- 用户信息更新
- 微信接口调用结果

## SessionKey安全管理

### 安全设计原则

为了保护用户隐私和数据安全，本系统采用了以下安全设计：

1. **sessionKey不下发**：sessionKey绝不会下发到客户端，只在服务端安全存储
2. **内存缓存管理**：sessionKey存储在服务端内存中，支持过期时间管理
3. **自动清理机制**：定时任务自动清理过期的sessionKey
4. **访问控制**：只有服务端内部方法可以访问sessionKey

### SessionKey管理API（仅供管理员使用）

#### 检查用户sessionKey状态
```
GET /api/wechat/checkSessionKey?openid=xxx
```

#### 获取sessionKey缓存统计
```
GET /api/wechat/getSessionStats
```

#### 手动清理过期sessionKey
```
POST /api/wechat/cleanExpiredSessions
```

## 框架配置

### Shiro安全框架配置

小程序API接口已配置为匿名访问，不会被Shiro框架拦截：

```java
// 在ShiroConfig.java中配置
filterChainDefinitionMap.put("/api/wechat/**", "anon");
filterChainDefinitionMap.put("/api/hotel/**", "anon");
```

### CSRF和XSS保护配置

API接口已排除在CSRF和XSS过滤之外：

```yaml
# application.yml配置
xss:
  excludes: /system/notice/*,/api/**
csrf:
  whites: /druid,/api/**
```

## 安全注意事项

1. **jsCode有效期**：jsCode只能使用一次，且有效期为5分钟
2. **sessionKey安全**：sessionKey仅在服务端存储，用于解密用户敏感数据，绝不下发到客户端
3. **openid唯一性**：openid是用户在当前小程序下的唯一标识
4. **unionid绑定**：如果有多个小程序，可以通过unionid关联用户
5. **数据解密**：如需解密用户手机号等敏感数据，使用服务端存储的sessionKey
6. **框架拦截**：小程序API接口已正确配置，不会被Shiro框架拦截

## 扩展功能建议

1. **用户头像存储**：可以在User实体类中添加avatarUrl字段
2. **登录token生成**：可以集成JWT生成登录token
3. **用户权限管理**：可以为小程序用户分配特定角色
4. **登录日志记录**：记录用户登录历史
5. **用户信息完善**：引导用户补充手机号、邮箱等信息

## 版本信息

- 创建时间：2025-07-23
- 版本：1.0.0
- 兼容性：若依框架 4.8.1+
- 依赖：微信小程序工具类
