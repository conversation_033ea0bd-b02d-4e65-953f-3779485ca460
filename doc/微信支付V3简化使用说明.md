# 微信支付V3版本简化使用说明

## 概述

本项目已完全升级到微信支付V3版本，移除了所有V2版本代码。V3版本提供更安全的RSA签名、JSON数据格式和更丰富的功能特性。

## 快速开始

### 1. 配置文件设置

在 `application.yml` 中配置微信支付参数：

```yaml
wechat:
  miniapp:
    appId: wx80ecd969d8dc4f6d
    appSecret: 780d9b814535e16b2b67fbd7f75fb9bf
  pay:
    mchId: 1724178853
    apiV3Key: your_api_v3_key_32_characters_long
    privateKeyPath: /path/to/your/apiclient_key.pem
    merchantSerialNumber: your_merchant_serial_number_here
    notifyUrl: https://your-domain.com/api/wechat/pay/notify
    timeoutMinutes: 30
```

### 2. 证书配置

1. 从微信商户平台下载API证书
2. 提取私钥文件 `apiclient_key.pem`
3. 设置文件权限：`chmod 600 apiclient_key.pem`
4. 配置正确的文件路径

### 3. API接口使用

#### 创建支付订单

```http
POST /api/hotel/payment/create
Content-Type: application/json
Authorization: Bearer {token}

{
    "outTradeNo": "HT202501170001",
    "body": "酒店预订-豪华大床房",
    "totalFee": 20000,
    "detail": "2024-03-16至2024-03-18，共2晚",
    "attach": "conferenceId:1,roomId:1"
}
```

#### 查询支付状态

```http
GET /api/hotel/payment/query?outTradeNo=HT202501170001
Authorization: Bearer {token}
```

### 4. 小程序端调用

```javascript
// 使用封装好的支付工具类
const paymentData = {
    outTradeNo: 'HT202501170001',
    body: '酒店预订-豪华大床房',
    totalFee: 20000, // 金额单位为分
    detail: '2024-03-16至2024-03-18，共2晚'
};

PaymentUtils.createPayment(paymentData)
    .then(res => console.log('支付成功'))
    .catch(err => console.log('支付失败'));
```

## 核心组件

### 1. 配置类
- `WechatPayConfig` - V3版本支付配置

### 2. 工具类
- `WechatPayV3Utils` - V3版本支付工具类

### 3. 服务类
- `IWechatPayService` - 支付服务接口
- `WechatPayServiceImpl` - 支付服务实现

### 4. 实体类
- `WechatPayV3Request` - V3版本请求实体
- `WechatPayV3Result` - V3版本响应实体
- `WechatPayParams` - 小程序支付参数

## V3版本特性

### 🔒 安全性提升
- **RSA-SHA256签名**：替代MD5签名
- **证书验证**：支持微信平台证书验证
- **HTTPS强制**：所有接口必须使用HTTPS

### 📊 数据格式优化
- **JSON格式**：替代XML，更易处理
- **结构化数据**：清晰的嵌套结构
- **类型安全**：明确的字段类型定义

### 🚀 功能增强
- **丰富的订单信息**：支持商品详情、场景信息
- **精确状态跟踪**：更准确的订单状态管理
- **完善错误处理**：详细的错误码和描述

## 部署注意事项

### 1. 环境要求
- Java 8+
- HTTPS证书
- 微信商户平台配置

### 2. 证书安全
- 私钥文件权限设置为600
- 不要将私钥提交到代码仓库
- 定期更新证书和密钥

### 3. 配置验证
```bash
# 验证私钥文件
openssl rsa -in apiclient_key.pem -check -noout

# 查看证书序列号
openssl x509 -in apiclient_cert.pem -noout -serial
```

## 测试建议

### 1. 单元测试
运行测试用例验证功能：
```bash
mvn test -Dtest=WechatPayTest
```

### 2. 沙箱测试
- 使用微信支付沙箱环境
- 测试小额支付（如0.01元）
- 验证回调通知处理

### 3. 生产验证
- 确认HTTPS证书有效
- 验证回调URL可访问
- 监控支付成功率

## 常见问题

### 1. 签名验证失败
- 检查私钥文件格式和路径
- 确认证书序列号正确
- 验证APIv3密钥长度（32位）

### 2. 回调通知问题
- 确认回调URL使用HTTPS
- 检查防火墙和网络配置
- 验证请求头信息完整

### 3. 证书相关问题
- 确保证书未过期
- 检查文件权限设置
- 验证证书内容格式

## 监控和维护

### 1. 关键指标
- 支付成功率
- 支付响应时间
- 回调处理成功率
- 异常错误统计

### 2. 日志监控
```bash
# 查看支付相关日志
tail -f logs/wechat-pay.log | grep -E "(支付|订单|回调)"

# 统计支付成功率
grep -c "支付成功" logs/wechat-pay.log
```

### 3. 定期维护
- 每月检查证书有效期
- 每季度更新APIv3密钥
- 定期清理过期日志
- 监控支付趋势变化

## 技术支持

如遇到问题，请检查：
1. 配置文件是否正确
2. 证书文件是否有效
3. 网络连接是否正常
4. 日志中的错误信息

更多详细信息请参考：
- `doc/微信支付V3版本使用说明.md`
- `doc/微信支付V3部署配置指南.md`

## 版本说明

- **当前版本**：微信支付V3版本
- **移除内容**：所有V2版本相关代码
- **主要改进**：安全性、数据格式、功能特性全面升级
- **兼容性**：小程序端无需修改，后端自动使用V3版本
