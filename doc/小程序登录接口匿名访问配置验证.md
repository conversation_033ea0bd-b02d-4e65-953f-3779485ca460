# 小程序登录接口匿名访问配置验证

## 问题描述

小程序登录接口 `/api/wechat/login` 被Shiro拦截，无法匿名访问。

## 解决方案

### 1. 修改Shiro过滤器链配置

在 `ShiroConfig.java` 中，将整个 `/api/wechat/**` 路径设置为匿名访问：

```java
// 小程序微信相关接口，不需要Shiro拦截（包括登录、token验证等）
filterChainDefinitionMap.put("/api/wechat/**", "anon");
// 小程序API接口，需要小程序token认证
filterChainDefinitionMap.put("/api/hotel/**", "miniAppAuth");
```

### 2. 当前过滤器链顺序

```java
// 静态资源
filterChainDefinitionMap.put("/css/**", "anon");
filterChainDefinitionMap.put("/js/**", "anon");
// ... 其他静态资源

// 系统接口
filterChainDefinitionMap.put("/login", "anon,captchaValidate");
filterChainDefinitionMap.put("/register", "anon,captchaValidate");

// 小程序接口
filterChainDefinitionMap.put("/api/wechat/**", "anon");        // 匿名访问
filterChainDefinitionMap.put("/api/hotel/**", "miniAppAuth"); // 需要认证

// 默认规则
filterChainDefinitionMap.put("/**", "user,kickout,onlineSession,syncOnlineSession,csrfValidateFilter");
```

### 3. 匿名访问的微信接口列表

以下接口现在都可以匿名访问：

- `GET /api/wechat/getAccessToken` - 获取微信access_token
- `POST /api/wechat/refreshAccessToken` - 刷新access_token
- `POST /api/wechat/login` - 小程序登录 ⭐
- `POST /api/wechat/loginWithUserInfo` - 带用户信息登录 ⭐
- `POST /api/wechat/getUserInfo` - 获取微信用户信息（调试用）
- `POST /api/wechat/clearCache` - 清除缓存
- `GET /api/wechat/getUserByOpenid` - 根据openid查询用户
- `GET /api/wechat/getUserById` - 根据ID查询用户
- `GET /api/wechat/checkSessionKey` - 检查sessionKey状态
- `GET /api/wechat/getSessionStats` - 获取session统计
- `POST /api/wechat/cleanExpiredSessions` - 清理过期session
- `POST /api/wechat/validateToken` - 验证token（测试用）
- `POST /api/wechat/refreshToken` - 刷新token（测试用）

## 验证步骤

### 1. 测试小程序登录接口

使用Postman或curl测试：

```bash
curl -X POST http://localhost/api/wechat/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "jsCode=test_js_code"
```

**预期结果**：
- 不应该返回401认证错误
- 应该返回业务逻辑的响应（可能是jsCode无效的错误，但不是认证错误）

### 2. 测试其他微信接口

```bash
# 测试获取access_token
curl -X GET http://localhost/api/wechat/getAccessToken

# 测试token验证
curl -X POST http://localhost/api/wechat/validateToken \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "token=test_token"
```

### 3. 验证酒店接口仍需认证

```bash
# 不带token访问，应该返回401
curl -X POST http://localhost/api/hotel/validateCode \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "categoryId=TEST123&conferenceId=1"
```

**预期结果**：应该返回401认证失败

## 常见问题排查

### 1. 如果仍然被拦截

检查以下几点：

1. **确认项目已重启**
   - Shiro配置修改需要重启应用

2. **检查过滤器链顺序**
   - 确保 `/api/wechat/**` 在 `/**` 之前定义

3. **检查URL匹配**
   - 确认请求的URL完全匹配 `/api/wechat/**` 模式

4. **查看启动日志**
   - 检查Shiro过滤器链是否正确加载

### 2. 调试方法

#### 开启Shiro调试日志

在 `application.yml` 中添加：

```yaml
logging:
  level:
    org.apache.shiro: debug
    com.ruoyi.framework.shiro: debug
```

#### 检查过滤器链

在ShiroConfig中添加调试代码：

```java
// 在setFilterChainDefinitionMap之前添加
log.info("Shiro过滤器链配置:");
filterChainDefinitionMap.forEach((url, filter) -> {
    log.info("  {} -> {}", url, filter);
});
```

### 3. 验证配置是否生效

创建一个简单的测试接口：

```java
@GetMapping("/test")
public AjaxResult test() {
    return AjaxResult.success("微信接口可以匿名访问");
}
```

访问 `http://localhost/api/wechat/test`，如果能正常访问说明配置生效。

## 安全考虑

### 1. 接口安全性

虽然微信接口设置为匿名访问，但仍有安全保障：

- **登录接口**：需要有效的jsCode，jsCode有5分钟有效期
- **token验证接口**：只是验证token，不涉及敏感操作
- **管理接口**：如getUserByOpenid等，可以考虑添加IP白名单或其他限制

### 2. 建议的安全增强

1. **添加访问频率限制**
   ```java
   @RateLimiter(count = 10, time = 60) // 每分钟最多10次
   public AjaxResult login(@RequestParam String jsCode) {
       // ...
   }
   ```

2. **添加IP白名单**（如果需要）
   ```java
   // 在过滤器中检查IP
   filterChainDefinitionMap.put("/api/wechat/admin/**", "ipFilter");
   ```

3. **敏感接口单独配置**
   ```java
   // 将管理类接口单独配置
   filterChainDefinitionMap.put("/api/wechat/login", "anon");
   filterChainDefinitionMap.put("/api/wechat/loginWithUserInfo", "anon");
   filterChainDefinitionMap.put("/api/wechat/admin/**", "authc"); // 需要认证
   ```

## 总结

通过将 `/api/wechat/**` 配置为匿名访问，解决了小程序登录接口被拦截的问题。这样配置的好处：

1. ✅ 小程序登录接口可以正常访问
2. ✅ 其他微信相关接口也可以正常使用
3. ✅ 酒店业务接口仍然需要token认证
4. ✅ 管理后台登录不受影响

如果需要更细粒度的控制，可以将微信接口分类，对不同类型的接口应用不同的访问策略。
