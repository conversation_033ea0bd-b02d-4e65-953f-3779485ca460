# 微信小程序工具类使用说明

## 概述

本文档介绍了新增的微信小程序工具类的使用方法，主要实现了获取微信小程序access_token的功能，并提供了完整的缓存机制和错误处理。

## 功能特性

- ✅ 获取微信小程序access_token
- ✅ 自动缓存管理（默认缓存时间2小时，提前5分钟刷新）
- ✅ 强制刷新access_token
- ✅ 微信小程序登录（code2Session）
- ✅ 完善的错误处理和日志记录
- ✅ 定时清理过期缓存
- ✅ RESTful API接口

## 配置说明

### 1. 配置文件设置

在 `ruoyi-admin/src/main/resources/application.yml` 中添加微信小程序配置：

```yaml
# 微信小程序配置
wechat:
  miniapp:
    # 小程序AppID
    appId: your_mini_app_id
    # 小程序AppSecret
    appSecret: your_mini_app_secret
    # access_token缓存时间（秒），默认7200秒（2小时）
    tokenCacheTime: 7200
```

**注意：** 请将 `your_mini_app_id` 和 `your_mini_app_secret` 替换为实际的微信小程序AppID和AppSecret。

## 核心类说明

### 1. WechatMiniAppUtils - 微信小程序工具类

主要功能：
- `getAccessToken()` - 获取access_token（从缓存或微信服务器）
- `getAccessToken(boolean forceRefresh)` - 获取access_token，支持强制刷新
- `code2Session(String jsCode)` - 微信小程序登录
- `clearAccessTokenCache()` - 清除access_token缓存
- `isAccessTokenValid(String accessToken)` - 验证access_token是否有效

### 2. WechatMiniAppConfig - 配置属性类

用于读取application.yml中的微信小程序配置。

### 3. WechatCacheWrapper - 缓存包装类

提供带过期时间的缓存功能，专门用于微信相关数据的缓存。

### 4. WechatApiResult - 微信API响应封装类

封装微信API的响应结果，包含错误码、错误信息、access_token等字段。

### 5. WechatConstants - 微信常量类

定义微信API的URL、错误码等常量。

## 使用示例

### 1. 在Service中使用

```java
@Service
public class YourService {
    
    @Autowired
    private WechatMiniAppUtils wechatMiniAppUtils;
    
    public void someMethod() {
        // 获取access_token
        String accessToken = wechatMiniAppUtils.getAccessToken();
        if (StringUtils.isNotEmpty(accessToken)) {
            // 使用access_token调用其他微信API
            // ...
        }
        
        // 强制刷新access_token
        String newAccessToken = wechatMiniAppUtils.getAccessToken(true);
        
        // 微信小程序登录
        WechatApiResult loginResult = wechatMiniAppUtils.code2Session(jsCode);
        if (loginResult.isSuccess()) {
            String openId = loginResult.getOpenId();
            String sessionKey = loginResult.getSessionKey();
            // 处理登录逻辑
        }
    }
}
```

### 2. 通过API接口使用

已提供完整的RESTful API接口：

#### 获取access_token
```
GET /api/wechat/getAccessToken
```

#### 强制刷新access_token
```
POST /api/wechat/refreshAccessToken
```

#### 微信小程序登录
```
POST /api/wechat/login
参数：jsCode - 小程序登录时获取的code
```

#### 清除缓存
```
POST /api/wechat/clearCache
```

## 缓存机制

### 缓存策略
- access_token默认缓存时间为2小时（7200秒）
- 实际缓存时间为配置时间减去5分钟，确保提前刷新
- 使用内存缓存，支持过期时间设置
- 提供定时任务清理过期缓存

### 缓存键规则
```
wechat:access_token:{appId}
```

## 定时任务

### 缓存清理任务
- 类名：`WechatCacheCleanTask`
- 方法：`cleanExpiredCache()`
- 建议执行频率：每30分钟执行一次

可以在系统管理 -> 定时任务中添加该任务：
- 任务名称：微信缓存清理
- 任务组名：DEFAULT
- 调用目标字符串：`wechatCacheCleanTask.cleanExpiredCache()`
- cron表达式：`0 */30 * * * ?`（每30分钟执行一次）

## 错误处理

### 常见错误码
- `40001` - access_token过期
- `42001` - access_token无效
- `40013` - 不合法的AppID
- `40125` - 不合法的密钥

### 日志级别
- INFO：正常操作日志
- DEBUG：详细调试信息
- ERROR：错误和异常信息

## 安全注意事项

1. **配置安全**：AppSecret是敏感信息，不要提交到版本控制系统
2. **access_token保护**：access_token具有较高权限，注意保护
3. **频率限制**：微信API有调用频率限制，合理使用缓存
4. **错误处理**：完善的错误处理机制，避免敏感信息泄露

## 扩展功能

基于现有的工具类，可以轻松扩展以下功能：
- 发送订阅消息
- 获取用户信息
- 生成小程序码
- 内容安全检测
- 数据分析接口

## 测试建议

1. **单元测试**：为核心方法编写单元测试
2. **集成测试**：测试与微信服务器的交互
3. **缓存测试**：验证缓存机制的正确性
4. **异常测试**：测试各种异常情况的处理

## 版本信息

- 创建时间：2025-07-23
- 版本：1.0.0
- 兼容性：若依框架 4.8.1+
- 依赖：Spring Boot 2.5.15+
