# 酒店订单管理功能说明

## 概述

本文档介绍了酒店订单管理系统的完整功能，包括订单创建、状态管理、支付集成和数据统计等核心功能。系统采用完整的订单生命周期管理，支持订单状态跟踪和变更记录。

## 功能特性

### 🏨 订单管理
- ✅ 订单创建和信息记录
- ✅ 订单状态实时跟踪
- ✅ 支付状态管理
- ✅ 订单取消和确认
- ✅ 超时订单自动处理

### 💰 支付集成
- ✅ 微信支付V3版本集成
- ✅ 支付状态同步更新
- ✅ 支付回调处理
- ✅ 订单支付信息记录

### 📊 数据统计
- ✅ 订单数量统计
- ✅ 用户订单统计
- ✅ 状态分布统计
- ✅ 超时订单监控

### 📝 状态记录
- ✅ 订单状态变更日志
- ✅ 支付状态变更记录
- ✅ 操作人员记录
- ✅ 变更原因追踪

## 数据库设计

### 1. 订单表 (hotel_order)

```sql
CREATE TABLE `hotel_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `openid` varchar(128) DEFAULT NULL COMMENT '微信用户openid',
  `conference_id` bigint(20) NOT NULL COMMENT '会议ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `room_type` varchar(50) NOT NULL COMMENT '房间类型',
  `room_name` varchar(100) NOT NULL COMMENT '房间名称',
  `checkin_date` date NOT NULL COMMENT '入住日期',
  `checkout_date` date NOT NULL COMMENT '退房日期',
  `nights` int(11) NOT NULL COMMENT '住宿天数',
  `room_price` decimal(10,2) NOT NULL COMMENT '房间单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `deposit_amount` decimal(10,2) NOT NULL COMMENT '押金金额',
  `order_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
  `payment_status` varchar(20) NOT NULL DEFAULT 'UNPAID' COMMENT '支付状态',
  -- 更多字段...
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`)
);
```

### 2. 状态变更记录表 (hotel_order_status_log)

```sql
CREATE TABLE `hotel_order_status_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `old_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `new_status` varchar(20) NOT NULL COMMENT '新状态',
  `status_type` varchar(20) NOT NULL COMMENT '状态类型',
  `change_reason` varchar(200) DEFAULT NULL COMMENT '变更原因',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`)
);
```

## 订单状态说明

### 订单状态 (order_status)
- **PENDING** - 待支付：订单已创建，等待用户支付
- **PAID** - 已支付：用户已完成支付，等待确认
- **CONFIRMED** - 已确认：订单已确认，可以入住
- **CANCELLED** - 已取消：订单已取消
- **REFUNDED** - 已退款：订单已退款

### 支付状态 (payment_status)
- **UNPAID** - 未支付：尚未支付
- **PAID** - 已支付：支付成功
- **REFUNDING** - 退款中：正在处理退款
- **REFUNDED** - 已退款：退款完成

## API接口说明

### 1. 创建订单并发起支付

**接口地址**：`POST /api/hotel/order/create`

**请求示例**：
```json
{
    "conferenceId": 1,
    "roomId": 1,
    "roomType": "DELUXE",
    "roomName": "豪华大床房",
    "checkinDate": "2025-03-16",
    "checkoutDate": "2025-03-18",
    "nights": 2,
    "roomPrice": 200.00,
    "totalAmount": 400.00,
    "depositAmount": 100.00,
    "guestName": "张三",
    "guestPhone": "13800138001",
    "guestIdCard": "110101199001011234",
    "specialRequirements": "需要无烟房间",
    "remark": "会议期间住宿"
}
```

**响应示例**：
```json
{
    "code": 200,
    "msg": "创建订单成功",
    "data": {
        "appId": "wx80ecd969d8dc4f6d",
        "timeStamp": "1705472400",
        "nonceStr": "abc123def456",
        "packageValue": "prepay_id=wx17154724001234567890",
        "signType": "RSA",
        "paySign": "Base64EncodedRSASignature"
    },
    "orderNo": "HT20250117154724001",
    "orderId": 1
}
```

### 2. 查询用户订单列表

**接口地址**：`GET /api/hotel/order/list?orderStatus=PENDING`

**响应示例**：
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": [
        {
            "orderId": 1,
            "orderNo": "HT20250117154724001",
            "roomName": "豪华大床房",
            "checkinDate": "2025-03-16",
            "checkoutDate": "2025-03-18",
            "nights": 2,
            "totalAmount": 400.00,
            "orderStatus": "PENDING",
            "paymentStatus": "UNPAID",
            "createTime": "2025-01-17 15:47:24"
        }
    ]
}
```

### 3. 查询订单详情

**接口地址**：`GET /api/hotel/order/detail/{orderNo}`

**响应示例**：
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "orderId": 1,
        "orderNo": "HT20250117154724001",
        "userId": 1,
        "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
        "conferenceId": 1,
        "roomId": 1,
        "roomType": "DELUXE",
        "roomName": "豪华大床房",
        "checkinDate": "2025-03-16",
        "checkoutDate": "2025-03-18",
        "nights": 2,
        "roomPrice": 200.00,
        "totalAmount": 400.00,
        "depositAmount": 100.00,
        "orderStatus": "PENDING",
        "paymentStatus": "UNPAID",
        "guestName": "张三",
        "guestPhone": "13800138001",
        "specialRequirements": "需要无烟房间",
        "createTime": "2025-01-17 15:47:24"
    }
}
```

### 4. 取消订单

**接口地址**：`POST /api/hotel/order/cancel/{orderNo}?cancelReason=用户主动取消`

**响应示例**：
```json
{
    "code": 200,
    "msg": "取消订单成功"
}
```

## 核心服务类

### 1. IHotelOrderService - 订单服务接口

主要方法：
- `insertHotelOrder(HotelOrder)` - 创建订单
- `selectHotelOrderByOrderNo(String)` - 根据订单号查询
- `updateOrderPaymentInfo(...)` - 更新支付信息
- `updateOrderStatus(...)` - 更新订单状态
- `cancelOrder(...)` - 取消订单
- `handleTimeoutOrders(int)` - 处理超时订单

### 2. HotelOrderServiceImpl - 订单服务实现

特色功能：
- **自动生成订单号**：格式为 `HT + yyyyMMddHHmmss + 3位随机数`
- **状态变更记录**：每次状态变更都会记录日志
- **事务管理**：确保数据一致性
- **超时处理**：自动取消超时未支付订单

## 小程序端集成

### 1. 创建订单并支付

```javascript
// 创建订单数据
const orderData = {
    conferenceId: 1,
    roomId: 1,
    roomType: 'DELUXE',
    roomName: '豪华大床房',
    checkinDate: '2025-03-16',
    checkoutDate: '2025-03-18',
    nights: 2,
    roomPrice: 200.00,
    totalAmount: 400.00,
    depositAmount: 100.00,
    guestName: '张三',
    guestPhone: '13800138001'
};

// 调用创建订单接口
wx.request({
    url: '/api/hotel/order/create',
    method: 'POST',
    data: orderData,
    success: (res) => {
        if (res.data.code === 200) {
            // 获取支付参数
            const payParams = res.data.data;
            const orderNo = res.data.orderNo;
            
            // 调起微信支付
            wx.requestPayment({
                ...payParams,
                success: () => {
                    console.log('支付成功，订单号：', orderNo);
                    // 跳转到订单详情页面
                },
                fail: () => {
                    console.log('支付失败');
                }
            });
        }
    }
});
```

### 2. 查询订单列表

```javascript
// 查询订单列表
wx.request({
    url: '/api/hotel/order/list',
    method: 'GET',
    data: {
        orderStatus: 'PENDING' // 可选，筛选特定状态
    },
    success: (res) => {
        if (res.data.code === 200) {
            const orderList = res.data.data;
            console.log('订单列表：', orderList);
            // 更新页面数据
        }
    }
});
```

## 管理后台功能

### 1. 订单管理页面
- 订单列表查询和筛选
- 订单详情查看
- 订单状态修改
- 批量操作功能

### 2. 统计报表
- 订单数量统计
- 收入统计分析
- 状态分布图表
- 趋势分析报告

## 定时任务

### 1. 超时订单处理

```java
@Scheduled(fixedRate = 300000) // 每5分钟执行一次
public void handleTimeoutOrders() {
    int timeoutMinutes = 30; // 30分钟超时
    int handledCount = hotelOrderService.handleTimeoutOrders(timeoutMinutes);
    if (handledCount > 0) {
        log.info("处理超时订单数量: {}", handledCount);
    }
}
```

### 2. 订单状态同步

```java
@Scheduled(cron = "0 */10 * * * ?") // 每10分钟执行一次
public void syncOrderStatus() {
    // 查询已支付但未确认的订单
    // 调用微信支付查询接口确认状态
    // 更新本地订单状态
}
```

## 测试验证

### 1. 单元测试

运行订单功能测试：
```bash
mvn test -Dtest=HotelOrderTest
```

### 2. 集成测试

测试完整的订单创建和支付流程：
```bash
mvn test -Dtest=WechatPayTest#testHttpUtilsIntegration
```

### 3. 性能测试

测试订单创建和查询的性能：
- 并发订单创建测试
- 大量数据查询测试
- 状态更新性能测试

## 监控和维护

### 1. 关键指标监控
- 订单创建成功率
- 支付成功率
- 订单处理时间
- 超时订单数量

### 2. 日志监控
```bash
# 查看订单相关日志
tail -f logs/ruoyi-admin.log | grep -E "(订单|支付|Order)"

# 统计订单创建数量
grep -c "创建酒店订单成功" logs/ruoyi-admin.log
```

### 3. 数据维护
- 定期清理过期的状态变更日志
- 备份重要订单数据
- 监控数据库性能

## 安全注意事项

### 1. 数据安全
- 订单信息加密存储
- 敏感信息脱敏显示
- 访问权限控制

### 2. 接口安全
- 用户身份验证
- 订单归属验证
- 防止重复提交

### 3. 支付安全
- 金额校验
- 订单状态校验
- 回调签名验证

## 总结

酒店订单管理系统提供了完整的订单生命周期管理功能，从订单创建、支付处理到状态跟踪，形成了闭环的业务流程。系统具有以下优势：

1. **完整性**：覆盖订单管理的所有环节
2. **可靠性**：事务管理和状态记录确保数据一致性
3. **可扩展性**：模块化设计便于功能扩展
4. **易维护性**：详细的日志记录和监控机制

通过这套订单管理系统，可以有效地管理酒店预订业务，提供良好的用户体验和管理效率。
