# 小程序API接口Shiro配置说明

## 概述

本文档说明了如何配置Shiro框架，使小程序API接口能够正常访问而不被拦截。小程序登录接口必须排除在Shiro的认证拦截之外，否则会形成循环依赖（需要登录才能登录）。

## 配置修改

### 1. Shiro过滤器配置

在 `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ShiroConfig.java` 中添加了以下配置：

```java
// 小程序API接口，不需要Shiro拦截
filterChainDefinitionMap.put("/api/wechat/**", "anon");
filterChainDefinitionMap.put("/api/hotel/**", "anon");
```

### 2. CSRF保护配置

在 `ruoyi-admin/src/main/resources/application.yml` 中修改了CSRF白名单：

```yaml
# 防止csrf攻击
csrf:
  # 过滤开关
  enabled: false
  # 白名单（多个用逗号分隔）
  whites: /druid,/api/**
```

### 3. XSS过滤配置

在 `ruoyi-admin/src/main/resources/application.yml` 中修改了XSS排除列表：

```yaml
# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*,/api/**
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
```

## 排除的API路径

以下API路径已被配置为匿名访问，不需要Shiro认证：

### 1. 微信小程序API (`/api/wechat/**`)

- `POST /api/wechat/login` - 小程序登录
- `POST /api/wechat/loginWithUserInfo` - 带用户信息的登录
- `POST /api/wechat/getUserInfo` - 获取微信用户信息
- `GET /api/wechat/getAccessToken` - 获取access_token
- `POST /api/wechat/refreshAccessToken` - 刷新access_token
- `POST /api/wechat/clearCache` - 清除缓存
- `GET /api/wechat/getUserByOpenid` - 根据openid查询用户
- `GET /api/wechat/getUserById` - 根据用户ID查询用户
- `GET /api/wechat/checkSessionKey` - 检查sessionKey状态
- `GET /api/wechat/getSessionStats` - 获取sessionKey统计
- `POST /api/wechat/cleanExpiredSessions` - 清理过期sessionKey

### 2. 酒店预订API (`/api/hotel/**`)

- `GET /api/hotel/conferences` - 获取会议列表
- `GET /api/hotel/conference` - 获取会议详情
- `POST /api/hotel/validateCode` - 验证识别码
- `GET /api/hotel/rooms` - 获取房型列表
- `GET /api/hotel/room` - 获取房型详情
- `POST /api/hotel/getRoomByCode` - 根据识别码获取房型

## 安全考虑

### 1. 为什么要排除这些接口？

1. **登录接口必须排除**：小程序登录接口如果被Shiro拦截，会形成循环依赖
2. **公开数据接口**：会议列表、房型信息等属于公开数据，不需要认证
3. **用户体验**：小程序用户无需先登录后台管理系统

### 2. 安全措施

虽然这些接口不需要Shiro认证，但仍然有以下安全措施：

1. **业务层验证**：在Service层进行业务逻辑验证
2. **参数验证**：对输入参数进行严格验证
3. **权限控制**：通过openid等方式进行用户身份验证
4. **日志记录**：记录所有API调用日志
5. **频率限制**：可以在网关层添加频率限制

### 3. 管理员接口的保护

对于一些管理员专用的接口（如清理缓存、获取统计信息），建议：

1. **添加IP白名单**：只允许内网IP访问
2. **添加API密钥**：通过API密钥进行认证
3. **单独的路径**：将管理员接口放在单独的路径下，如 `/api/admin/**`

## 测试验证

### 1. 测试小程序登录接口

```bash
# 测试登录接口是否可以正常访问
curl -X POST "http://localhost:80/api/wechat/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "jsCode=test_js_code"
```

**期望结果**：返回JSON响应，不会被重定向到登录页面

### 2. 测试酒店API接口

```bash
# 测试获取会议列表
curl -X GET "http://localhost:80/api/hotel/conferences"
```

**期望结果**：返回会议列表数据，不会被Shiro拦截

### 3. 测试管理后台接口

```bash
# 测试管理后台接口是否仍然受保护
curl -X GET "http://localhost:80/system/user/list"
```

**期望结果**：被重定向到登录页面或返回401/403错误

## 常见问题

### 1. 接口仍然被拦截

**问题**：配置了匿名访问，但接口仍然被Shiro拦截

**解决方案**：
1. 检查路径配置是否正确
2. 确认过滤器链的顺序
3. 重启应用服务器

### 2. CSRF错误

**问题**：POST请求返回CSRF token错误

**解决方案**：
1. 确认CSRF白名单配置正确
2. 或者在请求头中添加CSRF token

### 3. XSS过滤问题

**问题**：请求参数被XSS过滤器修改

**解决方案**：
1. 将API路径添加到XSS排除列表
2. 或者在业务层处理XSS防护

## 配置文件总结

### ShiroConfig.java 关键配置

```java
// 小程序API接口，不需要Shiro拦截
filterChainDefinitionMap.put("/api/wechat/**", "anon");
filterChainDefinitionMap.put("/api/hotel/**", "anon");
```

### application.yml 关键配置

```yaml
# 防止XSS攻击
xss:
  excludes: /system/notice/*,/api/**

# 防止csrf攻击  
csrf:
  whites: /druid,/api/**
```

## 版本信息

- **修改时间**：2025-07-23
- **版本**：1.0.0
- **适用范围**：若依框架 + 微信小程序API
- **维护团队**：后端开发团队

## 相关文档

- [小程序登录接口使用说明](./小程序登录接口使用说明.md)
- [微信小程序SessionKey安全管理说明](./微信小程序SessionKey安全管理说明.md)
- [Apache Shiro官方文档](https://shiro.apache.org/documentation.html)
