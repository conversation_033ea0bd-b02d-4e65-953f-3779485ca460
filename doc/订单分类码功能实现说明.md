# 订单分类码功能实现说明

## 功能概述
在创建订单时，需要将分类码（识别码）也传递过来，用于查询房型数量，同时订单表也需要记录分类码。

## 实现内容

### 1. 后端修改

#### 1.1 CreateOrderRequest 请求类修改
- **文件**: `ruoyi-common/src/main/java/com/ruoyi/common/core/domain/request/hotel/CreateOrderRequest.java`
- **修改内容**:
  - 添加 `categoryId` 字段用于接收前端传递的分类码
  - 添加对应的 getter 和 setter 方法
  - 更新 toString 方法

#### 1.2 HotelOrder 实体类修改
- **文件**: `ruoyi-system/src/main/java/com/ruoyi/system/domain/HotelOrder.java`
- **修改内容**:
  - 添加 `categoryId` 字段用于存储订单关联的分类码
  - 添加对应的 getter 和 setter 方法
  - 更新 toString 方法

#### 1.3 订单创建逻辑修改
- **文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/api/HotelApiController.java`
- **修改内容**:
  - 在 `buildHotelOrder` 方法中添加分类码的传递逻辑
  - 将请求参数中的 `categoryId` 设置到订单对象中

#### 1.4 数据库表结构修改
- **文件**: `sql/hotel_order_add_category_id.sql`
- **修改内容**:
  - 为 `hotel_order` 表添加 `category_id` 字段
  - 添加索引以提高查询性能

#### 1.5 Mapper XML 文件修改
- **文件**: `ruoyi-system/src/main/resources/mapper/system/HotelOrderMapper.xml`
- **修改内容**:
  - 在 resultMap 中添加 `categoryId` 字段映射
  - 在所有查询语句中添加 `category_id` 字段
  - 在插入和更新语句中添加 `category_id` 字段处理

### 2. 前端修改

#### 2.1 小程序端订单创建请求修改
- **文件**: `galleno_miniprogram/pages/booking/booking.js`
- **修改内容**:
  - 在 `requestPayment` 方法中的 `createOrderData` 对象中添加 `categoryId` 字段
  - 确保分类码从预订数据中正确传递到订单创建请求中

## 数据流程

1. **用户验证分类码**: 用户在小程序中输入分类码，系统验证分类码有效性
2. **房型查询**: 根据分类码查询可用的房型和数量信息
3. **订单创建**: 用户选择房型并填写信息后，创建订单时将分类码一并传递
4. **数据存储**: 订单创建成功后，分类码被保存在订单记录中

## 使用场景

- **房型数量查询**: 根据分类码查询特定用户群体可预订的房型数量
- **订单追溯**: 通过订单中的分类码，可以追溯用户是通过哪个分类码进行的预订
- **数据分析**: 可以根据分类码统计不同用户群体的预订情况

## 注意事项

1. **数据库迁移**: 需要执行 `sql/hotel_order_add_category_id.sql` 脚本来添加数据库字段
2. **向后兼容**: 分类码字段设置为可空，确保现有数据的兼容性
3. **数据验证**: 前端传递的分类码应该是有效的，建议在后端也进行验证
4. **索引优化**: 为 `category_id` 字段添加了索引，提高查询性能

## 测试建议

1. 测试创建订单时分类码的正确传递和存储
2. 测试查询订单时分类码信息的正确显示
3. 测试分类码为空的情况下系统的正常运行
4. 测试数据库迁移脚本的执行效果
