# 会议照片功能验证脚本

## 1. 数据库验证

### 检查字段是否添加成功
```sql
DESCRIBE conference;
```
应该能看到 `photo_path` 字段，类型为 VARCHAR(255)

### 插入测试数据（可选）
```sql
INSERT INTO conference (conference_title, photo_path, enable, operator, create_time) 
VALUES ('测试会议', '/profile/upload/2025/01/29/test.jpg', 'Y', 'admin', NOW());
```

## 2. 后端接口验证

### 检查Controller方法
- ConferenceController.addSave() 方法应该接收 MultipartFile 参数
- ConferenceController.editSave() 方法应该接收 MultipartFile 参数
- 两个方法都应该处理文件上传逻辑

### 检查实体类
- Conference.java 应该包含 photoPath 字段
- 应该有对应的 getter/setter 方法

### 检查Mapper
- ConferenceMapper.xml 应该在所有SQL中包含 photo_path 字段

## 3. 前端功能验证

### 页面检查清单
- [ ] add.html 包含文件上传控件
- [ ] edit.html 包含文件上传控件和现有图片预览
- [ ] view.html 显示会议照片
- [ ] conference.html 列表显示缩略图

### JavaScript功能检查
- [ ] 文件上传使用 FormData
- [ ] Ajax请求设置 processData: false, contentType: false
- [ ] 图片预览功能正常

## 4. 文件上传测试

### 测试步骤
1. 启动应用
2. 访问 http://localhost/hotel/conference
3. 点击"添加"按钮
4. 填写会议信息并上传图片
5. 保存后检查：
   - 数据库记录是否正确
   - 文件是否保存到服务器
   - 列表页面是否显示缩略图

### 预期结果
- 图片保存路径：`{profile}/upload/yyyy/MM/dd/filename`
- 数据库存储：`/profile/upload/yyyy/MM/dd/filename`
- 浏览器访问：`http://localhost/profile/upload/yyyy/MM/dd/filename`

## 5. 常见问题排查

### 文件上传失败
1. 检查文件大小是否超限（默认10MB）
2. 检查文件格式是否支持
3. 检查服务器磁盘空间
4. 检查上传目录权限

### 图片显示异常
1. 检查静态资源映射配置
2. 检查图片路径是否正确
3. 检查浏览器控制台错误信息

### 数据保存失败
1. 检查数据库字段是否存在
2. 检查字段长度是否足够
3. 检查SQL语句是否正确

## 6. 性能优化建议

### 图片处理
- 考虑添加图片压缩功能
- 限制图片尺寸和文件大小
- 实现图片格式转换

### 存储优化
- 定期清理无用图片
- 考虑使用CDN存储
- 实现图片缓存策略
