<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CategoryCodeRoomMapper">
    
    <resultMap type="CategoryCodeRoom" id="CategoryCodeRoomResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="conference"    column="conference"    />
        <result property="roomId"    column="room_id"    />
        <result property="roomCount"    column="room_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="roomTitle"    column="room_title"    />
    </resultMap>

    <sql id="selectCategoryCodeRoomVo">
        select ccr.id, ccr.category_id, ccr.conference, ccr.room_id, ccr.room_count, ccr.create_time, r.room_title
        from category_code_room ccr
        left join room r on ccr.room_id = r.id
    </sql>

    <select id="selectCategoryCodeRoomList" parameterType="CategoryCodeRoom" resultMap="CategoryCodeRoomResult">
        <include refid="selectCategoryCodeRoomVo"/>
        <where>
            <if test="categoryId != null  and categoryId != ''"> and ccr.category_id = #{categoryId}</if>
            <if test="conference != null "> and ccr.conference = #{conference}</if>
            <if test="roomId != null "> and ccr.room_id = #{roomId}</if>
            <if test="roomCount != null "> and ccr.room_count = #{roomCount}</if>
        </where>
        order by ccr.create_time desc
    </select>
    
    <select id="selectCategoryCodeRoomById" parameterType="Long" resultMap="CategoryCodeRoomResult">
        <include refid="selectCategoryCodeRoomVo"/>
        where ccr.id = #{id}
    </select>

    <select id="selectRoomsByCategoryCode" resultMap="CategoryCodeRoomResult">
        <include refid="selectCategoryCodeRoomVo"/>
        where ccr.category_id = #{categoryId} and ccr.conference = #{conference}
        order by ccr.create_time desc
    </select>
        
    <insert id="insertCategoryCodeRoom" parameterType="CategoryCodeRoom" useGeneratedKeys="true" keyProperty="id">
        insert into category_code_room
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null and categoryId != ''">category_id,</if>
            <if test="conference != null">conference,</if>
            <if test="roomId != null">room_id,</if>
            <if test="roomCount != null">room_count,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null and categoryId != ''">#{categoryId},</if>
            <if test="conference != null">#{conference},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="roomCount != null">#{roomCount},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertCategoryCodeRoom" parameterType="java.util.List">
        insert into category_code_room (category_id, conference, room_id, room_count, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.categoryId}, #{item.conference}, #{item.roomId}, #{item.roomCount}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateCategoryCodeRoom" parameterType="CategoryCodeRoom">
        update category_code_room
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null and categoryId != ''">category_id = #{categoryId},</if>
            <if test="conference != null">conference = #{conference},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="roomCount != null">room_count = #{roomCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCategoryCodeRoomById" parameterType="Long">
        delete from category_code_room where id = #{id}
    </delete>

    <delete id="deleteCategoryCodeRoomByIds" parameterType="String">
        delete from category_code_room where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByCategoryCode">
        delete from category_code_room where category_id = #{categoryId} and conference = #{conference}
    </delete>

    <!-- 获取指定房间的总库存数量 -->
    <select id="getRoomInventory" resultType="int">
        SELECT room_count
        FROM category_code_room
        WHERE room_id = #{roomId} AND conference = #{conferenceId} and category_id = #{categoryId}
    </select>
</mapper>
