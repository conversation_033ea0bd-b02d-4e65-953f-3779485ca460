<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HotelOrderStatusLogMapper">
    
    <resultMap type="HotelOrderStatusLog" id="HotelOrderStatusLogResult">
        <result property="logId"    column="log_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="oldStatus"    column="old_status"    />
        <result property="newStatus"    column="new_status"    />
        <result property="statusType"    column="status_type"    />
        <result property="changeReason"    column="change_reason"    />
        <result property="operator"    column="operator"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectHotelOrderStatusLogVo">
        select log_id, order_id, order_no, old_status, new_status, status_type, change_reason, operator, create_time from hotel_order_status_log
    </sql>

    <select id="selectHotelOrderStatusLogList" parameterType="HotelOrderStatusLog" resultMap="HotelOrderStatusLogResult">
        <include refid="selectHotelOrderStatusLogVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="oldStatus != null  and oldStatus != ''"> and old_status = #{oldStatus}</if>
            <if test="newStatus != null  and newStatus != ''"> and new_status = #{newStatus}</if>
            <if test="statusType != null  and statusType != ''"> and status_type = #{statusType}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectHotelOrderStatusLogByLogId" parameterType="Long" resultMap="HotelOrderStatusLogResult">
        <include refid="selectHotelOrderStatusLogVo"/>
        where log_id = #{logId}
    </select>

    <select id="selectHotelOrderStatusLogByOrderId" parameterType="Long" resultMap="HotelOrderStatusLogResult">
        <include refid="selectHotelOrderStatusLogVo"/>
        where order_id = #{orderId}
        order by create_time desc
    </select>

    <select id="selectHotelOrderStatusLogByOrderNo" parameterType="String" resultMap="HotelOrderStatusLogResult">
        <include refid="selectHotelOrderStatusLogVo"/>
        where order_no = #{orderNo}
        order by create_time desc
    </select>
        
    <insert id="insertHotelOrderStatusLog" parameterType="HotelOrderStatusLog" useGeneratedKeys="true" keyProperty="logId">
        insert into hotel_order_status_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="oldStatus != null">old_status,</if>
            <if test="newStatus != null and newStatus != ''">new_status,</if>
            <if test="statusType != null and statusType != ''">status_type,</if>
            <if test="changeReason != null">change_reason,</if>
            <if test="operator != null">operator,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="oldStatus != null">#{oldStatus},</if>
            <if test="newStatus != null and newStatus != ''">#{newStatus},</if>
            <if test="statusType != null and statusType != ''">#{statusType},</if>
            <if test="changeReason != null">#{changeReason},</if>
            <if test="operator != null">#{operator},</if>
            sysdate()
         </trim>
    </insert>

    <insert id="batchInsertHotelOrderStatusLog" parameterType="java.util.List">
        insert into hotel_order_status_log (order_id, order_no, old_status, new_status, status_type, change_reason, operator, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId}, #{item.orderNo}, #{item.oldStatus}, #{item.newStatus}, #{item.statusType}, #{item.changeReason}, #{item.operator}, sysdate())
        </foreach>
    </insert>

    <insert id="logStatusChange">
        insert into hotel_order_status_log (order_id, order_no, old_status, new_status, status_type, change_reason, operator, create_time)
        values (#{orderId}, #{orderNo}, #{oldStatus}, #{newStatus}, #{statusType}, #{changeReason}, #{operator}, sysdate())
    </insert>

    <update id="updateHotelOrderStatusLog" parameterType="HotelOrderStatusLog">
        update hotel_order_status_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="oldStatus != null">old_status = #{oldStatus},</if>
            <if test="newStatus != null and newStatus != ''">new_status = #{newStatus},</if>
            <if test="statusType != null and statusType != ''">status_type = #{statusType},</if>
            <if test="changeReason != null">change_reason = #{changeReason},</if>
            <if test="operator != null">operator = #{operator},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteHotelOrderStatusLogByLogId" parameterType="Long">
        delete from hotel_order_status_log where log_id = #{logId}
    </delete>

    <delete id="deleteHotelOrderStatusLogByLogIds" parameterType="String">
        delete from hotel_order_status_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <delete id="deleteHotelOrderStatusLogByOrderId" parameterType="Long">
        delete from hotel_order_status_log where order_id = #{orderId}
    </delete>
</mapper>
