<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserMapper">
    
    <resultMap type="User" id="UserResult">
        <result property="id"    column="id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="realName"    column="real_name"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="gender"    column="gender"    />
        <result property="openid"    column="openid"    />
        <result property="unionid"    column="unionid"    />
        <result property="email"    column="email"    />
        <result property="company"    column="company"    />
        <result property="position"    column="position"    />
        <result property="idCard"    column="id_card"    />
        <result property="regSource"    column="reg_source"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserVo">
        select id, nick_name, real_name, phone_number, gender, openid, unionid, email, company, position, id_card, reg_source, create_time, update_time from user
    </sql>

    <select id="selectUserList" parameterType="User" resultMap="UserResult">
        <include refid="selectUserVo"/>
        <where>  
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="unionid != null  and unionid != ''"> and unionid = #{unionid}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="company != null  and company != ''"> and company like concat('%', #{company}, '%')</if>
            <if test="position != null  and position != ''"> and position like concat('%', #{position}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="regSource != null  and regSource != ''"> and reg_source = #{regSource}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectUserById" parameterType="Integer" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where id = #{id}
    </select>

    <select id="selectUserByOpenid" parameterType="String" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where openid = #{openid}
    </select>

    <select id="selectUserByPhoneNumber" parameterType="String" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where phone_number = #{phoneNumber}
    </select>

    <select id="selectUserByEmail" parameterType="String" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where email = #{email}
    </select>
        
    <insert id="insertUser" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        insert into user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="realName != null and realName != ''">real_name,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="gender != null">gender,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="unionid != null and unionid != ''">unionid,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="company != null and company != ''">company,</if>
            <if test="position != null and position != ''">position,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="regSource != null and regSource != ''">reg_source,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="realName != null and realName != ''">#{realName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="gender != null">#{gender},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="unionid != null and unionid != ''">#{unionid},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="company != null and company != ''">#{company},</if>
            <if test="position != null and position != ''">#{position},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="regSource != null and regSource != ''">#{regSource},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUser" parameterType="User">
        update user
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="unionid != null and unionid != ''">unionid = #{unionid},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="company != null and company != ''">company = #{company},</if>
            <if test="position != null and position != ''">position = #{position},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="regSource != null and regSource != ''">reg_source = #{regSource},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserById" parameterType="Integer">
        delete from user where id = #{id}
    </delete>

    <delete id="deleteUserByIds" parameterType="Integer">
        delete from user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
