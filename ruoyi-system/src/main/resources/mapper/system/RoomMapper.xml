<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.RoomMapper">
    
    <resultMap type="Room" id="RoomResult">
        <result property="id"    column="id"    />
        <result property="conferenceId"    column="conference_id"    />
        <result property="roomTitle"    column="room_title"    />
        <result property="roomImgUrl"    column="room_img_url"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="operator"    column="operator"    />
        <result property="conferenceTitle"    column="conference_title"    />
    </resultMap>

    <sql id="selectRoomVo">
        select r.id, r.conference_id, r.room_title, r.room_img_url, r.create_time, r.update_time, r.operator, c.conference_title
        from room r
        left join conference c on r.conference_id = c.id
    </sql>

    <select id="selectRoomList" parameterType="Room" resultMap="RoomResult">
        <include refid="selectRoomVo"/>
        <where>  
            <if test="conferenceId != null "> and r.conference_id = #{conferenceId}</if>
            <if test="roomTitle != null  and roomTitle != ''"> and r.room_title like concat('%', #{roomTitle}, '%')</if>
            <if test="operator != null  and operator != ''"> and r.operator = #{operator}</if>
            <if test="conferenceTitle != null  and conferenceTitle != ''"> and c.conference_title like concat('%', #{conferenceTitle}, '%')</if>
        </where>
        order by r.create_time desc
    </select>
    
    <select id="selectRoomById" parameterType="Long" resultMap="RoomResult">
        <include refid="selectRoomVo"/>
        where r.id = #{id}
    </select>

    <select id="selectRoomListByConferenceId" parameterType="Long" resultMap="RoomResult">
        select id, conference_id, room_title, room_img_url, create_time, update_time, operator
        from room
        where conference_id = #{conferenceId}
        order by create_time desc
    </select>
        
    <insert id="insertRoom" parameterType="Room" useGeneratedKeys="true" keyProperty="id">
        insert into room
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="conferenceId != null">conference_id,</if>
            <if test="roomTitle != null and roomTitle != ''">room_title,</if>
            <if test="roomImgUrl != null">room_img_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="operator != null">operator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="conferenceId != null">#{conferenceId},</if>
            <if test="roomTitle != null and roomTitle != ''">#{roomTitle},</if>
            <if test="roomImgUrl != null">#{roomImgUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operator != null">#{operator},</if>
         </trim>
    </insert>

    <update id="updateRoom" parameterType="Room">
        update room
        <trim prefix="SET" suffixOverrides=",">
            <if test="conferenceId != null">conference_id = #{conferenceId},</if>
            <if test="roomTitle != null and roomTitle != ''">room_title = #{roomTitle},</if>
            <if test="roomImgUrl != null">room_img_url = #{roomImgUrl},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="operator != null">operator = #{operator},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoomById" parameterType="Long">
        delete from room where id = #{id}
    </delete>

    <delete id="deleteRoomByIds" parameterType="String">
        delete from room where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
