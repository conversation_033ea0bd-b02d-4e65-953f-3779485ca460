package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.HotelOrder;
import org.apache.ibatis.annotations.Param;

/**
 * 酒店订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface HotelOrderMapper 
{
    /**
     * 查询酒店订单
     * 
     * @param orderId 酒店订单主键
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询酒店订单
     * 
     * @param orderNo 订单号
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByOrderNo(String orderNo);

    /**
     * 根据微信交易号查询酒店订单
     * 
     * @param transactionId 微信交易号
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByTransactionId(String transactionId);

    /**
     * 查询酒店订单列表
     * 
     * @param hotelOrder 酒店订单
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderList(HotelOrder hotelOrder);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByUserId(Long userId);

    /**
     * 根据openid查询订单列表
     * 
     * @param openid 微信openid
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByOpenid(String openid);

    /**
     * 根据会议ID查询订单列表
     * 
     * @param conferenceId 会议ID
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByConferenceId(Long conferenceId);

    /**
     * 查询待支付订单列表（超时订单）
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectTimeoutPendingOrders(@Param("timeoutMinutes") int timeoutMinutes);

    /**
     * 新增酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    public int insertHotelOrder(HotelOrder hotelOrder);

    /**
     * 修改酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    public int updateHotelOrder(HotelOrder hotelOrder);

    /**
     * 更新订单支付信息
     * 
     * @param orderNo 订单号
     * @param transactionId 微信交易号
     * @param paymentTime 支付时间
     * @param orderStatus 订单状态
     * @param paymentStatus 支付状态
     * @param paymentMethod 支付方式
     * @return 结果
     */
    public int updateOrderPaymentInfo(@Param("orderNo") String orderNo,
                                      @Param("transactionId") String transactionId,
                                      @Param("paymentTime") java.util.Date paymentTime,
                                      @Param("orderStatus") String orderStatus,
                                      @Param("paymentStatus") String paymentStatus,
                                      @Param("paymentMethod") String paymentMethod);

    /**
     * 更新订单状态
     * 
     * @param orderNo 订单号
     * @param orderStatus 订单状态
     * @param updateBy 更新人
     * @return 结果
     */
    public int updateOrderStatus(@Param("orderNo") String orderNo,
                                 @Param("orderStatus") String orderStatus,
                                 @Param("updateBy") String updateBy);

    /**
     * 更新支付状态
     * 
     * @param orderNo 订单号
     * @param paymentStatus 支付状态
     * @param updateBy 更新人
     * @return 结果
     */
    public int updatePaymentStatus(@Param("orderNo") String orderNo,
                                   @Param("paymentStatus") String paymentStatus,
                                   @Param("updateBy") String updateBy);

    /**
     * 取消订单
     *
     * @param orderNo 订单号
     * @param cancelReason 取消原因
     * @param updateBy 更新人
     * @return 结果
     */
    public int cancelOrder(@Param("orderNo") String orderNo,
                           @Param("cancelReason") String cancelReason,
                           @Param("updateBy") String updateBy);

    /**
     * 确认订单
     *
     * @param orderNo 订单号
     * @param updateBy 更新人
     * @return 结果
     */
    public int confirmOrder(@Param("orderNo") String orderNo,
                            @Param("updateBy") String updateBy);

    /**
     * 退款订单
     *
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @param updateBy 更新人
     * @return 结果
     */
    public int refundOrder(@Param("orderNo") String orderNo,
                           @Param("refundAmount") java.math.BigDecimal refundAmount,
                           @Param("refundReason") String refundReason,
                           @Param("updateBy") String updateBy);

    /**
     * 更新微信交易号
     *
     * @param orderNo 订单号
     * @param transactionId 微信交易号
     * @param updateBy 更新人
     * @return 结果
     */
    public int updateTransactionId(@Param("orderNo") String orderNo,
                                   @Param("transactionId") String transactionId,
                                   @Param("updateBy") String updateBy);

    /**
     * 删除酒店订单
     * 
     * @param orderId 酒店订单主键
     * @return 结果
     */
    public int deleteHotelOrderByOrderId(Long orderId);

    /**
     * 批量删除酒店订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHotelOrderByOrderIds(Long[] orderIds);

    /**
     * 统计订单数量
     * 
     * @param hotelOrder 查询条件
     * @return 订单数量
     */
    public int countHotelOrders(HotelOrder hotelOrder);

    /**
     * 统计用户订单数量
     *
     * @param userId 用户ID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    public int countUserOrders(@Param("userId") Long userId, @Param("orderStatus") String orderStatus);

    /**
     * 统计指定房间在指定日期范围内的已预订数量
     *
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     * @param checkinDate 入住日期
     * @param checkoutDate 退房日期
     * @return 已预订数量（包括待支付、已支付、已确认的订单，不包括已取消和已退款的订单）
     */
    public int countBookedRooms(@Param("roomId") Long roomId,
                                @Param("conferenceId") Long conferenceId,
                                @Param("categoryId") String categoryId,
                                @Param("checkinDate") java.util.Date checkinDate,
                                @Param("checkoutDate") java.util.Date checkoutDate);
}
