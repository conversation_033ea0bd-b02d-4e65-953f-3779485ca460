package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.HotelOrderStatusLog;
import org.apache.ibatis.annotations.Param;

/**
 * 订单状态变更记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface HotelOrderStatusLogMapper 
{
    /**
     * 查询订单状态变更记录
     * 
     * @param logId 订单状态变更记录主键
     * @return 订单状态变更记录
     */
    public HotelOrderStatusLog selectHotelOrderStatusLogByLogId(Long logId);

    /**
     * 查询订单状态变更记录列表
     * 
     * @param hotelOrderStatusLog 订单状态变更记录
     * @return 订单状态变更记录集合
     */
    public List<HotelOrderStatusLog> selectHotelOrderStatusLogList(HotelOrderStatusLog hotelOrderStatusLog);

    /**
     * 根据订单ID查询状态变更记录
     * 
     * @param orderId 订单ID
     * @return 订单状态变更记录集合
     */
    public List<HotelOrderStatusLog> selectHotelOrderStatusLogByOrderId(Long orderId);

    /**
     * 根据订单号查询状态变更记录
     * 
     * @param orderNo 订单号
     * @return 订单状态变更记录集合
     */
    public List<HotelOrderStatusLog> selectHotelOrderStatusLogByOrderNo(String orderNo);

    /**
     * 新增订单状态变更记录
     * 
     * @param hotelOrderStatusLog 订单状态变更记录
     * @return 结果
     */
    public int insertHotelOrderStatusLog(HotelOrderStatusLog hotelOrderStatusLog);

    /**
     * 批量新增订单状态变更记录
     * 
     * @param hotelOrderStatusLogList 订单状态变更记录列表
     * @return 结果
     */
    public int batchInsertHotelOrderStatusLog(List<HotelOrderStatusLog> hotelOrderStatusLogList);

    /**
     * 记录订单状态变更
     * 
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param statusType 状态类型
     * @param changeReason 变更原因
     * @param operator 操作人
     * @return 结果
     */
    public int logStatusChange(@Param("orderId") Long orderId,
                               @Param("orderNo") String orderNo,
                               @Param("oldStatus") String oldStatus,
                               @Param("newStatus") String newStatus,
                               @Param("statusType") String statusType,
                               @Param("changeReason") String changeReason,
                               @Param("operator") String operator);

    /**
     * 修改订单状态变更记录
     * 
     * @param hotelOrderStatusLog 订单状态变更记录
     * @return 结果
     */
    public int updateHotelOrderStatusLog(HotelOrderStatusLog hotelOrderStatusLog);

    /**
     * 删除订单状态变更记录
     * 
     * @param logId 订单状态变更记录主键
     * @return 结果
     */
    public int deleteHotelOrderStatusLogByLogId(Long logId);

    /**
     * 批量删除订单状态变更记录
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHotelOrderStatusLogByLogIds(Long[] logIds);

    /**
     * 根据订单ID删除状态变更记录
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteHotelOrderStatusLogByOrderId(Long orderId);
}
