package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.Conference;

/**
 * 会议Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface ConferenceMapper 
{
    /**
     * 查询会议
     * 
     * @param id 会议主键
     * @return 会议
     */
    public Conference selectConferenceById(Long id);

    /**
     * 查询会议列表
     * 
     * @param conference 会议
     * @return 会议集合
     */
    public List<Conference> selectConferenceList(Conference conference);

    /**
     * 新增会议
     * 
     * @param conference 会议
     * @return 结果
     */
    public int insertConference(Conference conference);

    /**
     * 修改会议
     * 
     * @param conference 会议
     * @return 结果
     */
    public int updateConference(Conference conference);

    /**
     * 删除会议
     * 
     * @param id 会议主键
     * @return 结果
     */
    public int deleteConferenceById(Long id);

    /**
     * 批量删除会议
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConferenceByIds(Long[] ids);

    /**
     * 查询启用的会议列表（供小程序使用）
     * 
     * @return 会议集合
     */
    public List<Conference> selectEnabledConferenceList();
}
