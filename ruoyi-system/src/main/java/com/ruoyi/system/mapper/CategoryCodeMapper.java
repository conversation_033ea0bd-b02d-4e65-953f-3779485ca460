package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CategoryCode;
import org.apache.ibatis.annotations.Param;

/**
 * 识别码Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface CategoryCodeMapper 
{
    /**
     * 查询识别码
     * 
     * @param categoryId 识别码主键
     * @param conference 会议ID
     * @return 识别码
     */
    CategoryCode selectCategoryCodeById(@Param("categoryId") String categoryId, @Param("conference") Long conference);

    /**
     * 查询识别码列表
     * 
     * @param categoryCode 识别码
     * @return 识别码集合
     */
    public List<CategoryCode> selectCategoryCodeList(CategoryCode categoryCode);

    /**
     * 新增识别码
     * 
     * @param categoryCode 识别码
     * @return 结果
     */
    public int insertCategoryCode(CategoryCode categoryCode);

    /**
     * 修改识别码
     * 
     * @param categoryCode 识别码
     * @return 结果
     */
    public int updateCategoryCode(CategoryCode categoryCode);

    /**
     * 删除识别码
     * 
     * @param categoryId 识别码主键
     * @param conference 会议ID
     * @return 结果
     */
    public int deleteCategoryCodeById(String categoryId, Long conference);

    /**
     * 批量删除识别码
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCategoryCodeByIds(String[] categoryIds);

    /**
     * 验证识别码是否存在
     * 
     * @param categoryId 识别码
     * @param conference 会议ID
     * @return 识别码信息
     */
    CategoryCode validateCategoryCode(@Param("categoryId") String categoryId, @Param("conference") Long conference);
}
