package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单状态变更记录对象 hotel_order_status_log
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class HotelOrderStatusLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 原状态 */
    @Excel(name = "原状态")
    private String oldStatus;

    /** 新状态 */
    @Excel(name = "新状态")
    private String newStatus;

    /** 状态类型 */
    @Excel(name = "状态类型")
    private String statusType;

    /** 变更原因 */
    @Excel(name = "变更原因")
    private String changeReason;

    /** 操作人 */
    @Excel(name = "操作人")
    private String operator;

    // 状态类型枚举
    public static class StatusType {
        public static final String ORDER = "ORDER";         // 订单状态
        public static final String PAYMENT = "PAYMENT";     // 支付状态
    }

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setOldStatus(String oldStatus) 
    {
        this.oldStatus = oldStatus;
    }

    public String getOldStatus() 
    {
        return oldStatus;
    }
    public void setNewStatus(String newStatus) 
    {
        this.newStatus = newStatus;
    }

    public String getNewStatus() 
    {
        return newStatus;
    }
    public void setStatusType(String statusType) 
    {
        this.statusType = statusType;
    }

    public String getStatusType() 
    {
        return statusType;
    }
    public void setChangeReason(String changeReason) 
    {
        this.changeReason = changeReason;
    }

    public String getChangeReason() 
    {
        return changeReason;
    }
    public void setOperator(String operator) 
    {
        this.operator = operator;
    }

    public String getOperator() 
    {
        return operator;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("orderId", getOrderId())
            .append("orderNo", getOrderNo())
            .append("oldStatus", getOldStatus())
            .append("newStatus", getNewStatus())
            .append("statusType", getStatusType())
            .append("changeReason", getChangeReason())
            .append("operator", getOperator())
            .append("createTime", getCreateTime())
            .toString();
    }
}
