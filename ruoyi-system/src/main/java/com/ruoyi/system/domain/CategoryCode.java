package com.ruoyi.system.domain;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 识别码对象 category_code
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class CategoryCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 识别码 */
    @Excel(name = "识别码")
    private String categoryId;

    /** 会议ID */
    @Excel(name = "会议ID")
    private Long conference;

    /** 房间数量 */
    @Excel(name = "房间数量")
    private Integer roomCount;

    /** 会议名称（关联查询用） */
    private String conferenceTitle;

    /** 关联的房型列表 */
    private List<Room> roomList;

    /** 房型ID列表（用于前端传参） */
    private List<Long> roomIds;

    /** 房型数量映射（用于前端传参，key为房型ID，value为数量） */
    private java.util.Map<Long, Integer> roomCountMap;

    public CategoryCode() {
        this.roomList = new java.util.ArrayList<>();
    }

    public void setCategoryId(String categoryId)
    {
        this.categoryId = categoryId;
    }

    public String getCategoryId() 
    {
        return categoryId;
    }
    public void setConference(Long conference) 
    {
        this.conference = conference;
    }

    public Long getConference()
    {
        return conference;
    }

    public void setRoomCount(Integer roomCount)
    {
        this.roomCount = roomCount;
    }

    public Integer getRoomCount()
    {
        return roomCount;
    }

    public String getConferenceTitle()
    {
        return conferenceTitle;
    }

    public void setConferenceTitle(String conferenceTitle)
    {
        this.conferenceTitle = conferenceTitle;
    }

    public List<Room> getRoomList()
    {
        return roomList;
    }

    public void setRoomList(List<Room> roomList)
    {
        this.roomList = roomList;
    }

    public List<Long> getRoomIds()
    {
        return roomIds;
    }

    public void setRoomIds(List<Long> roomIds)
    {
        this.roomIds = roomIds;
    }

    public java.util.Map<Long, Integer> getRoomCountMap()
    {
        return roomCountMap;
    }

    public void setRoomCountMap(java.util.Map<Long, Integer> roomCountMap)
    {
        this.roomCountMap = roomCountMap;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("categoryId", getCategoryId())
            .append("conference", getConference())
            .append("roomCount", getRoomCount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("conferenceTitle", getConferenceTitle())
            .append("roomList", getRoomList())
            .append("roomIds", getRoomIds())
            .append("roomCountMap", getRoomCountMap())
            .toString();
    }
}
