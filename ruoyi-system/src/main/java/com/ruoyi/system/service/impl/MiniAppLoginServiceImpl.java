package com.ruoyi.system.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.MiniAppLoginResult;
import com.ruoyi.common.core.domain.WechatApiResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.wechat.WechatMiniAppUtils;
import com.ruoyi.common.utils.wechat.WechatSessionManager;
import com.ruoyi.common.utils.wechat.MiniAppTokenUtils;
import com.ruoyi.common.core.domain.entity.User;
import com.ruoyi.system.service.IMiniAppLoginService;
import com.ruoyi.system.service.IUserService;

/**
 * 小程序登录服务实现类
 *
 * <AUTHOR>
 */
@Service
public class MiniAppLoginServiceImpl implements IMiniAppLoginService {
    private static final Logger log = LoggerFactory.getLogger(MiniAppLoginServiceImpl.class);

    @Autowired
    private WechatMiniAppUtils wechatMiniAppUtils;

    @Autowired
    private IUserService userService;

    /**
     * 小程序登录
     *
     * @param jsCode 小程序登录时获取的code
     * @return 登录结果
     */
    @Override
    public MiniAppLoginResult login(String jsCode) {
        return loginWithUserInfo(jsCode, null, null, null);
    }

    /**
     * 小程序登录（带用户信息）
     *
     * @param jsCode    小程序登录时获取的code
     * @param nickName  用户昵称
     * @param avatarUrl 用户头像URL
     * @param gender    用户性别
     * @return 登录结果
     */
    @Override
    public MiniAppLoginResult loginWithUserInfo(String jsCode, String nickName, String avatarUrl, String gender) {
        try {
            // 参数验证
            if (StringUtils.isEmpty(jsCode)) {
                log.error("小程序登录失败：jsCode不能为空");
                return null;
            }

            // 调用微信接口获取openid和session_key
            WechatApiResult wechatResult = wechatMiniAppUtils.code2Session(jsCode);

            if (!wechatResult.isSuccess()) {
                log.error("小程序登录失败：微信接口调用失败，错误码：{}，错误信息：{}",
                        wechatResult.getErrCode(), wechatResult.getErrMsg());
                return null;
            }

            String openid = wechatResult.getOpenId();
            String unionid = wechatResult.getUnionId();
            String sessionKey = wechatResult.getSessionKey();

            if (StringUtils.isEmpty(openid)) {
                log.error("小程序登录失败：获取openid失败");
                return null;
            }

            log.info("小程序登录：获取到openid：{}，unionid：{}", openid, unionid);

            // 安全存储sessionKey到服务端，不下发到客户端
            if (StringUtils.isNotEmpty(sessionKey))
            {
                WechatSessionManager.storeSessionKey(openid, sessionKey);
                log.debug("已安全存储sessionKey，openid：{}", openid);
            }

            // 查询用户是否存在
            User existUser = userService.selectUserByOpenid(openid);
            boolean isNewUser = (existUser == null);

            User user;
            if (isNewUser) {
                // 用户不存在，创建新用户
                log.info("创建新用户，openid：{}", openid);
                user = userService.loginOrRegisterByWechat(openid, unionid, nickName);

                if (user == null) {
                    log.error("小程序登录失败：创建用户失败");
                    return null;
                }

                // 如果有额外的用户信息，更新用户
                if (StringUtils.isNotEmpty(avatarUrl) || StringUtils.isNotEmpty(gender)) {
                    updateUserInfo(user, avatarUrl, gender);
                }
            } else {
                // 用户已存在，更新登录信息
                log.info("用户已存在，openid：{}，用户ID：{}", openid, existUser.getId());
                user = userService.loginOrRegisterByWechat(openid, unionid, nickName);

                // 如果有额外的用户信息，更新用户
                if (StringUtils.isNotEmpty(avatarUrl) || StringUtils.isNotEmpty(gender)) {
                    updateUserInfo(user, avatarUrl, gender);
                }
            }

            // 生成小程序token
            String token = MiniAppTokenUtils.generateToken(openid, user.getId());
            if (StringUtils.isEmpty(token)) {
                log.error("小程序登录失败：生成token失败");
                return null;
            }

            // 构建登录结果（包含token但不包含sessionKey，sessionKey已安全存储在服务端）
            MiniAppLoginResult result = new MiniAppLoginResult(isNewUser, user, token);

            log.info("小程序登录成功，openid：{}，用户ID：{}，是否新用户：{}", openid, user.getId(), isNewUser);
            return result;
        } catch (Exception e) {
            log.error("小程序登录异常", e);
            return null;
        }
    }

    /**
     * 更新用户信息
     *
     * @param user      用户对象
     * @param avatarUrl 头像URL
     * @param gender    性别
     */
    private void updateUserInfo(User user, String avatarUrl, String gender) {
        try {
            boolean needUpdate = false;

            // 这里可以根据需要添加头像字段到User实体类
            // if (StringUtils.isNotEmpty(avatarUrl) && !avatarUrl.equals(user.getAvatarUrl()))
            // {
            //     user.setAvatarUrl(avatarUrl);
            //     needUpdate = true;
            // }

            if (StringUtils.isNotEmpty(gender) && !gender.equals(user.getGender())) {
                user.setGender(gender);
                needUpdate = true;
            }

            if (needUpdate) {
                userService.updateUser(user);
                log.info("更新用户信息成功，用户ID：{}", user.getId());
            }
        } catch (Exception e) {
            log.error("更新用户信息失败，用户ID：{}", user.getId(), e);
        }
    }

    /**
     * 获取用户的sessionKey（仅供服务端内部使用）
     *
     * @param openid 用户openid
     * @return sessionKey
     */
    @Override
    public String getSessionKey(String openid)
    {
        return WechatSessionManager.getSessionKey(openid);
    }

    /**
     * 检查用户是否有有效的sessionKey
     *
     * @param openid 用户openid
     * @return 是否有有效的sessionKey
     */
    @Override
    public boolean hasValidSessionKey(String openid)
    {
        return WechatSessionManager.hasValidSessionKey(openid);
    }
}
