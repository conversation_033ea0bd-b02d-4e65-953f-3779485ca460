package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.WechatPayParams;
import com.ruoyi.common.core.domain.WechatPayV3Result;
import com.ruoyi.common.core.domain.WechatRefundResult;
import com.ruoyi.common.core.domain.request.wechat.WechatPayRequest;

/**
 * 微信支付V3版本服务接口
 *
 * <AUTHOR>
 */
public interface IWechatPayService
{
    /**
     * 创建支付订单
     *
     * @param payRequest 支付请求参数
     * @return 小程序支付参数
     */
    WechatPayParams createPayOrder(WechatPayRequest payRequest);

    /**
     * 使用已有prepay_id重新生成支付参数
     *
     * @param prepayId 预支付交易会话标识
     * @return 小程序支付参数
     */
    WechatPayParams regeneratePayParams(String prepayId);

    /**
     * 查询支付订单状态
     *
     * @param outTradeNo 商户订单号
     * @return 支付结果
     */
    WechatPayV3Result queryPayOrder(String outTradeNo);

    /**
     * 处理支付回调通知
     *
     * @param requestBody 微信回调的请求体
     * @param headers 请求头信息
     * @return 处理结果
     */
    String handlePayNotify(String requestBody, java.util.Map<String, String> headers);

    /**
     * 申请退款
     *
     * @param outTradeNo 商户订单号
     * @param outRefundNo 商户退款单号
     * @param refundAmount 退款金额（分）
     * @param totalAmount 原订单金额（分）
     * @param refundReason 退款原因
     * @return 退款结果
     */
    WechatRefundResult refundOrder(String outTradeNo, String outRefundNo,
                                  Long refundAmount, Long totalAmount, String refundReason);

    /**
     * 查询退款状态
     *
     * @param outRefundNo 商户退款单号
     * @return 退款查询结果
     */
    WechatRefundResult queryRefund(String outRefundNo);

    /**
     * 根据微信交易号查询支付订单状态
     *
     * @param transactionId 微信交易号
     * @return 支付结果
     */
    WechatPayV3Result queryPayOrderByTransactionId(String transactionId);
}
