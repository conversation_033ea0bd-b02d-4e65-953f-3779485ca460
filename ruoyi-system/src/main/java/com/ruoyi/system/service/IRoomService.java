package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Room;

/**
 * 房型Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface IRoomService 
{
    /**
     * 查询房型
     * 
     * @param id 房型主键
     * @return 房型
     */
    public Room selectRoomById(Long id);

    /**
     * 查询房型列表
     * 
     * @param room 房型
     * @return 房型集合
     */
    public List<Room> selectRoomList(Room room);

    /**
     * 新增房型
     * 
     * @param room 房型
     * @return 结果
     */
    public int insertRoom(Room room);

    /**
     * 修改房型
     * 
     * @param room 房型
     * @return 结果
     */
    public int updateRoom(Room room);

    /**
     * 批量删除房型
     * 
     * @param ids 需要删除的房型主键集合
     * @return 结果
     */
    public int deleteRoomByIds(Long[] ids);

    /**
     * 删除房型信息
     * 
     * @param id 房型主键
     * @return 结果
     */
    public int deleteRoomById(Long id);

    /**
     * 根据会议ID查询房型列表
     * 
     * @param conferenceId 会议ID
     * @return 房型集合
     */
    public List<Room> selectRoomListByConferenceId(Long conferenceId);
}
