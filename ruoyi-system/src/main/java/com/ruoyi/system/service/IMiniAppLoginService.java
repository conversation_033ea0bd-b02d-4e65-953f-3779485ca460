package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.MiniAppLoginResult;

/**
 * 小程序登录服务接口
 * 
 * <AUTHOR>
 */
public interface IMiniAppLoginService
{
    /**
     * 小程序登录
     * 
     * @param jsCode 小程序登录时获取的code
     * @return 登录结果
     */
    public MiniAppLoginResult login(String jsCode);

    /**
     * 小程序登录（带用户信息）
     *
     * @param jsCode 小程序登录时获取的code
     * @param nickName 用户昵称
     * @param avatarUrl 用户头像URL
     * @param gender 用户性别
     * @return 登录结果
     */
    public MiniAppLoginResult loginWithUserInfo(String jsCode, String nickName, String avatarUrl, String gender);

    /**
     * 获取用户的sessionKey（仅供服务端内部使用）
     *
     * @param openid 用户openid
     * @return sessionKey
     */
    public String getSessionKey(String openid);

    /**
     * 检查用户是否有有效的sessionKey
     *
     * @param openid 用户openid
     * @return 是否有有效的sessionKey
     */
    public boolean hasValidSessionKey(String openid);
}
