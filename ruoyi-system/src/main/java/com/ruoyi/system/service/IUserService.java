package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.core.domain.entity.User;

/**
 * 用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IUserService 
{
    /**
     * 查询用户
     * 
     * @param id 用户主键
     * @return 用户
     */
    public User selectUserById(Integer id);

    /**
     * 查询用户列表
     * 
     * @param user 用户
     * @return 用户集合
     */
    public List<User> selectUserList(User user);

    /**
     * 新增用户
     * 
     * @param user 用户
     * @return 结果
     */
    public int insertUser(User user);

    /**
     * 修改用户
     * 
     * @param user 用户
     * @return 结果
     */
    public int updateUser(User user);

    /**
     * 批量删除用户
     * 
     * @param ids 需要删除的用户主键集合
     * @return 结果
     */
    public int deleteUserByIds(Integer[] ids);

    /**
     * 删除用户信息
     * 
     * @param id 用户主键
     * @return 结果
     */
    public int deleteUserById(Integer id);

    /**
     * 根据微信openid查询用户
     * 
     * @param openid 微信openid
     * @return 用户
     */
    public User selectUserByOpenid(String openid);

    /**
     * 根据手机号查询用户
     * 
     * @param phoneNumber 手机号
     * @return 用户
     */
    public User selectUserByPhoneNumber(String phoneNumber);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户
     */
    public User selectUserByEmail(String email);

    /**
     * 校验用户手机号是否唯一
     * 
     * @param user 用户信息
     * @return 结果
     */
    public String checkPhoneUnique(User user);

    /**
     * 校验用户邮箱是否唯一
     * 
     * @param user 用户信息
     * @return 结果
     */
    public String checkEmailUnique(User user);

    /**
     * 校验用户openid是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public String checkOpenidUnique(User user);

    /**
     * 微信小程序登录或注册用户
     *
     * @param openid 微信openid
     * @param unionid 微信unionid
     * @param nickName 用户昵称
     * @return 用户信息
     */
    public User loginOrRegisterByWechat(String openid, String unionid, String nickName);
}
