package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.ArrayList;

import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.CategoryCodeMapper;
import com.ruoyi.system.domain.CategoryCode;
import com.ruoyi.system.domain.CategoryCodeRoom;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.service.ICategoryCodeService;
import com.ruoyi.system.service.ICategoryCodeRoomService;
import com.ruoyi.system.service.IRoomService;

/**
 * 识别码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class CategoryCodeServiceImpl implements ICategoryCodeService {
    @Autowired
    private CategoryCodeMapper categoryCodeMapper;

    @Autowired
    private ICategoryCodeRoomService categoryCodeRoomService;

    @Autowired
    private IRoomService roomService;

    /**
     * 查询识别码
     *
     * @param categoryId 识别码主键
     * @param conference 会议ID
     * @return 识别码
     */
    @Override
    public CategoryCode selectCategoryCodeById(String categoryId, Long conference) {
        CategoryCode categoryCode = categoryCodeMapper.selectCategoryCodeById(categoryId, conference);
        if (categoryCode != null) {
            List<Room> roomList = new ArrayList<>();
            try {
                // 查询关联的房型列表
                List<CategoryCodeRoom> roomRelations = categoryCodeRoomService.selectRoomsByCategoryCode(categoryId, conference);
                if (roomRelations != null) {
                    for (CategoryCodeRoom relation : roomRelations) {
                        Room room = roomService.selectRoomById(relation.getRoomId());
                        if (room != null) {
                            // 将房型数量信息设置到Room对象中（临时存储）
                            room.setRoomCount(relation.getRoomCount());
                            roomList.add(room);
                        }
                    }
                }
            } catch (Exception e) {
                // 如果查询出错，记录日志但不影响主流程
                System.err.println("查询识别码 " + categoryId + " 的房型关联时出错: " + e.getMessage());
            }
            // 确保始终设置roomList，即使是空列表
            categoryCode.setRoomList(roomList);
        }
        return categoryCode;
    }

    /**
     * 查询识别码列表
     *
     * @param categoryCode 识别码
     * @return 识别码
     */
    @Override
    public List<CategoryCode> selectCategoryCodeList(CategoryCode categoryCode) {
        List<CategoryCode> categoryCodeList = categoryCodeMapper.selectCategoryCodeList(categoryCode);
        // 为每个识别码查询关联的房型列表
        for (CategoryCode code : categoryCodeList) {
            List<Room> roomList = new ArrayList<>();
            try {
                if (categoryCodeRoomService != null) {
                    List<CategoryCodeRoom> roomRelations = categoryCodeRoomService.selectRoomsByCategoryCode(code.getCategoryId(), code.getConference());

                    if (roomRelations != null && !roomRelations.isEmpty()) {
                        for (CategoryCodeRoom relation : roomRelations) {
                            if (roomService != null) {
                                Room room = roomService.selectRoomById(relation.getRoomId());
                                if (room != null) {
                                    // 将房型数量信息设置到Room对象中（临时存储）
                                    room.setRoomCount(relation.getRoomCount());
                                    roomList.add(room);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // 如果查询出错，记录日志但不影响主流程
                System.err.println("查询识别码 " + code.getCategoryId() + " 的房型关联时出错: " + e.getMessage());
                e.printStackTrace();
            }
            // 确保始终设置roomList，即使是空列表
            code.setRoomList(roomList);
        }
        return categoryCodeList;
    }

    /**
     * 新增识别码
     *
     * @param categoryCode 识别码
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCategoryCode(CategoryCode categoryCode) {
        categoryCode.setCreateTime(DateUtils.getNowDate());
        int result = categoryCodeMapper.insertCategoryCode(categoryCode);

        // 保存房型关联关系
        if (categoryCode.getRoomIds() != null && !categoryCode.getRoomIds().isEmpty()) {
            List<CategoryCodeRoom> roomRelations = new ArrayList<>();
            for (Long roomId : categoryCode.getRoomIds()) {
                CategoryCodeRoom relation = new CategoryCodeRoom();
                relation.setCategoryId(categoryCode.getCategoryId());
                relation.setConference(categoryCode.getConference());
                relation.setRoomId(roomId);

                // 设置该房型的数量，如果没有指定则默认为1
                Integer roomCount = 1;
                if (categoryCode.getRoomCountMap() != null && categoryCode.getRoomCountMap().containsKey(roomId)) {
                    roomCount = categoryCode.getRoomCountMap().get(roomId);
                }
                relation.setRoomCount(roomCount);

                roomRelations.add(relation);
            }
            categoryCodeRoomService.batchInsertCategoryCodeRoom(roomRelations);
        }

        return result;
    }

    /**
     * 修改识别码
     *
     * @param categoryCode 识别码
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCategoryCode(CategoryCode categoryCode) {
        categoryCode.setUpdateTime(DateUtils.getNowDate());
        int result = categoryCodeMapper.updateCategoryCode(categoryCode);

        // 先删除原有的房型关联关系
        categoryCodeRoomService.deleteByCategoryCode(categoryCode.getCategoryId(), categoryCode.getConference());

        // 重新保存房型关联关系
        if (categoryCode.getRoomIds() != null && !categoryCode.getRoomIds().isEmpty()) {
            List<CategoryCodeRoom> roomRelations = new ArrayList<>();
            for (Long roomId : categoryCode.getRoomIds()) {
                CategoryCodeRoom relation = new CategoryCodeRoom();
                relation.setCategoryId(categoryCode.getCategoryId());
                relation.setConference(categoryCode.getConference());
                relation.setRoomId(roomId);

                // 设置该房型的数量，如果没有指定则默认为1
                Integer roomCount = 1;
                if (categoryCode.getRoomCountMap() != null && categoryCode.getRoomCountMap().containsKey(roomId)) {
                    roomCount = categoryCode.getRoomCountMap().get(roomId);
                }
                relation.setRoomCount(roomCount);

                roomRelations.add(relation);
            }
            categoryCodeRoomService.batchInsertCategoryCodeRoom(roomRelations);
        }

        return result;
    }

    /**
     * 批量删除识别码
     *
     * @param categoryIds 需要删除的识别码主键
     * @return 结果
     */
    @Override
    public int deleteCategoryCodeByIds(String[] categoryIds) {
        return categoryCodeMapper.deleteCategoryCodeByIds(categoryIds);
    }

    /**
     * 删除识别码信息
     *
     * @param categoryId 识别码主键
     * @param conference 会议ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCategoryCodeById(String categoryId, Long conference) {
        // 先删除房型关联关系
        categoryCodeRoomService.deleteByCategoryCode(categoryId, conference);
        // 再删除识别码
        return categoryCodeMapper.deleteCategoryCodeById(categoryId, conference);
    }

    /**
     * 验证识别码是否存在
     *
     * @param categoryId 识别码
     * @param conference 会议ID
     * @return 识别码信息
     */
    @Override
    public CategoryCode validateCategoryCode(String categoryId, Long conference) {
        return categoryCodeMapper.validateCategoryCode(categoryId, conference);
    }
}
