package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CategoryCodeRoom;

/**
 * 识别码房型关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface ICategoryCodeRoomService 
{
    /**
     * 查询识别码房型关联
     * 
     * @param id 识别码房型关联主键
     * @return 识别码房型关联
     */
    public CategoryCodeRoom selectCategoryCodeRoomById(Long id);

    /**
     * 查询识别码房型关联列表
     * 
     * @param categoryCodeRoom 识别码房型关联
     * @return 识别码房型关联集合
     */
    public List<CategoryCodeRoom> selectCategoryCodeRoomList(CategoryCodeRoom categoryCodeRoom);

    /**
     * 根据识别码和会议ID查询房型关联列表
     * 
     * @param categoryId 识别码
     * @param conference 会议ID
     * @return 识别码房型关联集合
     */
    public List<CategoryCodeRoom> selectRoomsByCategoryCode(String categoryId, Long conference);

    /**
     * 新增识别码房型关联
     * 
     * @param categoryCodeRoom 识别码房型关联
     * @return 结果
     */
    public int insertCategoryCodeRoom(CategoryCodeRoom categoryCodeRoom);

    /**
     * 批量新增识别码房型关联
     * 
     * @param categoryCodeRoomList 识别码房型关联列表
     * @return 结果
     */
    public int batchInsertCategoryCodeRoom(List<CategoryCodeRoom> categoryCodeRoomList);

    /**
     * 修改识别码房型关联
     * 
     * @param categoryCodeRoom 识别码房型关联
     * @return 结果
     */
    public int updateCategoryCodeRoom(CategoryCodeRoom categoryCodeRoom);

    /**
     * 批量删除识别码房型关联
     * 
     * @param ids 需要删除的识别码房型关联主键集合
     * @return 结果
     */
    public int deleteCategoryCodeRoomByIds(Long[] ids);

    /**
     * 删除识别码房型关联信息
     * 
     * @param id 识别码房型关联主键
     * @return 结果
     */
    public int deleteCategoryCodeRoomById(Long id);

    /**
     * 根据识别码和会议ID删除房型关联
     *
     * @param categoryId 识别码
     * @param conference 会议ID
     * @return 结果
     */
    public int deleteByCategoryCode(String categoryId, Long conference);

    /**
     * 获取指定房间的总库存数量
     *
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     * @param categoryId 识别码
     * @return 房间总库存数量，如果未找到则返回0
     */
    public int getRoomInventory(Long roomId, Long conferenceId, String categoryId);
}
