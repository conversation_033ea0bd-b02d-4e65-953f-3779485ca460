package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.RoomMapper;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.service.IRoomService;

/**
 * 房型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class RoomServiceImpl implements IRoomService 
{
    @Autowired
    private RoomMapper roomMapper;

    /**
     * 查询房型
     * 
     * @param id 房型主键
     * @return 房型
     */
    @Override
    public Room selectRoomById(Long id)
    {
        return roomMapper.selectRoomById(id);
    }

    /**
     * 查询房型列表
     * 
     * @param room 房型
     * @return 房型
     */
    @Override
    public List<Room> selectRoomList(Room room)
    {
        return roomMapper.selectRoomList(room);
    }

    /**
     * 新增房型
     * 
     * @param room 房型
     * @return 结果
     */
    @Override
    public int insertRoom(Room room)
    {
        room.setCreateTime(DateUtils.getNowDate());
        return roomMapper.insertRoom(room);
    }

    /**
     * 修改房型
     * 
     * @param room 房型
     * @return 结果
     */
    @Override
    public int updateRoom(Room room)
    {
        room.setUpdateTime(DateUtils.getNowDate());
        return roomMapper.updateRoom(room);
    }

    /**
     * 批量删除房型
     * 
     * @param ids 需要删除的房型主键
     * @return 结果
     */
    @Override
    public int deleteRoomByIds(Long[] ids)
    {
        return roomMapper.deleteRoomByIds(ids);
    }

    /**
     * 删除房型信息
     * 
     * @param id 房型主键
     * @return 结果
     */
    @Override
    public int deleteRoomById(Long id)
    {
        return roomMapper.deleteRoomById(id);
    }

    /**
     * 根据会议ID查询房型列表
     * 
     * @param conferenceId 会议ID
     * @return 房型集合
     */
    @Override
    public List<Room> selectRoomListByConferenceId(Long conferenceId)
    {
        return roomMapper.selectRoomListByConferenceId(conferenceId);
    }
}
