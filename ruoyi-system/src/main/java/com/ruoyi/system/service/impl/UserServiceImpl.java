package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.UserMapper;
import com.ruoyi.common.core.domain.entity.User;
import com.ruoyi.system.service.IUserService;

/**
 * 用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class UserServiceImpl implements IUserService {
    @Autowired
    private UserMapper userMapper;

    /**
     * 查询用户
     *
     * @param id 用户主键
     * @return 用户
     */
    @Override
    public User selectUserById(Integer id) {
        return userMapper.selectUserById(id);
    }

    /**
     * 查询用户列表
     *
     * @param user 用户
     * @return 用户
     */
    @Override
    public List<User> selectUserList(User user) {
        return userMapper.selectUserList(user);
    }

    /**
     * 新增用户
     *
     * @param user 用户
     * @return 结果
     */
    @Override
    public int insertUser(User user) {
        user.setCreateTime(DateUtils.getNowDate());
        return userMapper.insertUser(user);
    }

    /**
     * 修改用户
     *
     * @param user 用户
     * @return 结果
     */
    @Override
    public int updateUser(User user) {
        user.setUpdateTime(DateUtils.getNowDate());
        return userMapper.updateUser(user);
    }

    /**
     * 批量删除用户
     *
     * @param ids 需要删除的用户主键
     * @return 结果
     */
    @Override
    public int deleteUserByIds(Integer[] ids) {
        return userMapper.deleteUserByIds(ids);
    }

    /**
     * 删除用户信息
     *
     * @param id 用户主键
     * @return 结果
     */
    @Override
    public int deleteUserById(Integer id) {
        return userMapper.deleteUserById(id);
    }

    /**
     * 根据微信openid查询用户
     *
     * @param openid 微信openid
     * @return 用户
     */
    @Override
    public User selectUserByOpenid(String openid) {
        return userMapper.selectUserByOpenid(openid);
    }

    /**
     * 根据手机号查询用户
     *
     * @param phoneNumber 手机号
     * @return 用户
     */
    @Override
    public User selectUserByPhoneNumber(String phoneNumber) {
        return userMapper.selectUserByPhoneNumber(phoneNumber);
    }

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户
     */
    @Override
    public User selectUserByEmail(String email) {
        return userMapper.selectUserByEmail(email);
    }

    /**
     * 校验用户手机号是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkPhoneUnique(User user) {
        long userId = StringUtils.isNull(user.getId()) ? -1 : user.getId();
        User info = userMapper.selectUserByPhoneNumber(user.getPhoneNumber());
        if (StringUtils.isNotNull(info) && info.getId().intValue() != (int) userId) {
            return "1";
        }
        return "0";
    }

    /**
     * 校验用户邮箱是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkEmailUnique(User user) {
        long userId = StringUtils.isNull(user.getId()) ? -1 : user.getId();
        User info = userMapper.selectUserByEmail(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getId() != userId) {
            return "1";
        }
        return "0";
    }

    /**
     * 校验用户openid是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkOpenidUnique(User user) {
        long userId = StringUtils.isNull(user.getId()) ? -1 : user.getId();
        User info = userMapper.selectUserByOpenid(user.getOpenid());
        if (StringUtils.isNotNull(info) && info.getId() != userId) {
            return "1";
        }
        return "0";
    }

    /**
     * 微信小程序登录或注册用户
     *
     * @param openid   微信openid
     * @param unionid  微信unionid
     * @param nickName 用户昵称
     * @return 用户信息
     */
    @Override
    public User loginOrRegisterByWechat(String openid, String unionid, String nickName) {
        // 先根据openid查询用户是否存在
        User existUser = userMapper.selectUserByOpenid(openid);

        if (existUser != null) {
            // 用户已存在，更新最后登录时间
            existUser.setUpdateTime(DateUtils.getNowDate());
            userMapper.updateUser(existUser);
            return existUser;
        }

        // 用户不存在，创建新用户
        User newUser = new User();
        newUser.setOpenid(openid);
        newUser.setUnionid(unionid);
        newUser.setNickName(StringUtils.isNotEmpty(nickName) ? nickName : "微信用户");
        newUser.setRegSource("miniapp");
        newUser.setCreateTime(DateUtils.getNowDate());

        // 插入新用户
        int result = userMapper.insertUser(newUser);

        if (result > 0) {
            return newUser;
        }

        return null;
    }
}
