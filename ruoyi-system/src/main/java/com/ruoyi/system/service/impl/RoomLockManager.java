package com.ruoyi.system.service.impl;

import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;

/**
 * 房间锁管理器
 * 用于管理按房间ID的分段锁，防止并发超卖问题
 * 
 * <AUTHOR>
 */
@Component
public class RoomLockManager {
    
    private static final Logger log = LoggerFactory.getLogger(RoomLockManager.class);
    
    /**
     * 房间锁映射表
     * Key: roomId_conferenceId
     * Value: ReentrantLock
     */
    private final ConcurrentHashMap<String, ReentrantLock> roomLocks = new ConcurrentHashMap<>();
    
    /**
     * 锁的引用计数，用于清理不再使用的锁
     */
    private final ConcurrentHashMap<String, Integer> lockRefCount = new ConcurrentHashMap<>();
    
    /**
     * 获取房间锁的key
     * 
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     * @return 锁的key
     */
    private String getLockKey(Long roomId, Long conferenceId) {
        return roomId + "_" + conferenceId;
    }
    
    /**
     * 获取房间锁
     * 
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     * @return ReentrantLock
     */
    public ReentrantLock getRoomLock(Long roomId, Long conferenceId) {
        String lockKey = getLockKey(roomId, conferenceId);
        
        // 使用computeIfAbsent确保线程安全地创建锁
        ReentrantLock lock = roomLocks.computeIfAbsent(lockKey, k -> {
            log.debug("创建房间锁: {}", k);
            return new ReentrantLock();
        });
        
        // 增加引用计数
        lockRefCount.merge(lockKey, 1, Integer::sum);
        
        return lock;
    }
    
    /**
     * 尝试获取房间锁（带超时）
     * 
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取锁
     */
    public boolean tryLockRoom(Long roomId, Long conferenceId, long timeout, TimeUnit unit) {
        ReentrantLock lock = getRoomLock(roomId, conferenceId);
        try {
            boolean acquired = lock.tryLock(timeout, unit);
            if (acquired) {
                log.debug("成功获取房间锁: roomId={}, conferenceId={}", roomId, conferenceId);
            } else {
                log.warn("获取房间锁超时: roomId={}, conferenceId={}, timeout={}ms", 
                    roomId, conferenceId, unit.toMillis(timeout));
            }
            return acquired;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取房间锁被中断: roomId={}, conferenceId={}", roomId, conferenceId, e);
            return false;
        }
    }
    
    /**
     * 释放房间锁
     * 
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     */
    public void unlockRoom(Long roomId, Long conferenceId) {
        String lockKey = getLockKey(roomId, conferenceId);
        ReentrantLock lock = roomLocks.get(lockKey);
        
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
            log.debug("释放房间锁: roomId={}, conferenceId={}", roomId, conferenceId);
            
            // 减少引用计数
            Integer refCount = lockRefCount.computeIfPresent(lockKey, (k, v) -> v - 1);
            
            // 如果引用计数为0且没有线程在等待，则清理锁
            if (refCount != null && refCount <= 0 && !lock.hasQueuedThreads()) {
                roomLocks.remove(lockKey);
                lockRefCount.remove(lockKey);
                log.debug("清理房间锁: {}", lockKey);
            }
        }
    }
    
    /**
     * 执行带锁的操作
     * 
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     * @param action 要执行的操作
     * @param timeout 超时时间（毫秒）
     * @return 操作结果
     * @throws Exception 操作异常
     */
    public <T> T executeWithLock(Long roomId, Long conferenceId, 
                                 RoomLockAction<T> action, long timeout) throws Exception {
        if (!tryLockRoom(roomId, conferenceId, timeout, TimeUnit.MILLISECONDS)) {
            throw new RuntimeException("获取房间锁超时，请稍后重试");
        }
        
        try {
            return action.execute();
        } finally {
            unlockRoom(roomId, conferenceId);
        }
    }
    
    /**
     * 房间锁操作接口
     */
    @FunctionalInterface
    public interface RoomLockAction<T> {
        T execute() throws Exception;
    }
    
    /**
     * 获取当前锁的统计信息（用于监控）
     * 
     * @return 锁的数量
     */
    public int getLockCount() {
        return roomLocks.size();
    }
    
    /**
     * 清理所有未使用的锁（谨慎使用，仅在维护时调用）
     */
    public void cleanupUnusedLocks() {
        roomLocks.entrySet().removeIf(entry -> {
            ReentrantLock lock = entry.getValue();
            String key = entry.getKey();
            
            // 如果锁没有被持有且没有等待的线程，则可以清理
            if (!lock.isLocked() && !lock.hasQueuedThreads()) {
                lockRefCount.remove(key);
                log.debug("清理未使用的房间锁: {}", key);
                return true;
            }
            return false;
        });
    }
}
