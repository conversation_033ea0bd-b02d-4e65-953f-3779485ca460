package com.ruoyi.system.service.impl;

import com.ruoyi.common.config.WechatPayConfig;
import com.ruoyi.common.core.domain.WechatPayParams;
import com.ruoyi.common.core.domain.WechatPayV3Result;
import com.ruoyi.common.core.domain.WechatRefundResult;
import com.ruoyi.common.core.domain.request.wechat.WechatPayRequest;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.wechat.WechatPayV3Utils;
import com.ruoyi.system.service.IWechatPayService;
import com.ruoyi.system.service.IHotelOrderService;
import com.ruoyi.system.domain.HotelOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * 微信支付V3版本服务实现类
 *
 * <AUTHOR>
 */
@Service
public class WechatPayServiceImpl implements IWechatPayService
{
    private static final Logger log = LoggerFactory.getLogger(WechatPayServiceImpl.class);

    @Autowired
    private WechatPayV3Utils wechatPayV3Utils;

    @Autowired
    private WechatPayConfig payConfig;

    @Autowired
    private IHotelOrderService hotelOrderService;

    /**
     * 创建支付订单
     */
    @Override
    public WechatPayParams createPayOrder(WechatPayRequest payRequest)
    {
        try {
            log.info("开始创建微信支付V3订单，订单号: {}, 金额: {}", payRequest.getOutTradeNo(), payRequest.getTotalFee());

            // 调用微信V3下单接口
            WechatPayV3Result unifiedOrderResult = wechatPayV3Utils.jsapiPay(payRequest);

            if (unifiedOrderResult == null || StringUtils.isEmpty(unifiedOrderResult.getPrepayId())) {
                log.error("微信V3统一下单失败，订单号: {}, 错误信息: {}",
                    payRequest.getOutTradeNo(), unifiedOrderResult != null ? unifiedOrderResult.getTradeStateDesc() : "返回结果为空");
                return null;
            }

            // 生成小程序支付参数
            WechatPayParams payParams = wechatPayV3Utils.generateMiniAppPayParams(unifiedOrderResult.getPrepayId());

            if (payParams == null) {
                log.error("生成小程序支付参数失败，订单号: {}", payRequest.getOutTradeNo());
                return null;
            }

            log.info("创建微信支付V3订单成功，订单号: {}, prepay_id: {}",
                payRequest.getOutTradeNo(), unifiedOrderResult.getPrepayId());

            return payParams;
        } catch (Exception e) {
            log.error("创建微信支付V3订单异常，订单号: " + payRequest.getOutTradeNo(), e);
            return null;
        }
    }

    /**
     * 使用已有prepay_id重新生成支付参数
     */
    @Override
    public WechatPayParams regeneratePayParams(String prepayId)
    {
        try {
            log.info("重新生成支付参数，prepay_id: {}", prepayId);

            // 使用微信支付工具类重新生成小程序支付参数
            WechatPayParams payParams = wechatPayV3Utils.generateMiniAppPayParams(prepayId);

            if (payParams == null) {
                log.error("重新生成支付参数失败，prepay_id: {}", prepayId);
                return null;
            }

            log.info("重新生成支付参数成功，prepay_id: {}", prepayId);
            return payParams;
        } catch (Exception e) {
            log.error("重新生成支付参数异常，prepay_id: " + prepayId, e);
            return null;
        }
    }

    /**
     * 查询支付订单状态
     */
    @Override
    public WechatPayV3Result queryPayOrder(String outTradeNo)
    {
        try {
            log.info("开始查询微信支付V3订单状态，订单号: {}", outTradeNo);

            WechatPayV3Result result = wechatPayV3Utils.queryOrder(outTradeNo);

            if (result != null && result.isSuccess()) {
                log.info("查询微信支付V3订单状态成功，订单号: {}, 交易状态: {}",
                    outTradeNo, result.getTradeState());
            } else {
                log.error("查询微信支付V3订单状态失败，订单号: {}, 错误信息: {}",
                    outTradeNo, result != null ? result.getTradeStateDesc() : "返回结果为空");
            }

            return result;
        } catch (Exception e) {
            log.error("查询微信支付V3订单状态异常，订单号: " + outTradeNo, e);
            return null;
        }
    }

    /**
     * 处理支付回调通知
     */
    @Override
    public String handlePayNotify(String requestBody, java.util.Map<String, String> headers)
    {
        try {
            log.info("收到微信支付V3回调通知");
            log.debug("微信支付V3回调数据: {}", requestBody);

            // 验证V3版本的签名
            String timestamp = headers.get("Wechatpay-Timestamp");
            String nonce = headers.get("Wechatpay-Nonce");
            String signature = headers.get("Wechatpay-Signature");
            String serialNumber = headers.get("Wechatpay-Serial");

            log.debug("微信支付V3回调验签参数 - timestamp: {}, nonce: {}, signature: {}, serialNumber: {}",
                timestamp, nonce, signature, serialNumber);

            // 验证必要的头部信息
            if (StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(nonce) ||
                StringUtils.isEmpty(signature) || StringUtils.isEmpty(serialNumber)) {
                log.error("微信支付V3回调验签失败：缺少必要的头部信息");
                return buildNotifyV3Response("FAIL", "缺少必要的头部信息");
            }

            // 执行签名验证
            boolean isSignatureValid = wechatPayV3Utils.verifyNotifySignature(
                timestamp, nonce, requestBody, signature, serialNumber);

            if (!isSignatureValid) {
                log.error("微信支付V3回调验签失败：签名验证不通过");
                log.error("验签失败详情 - timestamp: {}, nonce: {}, serialNumber: {}",
                    timestamp, nonce, serialNumber);
                return buildNotifyV3Response("FAIL", "签名验证失败");
            }

            log.info("微信支付V3回调验签成功，证书序列号: {}", serialNumber);

            // 解析回调数据
            WechatPayV3Result notifyData = com.alibaba.fastjson.JSON.parseObject(requestBody, WechatPayV3Result.class);

            if (notifyData != null && "SUCCESS".equals(notifyData.getTradeState())) {
                String outTradeNo = notifyData.getOutTradeNo();
                String transactionId = notifyData.getTransactionId();
                Integer totalFee = notifyData.getAmount() != null ? notifyData.getAmount().getTotal() : 0;
                String successTime = notifyData.getSuccessTime();

                log.info("微信支付V3成功，订单号: {}, 微信订单号: {}, 金额: {}, 支付时间: {}",
                    outTradeNo, transactionId, totalFee, successTime);

                // 更新订单支付信息
                try {
                    java.util.Date paymentTime = parseWechatTime(successTime);
                    int updateResult = hotelOrderService.updateOrderPaymentInfo(
                        outTradeNo,
                        transactionId,
                        paymentTime,
                        HotelOrder.OrderStatus.PAID,
                        HotelOrder.PaymentStatus.PAID,
                        HotelOrder.PaymentMethod.WECHAT
                    );

                    if (updateResult > 0) {
                        log.info("订单支付信息更新成功，订单号: {}", outTradeNo);
                    } else {
                        log.error("订单支付信息更新失败，订单号: {}", outTradeNo);
                    }
                } catch (Exception e) {
                    log.error("更新订单支付信息异常，订单号: " + outTradeNo, e);
                }

                return buildNotifyV3Response("SUCCESS", "成功");
            } else {
                log.error("微信支付V3失败，订单号: {}, 交易状态: {}",
                    notifyData != null ? notifyData.getOutTradeNo() : "未知",
                    notifyData != null ? notifyData.getTradeState() : "未知");
                return buildNotifyV3Response("FAIL", "支付失败");
            }
        } catch (Exception e) {
            log.error("处理微信支付V3回调异常", e);
            return buildNotifyV3Response("FAIL", "系统异常");
        }
    }



    /**
     * 构建V3版本回调响应
     */
    private String buildNotifyV3Response(String code, String message)
    {
        return "{\"code\":\"" + code + "\",\"message\":\"" + message + "\"}";
    }

    /**
     * 解析微信时间格式
     */
    private java.util.Date parseWechatTime(String wechatTime)
    {
        if (StringUtils.isEmpty(wechatTime)) {
            return new java.util.Date();
        }

        try {
            // 微信V3版本时间格式：2025-01-17T15:45:00+08:00
            java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
            java.time.OffsetDateTime offsetDateTime = java.time.OffsetDateTime.parse(wechatTime, formatter);
            return java.util.Date.from(offsetDateTime.toInstant());
        } catch (Exception e) {
            log.error("解析微信时间格式失败: {}", wechatTime, e);
            return new java.util.Date();
        }
    }

    /**
     * 申请退款
     */
    @Override
    public WechatRefundResult refundOrder(String outTradeNo, String outRefundNo,
                                         Long refundAmount, Long totalAmount, String refundReason)
    {
        try {
            log.info("开始申请微信支付V3退款，订单号: {}, 退款单号: {}, 退款金额: {}",
                outTradeNo, outRefundNo, refundAmount);

            WechatRefundResult result = wechatPayV3Utils.refundOrder(outTradeNo, outRefundNo,
                refundAmount, totalAmount, refundReason);

            if (result != null && (result.isSuccess() || result.isProcessing())) {
                log.info("微信支付V3退款申请成功，订单号: {}, 退款单号: {}, 状态: {}",
                    outTradeNo, outRefundNo, result.getStatus());
            } else {
                log.error("微信支付V3退款申请失败，订单号: {}, 状态: {}",
                    outTradeNo, result != null ? result.getStatus() : "返回结果为空");
            }

            return result;
        } catch (Exception e) {
            log.error("微信支付V3退款申请异常，订单号: " + outTradeNo, e);
            return null;
        }
    }

    /**
     * 查询退款状态
     */
    @Override
    public WechatRefundResult queryRefund(String outRefundNo)
    {
        try {
            log.info("开始查询微信支付V3退款状态，退款单号: {}", outRefundNo);

            WechatRefundResult result = wechatPayV3Utils.queryRefund(outRefundNo);

            if (result != null && (result.isSuccess() || result.isProcessing())) {
                log.info("查询微信支付V3退款状态成功，退款单号: {}, 状态: {}", outRefundNo, result.getStatus());
            } else {
                log.error("查询微信支付V3退款状态失败，退款单号: {}, 状态: {}",
                    outRefundNo, result != null ? result.getStatus() : "返回结果为空");
            }

            return result;
        } catch (Exception e) {
            log.error("查询微信支付V3退款状态异常，退款单号: " + outRefundNo, e);
            return null;
        }
    }

    /**
     * 根据微信交易号查询支付订单状态
     */
    @Override
    public WechatPayV3Result queryPayOrderByTransactionId(String transactionId)
    {
        try {
            log.info("开始根据微信交易号查询支付订单状态，微信交易号: {}", transactionId);

            WechatPayV3Result result = wechatPayV3Utils.queryOrderByTransactionId(transactionId);

            if (result != null && result.isSuccess()) {
                log.info("根据微信交易号查询支付订单状态成功，微信交易号: {}, 交易状态: {}",
                    transactionId, result.getTradeState());
            } else {
                log.error("根据微信交易号查询支付订单状态失败，微信交易号: {}, 错误信息: {}",
                    transactionId, result != null ? result.getTradeStateDesc() : "返回结果为空");
            }

            return result;
        } catch (Exception e) {
            log.error("根据微信交易号查询支付订单状态异常，微信交易号: " + transactionId, e);
            return null;
        }
    }
}
