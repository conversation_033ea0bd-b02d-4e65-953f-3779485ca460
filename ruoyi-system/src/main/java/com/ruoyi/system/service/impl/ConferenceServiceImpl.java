package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ConferenceMapper;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.service.IConferenceService;

/**
 * 会议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class ConferenceServiceImpl implements IConferenceService 
{
    @Autowired
    private ConferenceMapper conferenceMapper;

    /**
     * 查询会议
     * 
     * @param id 会议主键
     * @return 会议
     */
    @Override
    public Conference selectConferenceById(Long id)
    {
        return conferenceMapper.selectConferenceById(id);
    }

    /**
     * 查询会议列表
     * 
     * @param conference 会议
     * @return 会议
     */
    @Override
    public List<Conference> selectConferenceList(Conference conference)
    {
        return conferenceMapper.selectConferenceList(conference);
    }

    /**
     * 新增会议
     * 
     * @param conference 会议
     * @return 结果
     */
    @Override
    public int insertConference(Conference conference)
    {
        conference.setCreateTime(DateUtils.getNowDate());
        return conferenceMapper.insertConference(conference);
    }

    /**
     * 修改会议
     * 
     * @param conference 会议
     * @return 结果
     */
    @Override
    public int updateConference(Conference conference)
    {
        conference.setUpdateTime(DateUtils.getNowDate());
        return conferenceMapper.updateConference(conference);
    }

    /**
     * 批量删除会议
     * 
     * @param ids 需要删除的会议主键
     * @return 结果
     */
    @Override
    public int deleteConferenceByIds(Long[] ids)
    {
        return conferenceMapper.deleteConferenceByIds(ids);
    }

    /**
     * 删除会议信息
     * 
     * @param id 会议主键
     * @return 结果
     */
    @Override
    public int deleteConferenceById(Long id)
    {
        return conferenceMapper.deleteConferenceById(id);
    }

    /**
     * 查询启用的会议列表（供小程序使用）
     * 
     * @return 会议集合
     */
    @Override
    public List<Conference> selectEnabledConferenceList()
    {
        return conferenceMapper.selectEnabledConferenceList();
    }
}
