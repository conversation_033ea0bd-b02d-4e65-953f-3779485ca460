package com.ruoyi.quartz.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.ruoyi.common.utils.wechat.WechatCacheWrapper;
import com.ruoyi.common.utils.wechat.WechatSessionManager;

/**
 * 微信缓存清理定时任务
 * 
 * <AUTHOR>
 */
@Component("wechatCacheCleanTask")
public class WechatCacheCleanTask
{
    private static final Logger log = LoggerFactory.getLogger(WechatCacheCleanTask.class);

    /**
     * 清理过期的微信缓存
     */
    public void cleanExpiredCache()
    {
        log.info("开始执行微信缓存清理任务");
        try
        {
            // 清理access_token缓存
            WechatCacheWrapper.cleanExpiredCache();

            // 清理过期的sessionKey
            WechatSessionManager.cleanExpiredSessions();

            log.info("微信缓存清理任务执行完成");
        }
        catch (Exception e)
        {
            log.error("微信缓存清理任务执行异常", e);
        }
    }

    /**
     * 清理过期的微信缓存（带参数）
     * 
     * @param params 参数
     */
    public void cleanExpiredCache(String params)
    {
        cleanExpiredCache();
    }
}
