# 若依管理系统 - 会议酒店预定平台

## 项目简介

本项目基于若依(RuoYi)快速开发平台构建，专门为会议酒店预定业务场景设计的企业级管理系统。系统集成了完整的后台管理功能和会议酒店预定业务模块，为会议组织方和参会人员提供便捷的酒店预订服务。

### 核心特性

- 🏢 **企业级架构**：基于Spring Boot + MyBatis + Shiro的稳定架构
- 🎯 **业务专业化**：专门针对会议酒店预定场景优化
- 🔐 **安全可靠**：完善的权限管理和安全防护机制
- 📱 **移动友好**：支持响应式设计，适配移动端访问
- 🛠️ **开发高效**：内置代码生成器，快速生成CRUD代码
- 📊 **监控完善**：系统监控、缓存监控、SQL监控等

## 技术栈

### 后端技术
- **核心框架**：Spring Boot 2.5.15
- **安全框架**：Apache Shiro 1.13.0
- **持久层框架**：MyBatis
- **数据库连接池**：Druid 1.2.23
- **模板引擎**：Thymeleaf
- **定时任务**：Quartz
- **JSON处理**：Fastjson 1.2.83
- **API文档**：Swagger 3.0.0
- **分页插件**：PageHelper 1.4.7
- **系统监控**：OSHI 6.8.2
- **Excel处理**：Apache POI 4.1.2
- **代码生成**：Velocity 2.3

### 前端技术
- **UI框架**：Bootstrap + jQuery
- **图表库**：ECharts
- **表格组件**：Bootstrap Table
- **树形组件**：zTree
- **表单验证**：jQuery Validate
- **文件上传**：jQuery File Upload
- **日期选择**：Bootstrap DatePicker

### 数据库
- **主数据库**：MySQL 8.0+
- **缓存**：EhCache

### 开发工具
- **构建工具**：Maven 3.6+
- **JDK版本**：JDK 1.8+
- **开发IDE**：IntelliJ IDEA / Eclipse

## 项目结构

```
ruoyi/
├── ruoyi-admin/          # 管理后台模块（Web服务入口）
├── ruoyi-common/         # 通用工具模块
├── ruoyi-framework/      # 框架核心模块
├── ruoyi-generator/      # 代码生成模块
├── ruoyi-quartz/         # 定时任务模块
├── ruoyi-system/         # 系统管理模块
├── sql/                  # 数据库脚本
├── bin/                  # 启动脚本
├── doc/                  # 文档目录
└── logs/                 # 日志目录
```

## 系统功能

### 基础管理功能
1. **用户管理**：系统用户配置，支持用户增删改查、角色分配
2. **部门管理**：组织机构管理，树形结构展现，支持数据权限
3. **岗位管理**：用户岗位配置管理
4. **菜单管理**：系统菜单配置，操作权限，按钮权限标识
5. **角色管理**：角色权限分配，数据范围权限划分
6. **字典管理**：系统数据字典维护
7. **参数管理**：系统参数动态配置
8. **通知公告**：系统公告信息发布维护
9. **操作日志**：系统操作日志记录和查询
10. **登录日志**：用户登录日志记录，包含异常登录
11. **在线用户**：当前系统活跃用户监控
12. **定时任务**：在线任务调度管理，支持执行结果日志
13. **代码生成**：前后端代码自动生成，支持CRUD下载
14. **系统接口**：Swagger API接口文档自动生成
15. **服务监控**：系统CPU、内存、磁盘、堆栈信息监控
16. **缓存监控**：系统缓存查询、删除、清空操作
17. **连接池监控**：数据库连接池状态监控，SQL性能分析

### 会议酒店预定功能
1. **会议管理**：会议信息管理，包括时间、地点、参会人数等
2. **房型管理**：酒店房型信息管理，房间图片、价格配置
3. **识别码管理**：会议识别码生成和管理
4. **预定管理**：房间预定流程管理和订单处理
5. **库存管理**：房间库存实时管理和控制

## 模块详细说明

### ruoyi-admin 模块
**功能**：Web服务入口模块，提供系统的Web访问接口
- 系统启动类：`RuoYiApplication.java`
- 控制器层：处理HTTP请求和响应
- 配置文件：`application.yml`、`application-druid.yml`
- 静态资源：CSS、JS、图片等前端资源
- 模板文件：Thymeleaf模板文件

**主要特性**：
- 集成Swagger API文档
- 支持热部署开发
- 内置演示控制器
- 系统监控页面

### ruoyi-framework 模块
**功能**：框架核心模块，提供系统基础功能和配置
- Shiro安全配置：权限认证、会话管理
- 数据源配置：多数据源支持、连接池配置
- 缓存配置：EhCache缓存管理
- 异常处理：全局异常处理机制
- 拦截器配置：请求拦截和处理

**核心组件**：
- `ShiroConfig`：安全框架配置
- `DruidConfig`：数据源配置
- `DynamicDataSource`：动态数据源切换
- 各种过滤器和拦截器

### ruoyi-system 模块
**功能**：系统管理核心业务模块
- 用户管理：用户CRUD、角色分配、权限控制
- 角色管理：角色权限分配、数据权限控制
- 菜单管理：系统菜单树形管理
- 部门管理：组织架构管理
- 字典管理：系统字典数据维护
- 参数管理：系统参数配置
- 日志管理：操作日志、登录日志

**设计模式**：
- Service层：业务逻辑处理
- Mapper层：数据访问层
- Domain层：实体对象定义

### ruoyi-common 模块
**功能**：通用工具和组件模块
- 工具类：字符串、日期、文件处理等
- 常量定义：系统常量、错误码定义
- 异常处理：自定义异常类
- 注解定义：Excel导出、日志记录等
- 核心类：分页、响应结果封装

**主要工具**：
- `StringUtils`：字符串处理工具
- `ExcelUtil`：Excel导入导出工具
- `FileUtils`：文件操作工具
- `DateUtils`：日期处理工具

### ruoyi-quartz 模块
**功能**：定时任务调度模块
- 任务管理：定时任务的增删改查
- 任务调度：基于Quartz的任务调度
- 执行日志：任务执行结果记录
- 任务监控：任务状态监控

**核心功能**：
- 支持Cron表达式
- 任务并发控制
- 任务执行日志
- 动态任务管理

### ruoyi-generator 模块
**功能**：代码生成器模块
- 表结构导入：从数据库导入表结构
- 代码生成：自动生成CRUD代码
- 模板管理：Velocity模板管理
- 预览功能：代码生成预览

**生成内容**：
- Java实体类
- Mapper接口和XML
- Service接口和实现
- Controller控制器
- HTML页面模板

## 环境要求

- **JDK**：1.8 或以上版本
- **Maven**：3.6.0 或以上版本
- **MySQL**：8.0 或以上版本
- **Redis**：可选，用于缓存
- **操作系统**：Windows / Linux / macOS

## 快速开始

### 1. 克隆项目
```bash
git clone [项目地址]
cd RuoYi
```

### 2. 数据库配置
```sql
-- 创建数据库
CREATE DATABASE ry DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 导入数据库脚本
mysql -u root -p ry < sql/ry_20250416.sql
mysql -u root -p ry < sql/quartz.sql
mysql -u root -p ry < sql/booking.sql
```

### 3. 修改配置
编辑 `ruoyi-admin/src/main/resources/application-druid.yml`：
```yaml
spring:
  datasource:
    druid:
      master:
        url: *******************************************************************************************************************************************
        username: root
        password: 你的密码
```

### 4. 编译运行
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run -pl ruoyi-admin

# 或者运行jar包
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

### 5. 访问系统
- 访问地址：http://localhost
- 默认账号：admin/admin123

## 数据库设计

### 系统核心表
- `sys_user`：用户信息表
- `sys_role`：角色信息表
- `sys_menu`：菜单权限表
- `sys_dept`：部门信息表
- `sys_config`：参数配置表
- `sys_dict_type`：字典类型表
- `sys_dict_data`：字典数据表

### 会议预定业务表
- `conference`：会议信息表
  - 会议名称、时间、地点
  - 预定开放时间、参会人数
  - 酒店信息、预定日期范围
- `room`：房型信息表
  - 房间名称、图片URL
  - 关联会议ID
- `category_code`：识别码表
  - 会议识别码、房间配置
  - 房间数量限制

## 开发指南

### 代码生成使用
1. 访问系统管理 -> 代码生成
2. 导入数据表或创建表结构
3. 配置生成信息（包名、作者、模块名等）
4. 预览代码或下载代码包
5. 将生成的代码集成到项目中

### 权限控制
系统使用Shiro进行权限控制：
- 使用`@RequiresPermissions`注解控制方法权限
- 在页面使用`shiro:hasPermission`控制按钮显示
- 支持角色权限和数据权限控制

### 日志记录
使用`@Log`注解记录操作日志：
```java
@Log(title = "用户管理", businessType = BusinessType.INSERT)
@PostMapping("/add")
public AjaxResult add(@Validated SysUser user) {
    // 业务逻辑
}
```

### 数据权限
系统支持多种数据权限控制：
- 全部数据权限
- 自定数据权限
- 部门数据权限
- 部门及以下数据权限
- 仅本人数据权限

## 部署说明

### 开发环境部署
1. 使用IDE直接运行`RuoYiApplication`
2. 访问 http://localhost 进行开发调试

### 生产环境部署
1. 修改生产环境配置文件
2. 执行打包命令：`mvn clean package`
3. 上传jar包到服务器
4. 使用启动脚本：`./bin/run.sh`

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY ruoyi-admin.jar app.jar
EXPOSE 80
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 性能优化建议
- 配置合适的JVM参数
- 使用Redis作为缓存
- 配置数据库连接池参数
- 启用Gzip压缩
- 配置静态资源缓存

## 常见问题

### Q: 如何修改默认端口？
A: 在`application.yml`中修改`server.port`配置

### Q: 如何配置多数据源？
A: 在`application-druid.yml`中配置slave数据源并启用

### Q: 如何自定义登录页面？
A: 修改`templates/login.html`模板文件

### Q: 如何添加新的菜单？
A: 在系统管理->菜单管理中添加菜单项

## 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

## 技术支持

- 官方网站：http://ruoyi.vip
- 开发文档：http://doc.ruoyi.vip
- 问题反馈：通过Issues提交问题和建议
- QQ群：交流群号请查看官网

## 贡献指南

欢迎提交Pull Request或Issue来完善项目：

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交Pull Request

## 更新日志

### v4.8.1 (2025-01-17)
- 新增会议酒店预定功能模块
- 优化系统安全配置
- 修复已知问题
- 更新依赖版本

---

**注意**：本系统基于若依开源框架开发，在使用过程中请遵循相关开源协议。