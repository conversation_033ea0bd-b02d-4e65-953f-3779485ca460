# 会议酒店预定微信小程序

## 项目简介

这是一个专门用于会议酒店预定的微信小程序，为参会人员提供便捷的酒店预订服务。

## 功能特性

### 核心功能
- 🏨 会议列表展示和筛选
- 🔐 会议识别码验证
- 🛏️ 酒店房型选择和预订
- 💰 在线支付定金
- 👤 用户登录和个人中心
- 📋 预订记录管理

### 技术特点
- 📱 微信小程序原生开发
- 🎨 现代化UI设计，完全自适应
- 📊 完整的数据流管理
- 🔧 工具函数封装
- 💾 本地数据存储
- 📐 响应式设计，支持多种屏幕尺寸
- 🎯 移动优先的设计理念

## 项目结构

```
galleno_miniprogram/
├── app.js                 # 小程序入口文件
├── app.json              # 全局配置
├── app.wxss              # 全局样式
├── pages/                # 页面目录
│   ├── conference-list/  # 会议列表页
│   ├── conference-code/  # 识别码输入页
│   ├── hotel-booking/    # 酒店预订页
│   ├── booking/          # 预订信息页
│   ├── payment-success/  # 支付成功页
│   └── profile/          # 个人中心页
├── utils/                # 工具函数
│   └── util.js          # 通用工具函数
└── images/              # 图片资源
```

## 页面流程

1. **会议列表** → 选择会议
2. **识别码输入** → 验证身份
3. **酒店预订** → 选择房型和日期
4. **预订信息** → 填写个人信息
5. **支付成功** → 完成预订

## 开发环境

- 微信开发者工具
- Node.js (可选，用于构建工具)

## 快速开始

1. 下载并安装微信开发者工具
2. 导入项目到开发者工具
3. 配置AppID（测试可使用测试号）
4. 点击编译运行

## 主要页面说明

### 会议列表页 (conference-list)
- 展示所有可用会议
- 支持状态筛选（全部、可预订、进行中、已结束）
- 会议收藏功能
- 点击跳转到识别码输入页

### 识别码输入页 (conference-code)
- 显示选中会议信息
- 识别码输入和验证
- 错误提示和尝试次数限制
- 验证成功后跳转到酒店预订页

### 酒店预订页 (hotel-booking)
- 房型列表展示
- 房型选择功能
- 日期选择（入住/退房）
- 实时价格计算
- 预订按钮状态管理

### 预订信息页 (booking)
- 预订信息摘要
- 入住人信息填写
- 表单验证
- 支付流程

### 支付成功页 (payment-success)
- 支付结果展示
- 订单详情显示
- 重要提醒信息
- 后续操作引导

### 个人中心页 (profile)
- 用户登录/登出
- 个人信息展示
- 预订统计
- 功能菜单

## Mock数据

项目包含完整的mock数据，包括：
- 会议信息
- 房型数据
- 订单历史
- 用户信息

## 测试账号

### 会议识别码
- tech2024: TECH2024
- ai2024: AI2024
- blockchain2024: BLOCK2024

## 自适应特性

### 📱 支持设备
- iPhone SE (375px)
- iPhone 6/7/8 (375px)
- iPhone 6/7/8 Plus (414px)
- iPhone X/11/12 (375px)
- iPhone 12 Pro Max (428px)
- 各种Android设备

### 🎨 响应式特性
- **小屏优化**: 减少间距，简化文本，优化布局
- **大屏增强**: 增加内容区域，更大字体，丰富视觉
- **横屏适配**: 优化横屏显示效果
- **触摸友好**: 合适的触摸目标大小
- **性能优化**: 使用rpx单位，减少重排

### 🛠️ 技术实现
- 全局响应式工具类
- rpx单位自动适配
- JavaScript响应式工具函数
- 系统信息自动获取
- 灵活的布局系统

## 注意事项

1. 本项目为演示版本，包含模拟数据
2. 支付功能为模拟实现，实际使用需要配置真实支付
3. 图片资源使用外部链接，建议替换为本地资源
4. 需要配置真实的后端API接口
5. 已完全适配各种屏幕尺寸，可在不同设备上正常使用

## 开发规范

- 使用rpx单位进行响应式设计
- 遵循微信小程序开发规范
- 代码注释清晰完整
- 统一的错误处理机制

## 后续开发

- [ ] 集成真实后端API
- [ ] 完善支付功能
- [ ] 添加更多交互动画
- [ ] 优化性能和用户体验
- [ ] 添加单元测试

## 联系方式

如有问题请联系开发团队。
