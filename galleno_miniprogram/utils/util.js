// 格式化时间
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

// 格式化数字
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 格式化日期为 YYYY-MM-DD
const formatDate = date => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 格式化日期为中文显示
const formatDateCN = dateString => {
  const date = new Date(dateString)
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${month}月${day}日`
}

// 计算两个日期之间的天数
const calculateDays = (startDate, endDate) => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const timeDiff = end.getTime() - start.getTime()
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
}

// 验证手机号
const validatePhone = phone => {
  return /^1[3-9]\d{9}$/.test(phone)
}

// 验证身份证号
const validateIdCard = idCard => {
  return /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(idCard)
}

// 生成订单号
const generateOrderId = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  return `HT${year}${month}${day}${random}`
}

// 显示消息提示
const showMessage = (message, type = 'none') => {
  let icon = 'none'
  if (type === 'success') icon = 'success'
  if (type === 'error') icon = 'error'

  wx.showToast({
    title: message,
    icon: icon,
    duration: 2000
  })
}

// 显示加载中
const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title: title,
    mask: true
  })
}

// 隐藏加载中
const hideLoading = () => {
  wx.hideLoading()
}

// 获取状态文本
const getStatusText = status => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'checkedin': '已入住',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取房型图片 (已废弃 - 房型列表不再显示图片)
// const getRoomImage = roomType => {
//   const imageMap = {
//     'deluxe': 'https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=200&h=160&fit=crop',
//     'business': 'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=200&h=160&fit=crop',
//     'suite': 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=200&h=160&fit=crop',
//     'standard': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=160&fit=crop'
//   }
//   return imageMap[roomType] || imageMap['standard']
// }

// 响应式工具函数
const getSystemInfo = () => {
  try {
    const app = getApp()
    return app.globalData.systemInfo || {}
  } catch (e) {
    return {}
  }
}

// 获取屏幕宽度
const getScreenWidth = () => {
  try {
    const app = getApp()
    return app.globalData.screenWidth || 375
  } catch (e) {
    return 375
  }
}

// 获取窗口高度
const getWindowHeight = () => {
  try {
    const app = getApp()
    return app.globalData.windowHeight || 667
  } catch (e) {
    return 667
  }
}

// 判断是否为小屏设备
const isSmallScreen = () => {
  try {
    const screenWidth = getScreenWidth()
    return screenWidth < 375
  } catch (e) {
    return false
  }
}

// 判断是否为大屏设备
const isLargeScreen = () => {
  try {
    const screenWidth = getScreenWidth()
    return screenWidth > 414
  } catch (e) {
    return false
  }
}

module.exports = {
  formatTime,
  formatNumber,
  formatDate,
  formatDateCN,
  calculateDays,
  validatePhone,
  validateIdCard,
  generateOrderId,
  showMessage,
  showLoading,
  hideLoading,
  getStatusText,
  // getRoomImage, // 已废弃 - 房型列表不再显示图片
  getSystemInfo,
  getScreenWidth,
  getWindowHeight,
  isSmallScreen,
  isLargeScreen
}
