// auth.js - 认证相关工具
const api = require('./api.js')
const util = require('./util.js')

/**
 * 认证管理类
 */
class AuthManager {
  constructor() {
    this.isLogging = false
  }

  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    return !!(token && userInfo && userInfo.nickName)
  }

  /**
   * 获取当前用户信息
   * @returns {Object} 用户信息
   */
  getCurrentUser() {
    return wx.getStorageSync('userInfo') || {}
  }

  /**
   * 获取token
   * @returns {string} token
   */
  getToken() {
    return wx.getStorageSync('token') || ''
  }

  /**
   * 小程序登录
   * @param {Object} options 登录选项
   * @param {Function} options.success 成功回调
   * @param {Function} options.fail 失败回调
   * @param {boolean} options.showLoading 是否显示加载中
   */
  login(options = {}) {
    if (this.isLogging) {
      console.log('正在登录中，请勿重复操作')
      return
    }

    this.isLogging = true

    if (options.showLoading !== false) {
      util.showLoading('登录中...')
    }

    // 第一步：获取微信登录凭证
    wx.login({
      success: (loginRes) => {
        console.log('wx.login成功:', loginRes)
        
        if (loginRes.code) {
          // 第二步：发送code到后端
          this._performLogin(loginRes.code, options)
        } else {
          this._handleLoginError('获取登录凭证失败', options)
        }
      },
      fail: (err) => {
        console.error('wx.login失败:', err)
        this._handleLoginError('微信登录失败', options)
      }
    })
  }

  /**
   * 执行登录请求
   * @private
   */
  _performLogin(jsCode, options) {
    api.API.user.login(jsCode)
      .then(result => {
        console.log('登录成功:', result)
        
        // 保存token
        if (result.token) {
          wx.setStorageSync('token', result.token)
        }

        // 保存用户信息
        if (result.user) {
          const userInfo = {
            ...result.user,
            memberLevel: result.user.memberLevel || '普通会员',
            isNewUser: result.isNewUser || false
          }
          
          wx.setStorageSync('userInfo', userInfo)
          
          this.isLogging = false
          
          if (options.showLoading !== false) {
            util.hideLoading()
          }

          // 显示欢迎信息
          if (options.showWelcome !== false) {
            const welcomeMsg = result.isNewUser ? '欢迎新用户！' : '欢迎回来！'
            util.showMessage(welcomeMsg, 'success')
          }

          // 成功回调
          if (typeof options.success === 'function') {
            options.success(result)
          }
        } else {
          this._handleLoginError('登录返回数据异常', options)
        }
      })
      .catch(err => {
        console.error('登录失败:', err)
        this._handleLoginError(err.message || '登录失败', options)
      })
  }

  /**
   * 处理登录错误
   * @private
   */
  _handleLoginError(message, options) {
    this.isLogging = false
    
    if (options.showLoading !== false) {
      util.hideLoading()
    }

    if (options.showError !== false) {
      util.showMessage(message, 'none')
    }

    // 失败回调
    if (typeof options.fail === 'function') {
      options.fail(new Error(message))
    }
  }

  /**
   * 退出登录
   * @param {Object} options 选项
   * @param {Function} options.success 成功回调
   * @param {boolean} options.showConfirm 是否显示确认对话框
   */
  logout(options = {}) {
    const performLogout = () => {
      // 清除所有登录相关数据
      wx.removeStorageSync('token')
      wx.removeStorageSync('userInfo')
      
      if (options.showMessage !== false) {
        util.showMessage('已退出登录', 'success')
      }

      // 成功回调
      if (typeof options.success === 'function') {
        options.success()
      }
    }

    if (options.showConfirm !== false) {
      wx.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            performLogout()
          }
        }
      })
    } else {
      performLogout()
    }
  }

  /**
   * 检查token有效性
   * @returns {Promise} 检查结果
   */
  checkTokenValid() {
    return new Promise((resolve, reject) => {
      if (!this.isLoggedIn()) {
        reject(new Error('未登录'))
        return
      }

      api.API.user.getUserInfo()
        .then(userInfo => {
          // 更新用户信息
          wx.setStorageSync('userInfo', userInfo)
          resolve(userInfo)
        })
        .catch(err => {
          // token无效，清除登录状态
          this.logout({ showConfirm: false, showMessage: false })
          reject(err)
        })
    })
  }

  /**
   * 自动登录（静默登录）
   * @returns {Promise} 登录结果
   */
  autoLogin() {
    return new Promise((resolve, reject) => {
      if (this.isLoggedIn()) {
        // 已登录，检查token有效性
        this.checkTokenValid()
          .then(resolve)
          .catch(reject)
      } else {
        // 未登录，尝试静默登录
        this.login({
          showLoading: false,
          showWelcome: false,
          showError: false,
          success: resolve,
          fail: reject
        })
      }
    })
  }

  /**
   * 确保用户已登录（如果未登录则引导登录）
   * @param {Object} options 选项
   * @returns {Promise} 登录结果
   */
  ensureLogin(options = {}) {
    return new Promise((resolve, reject) => {
      if (this.isLoggedIn()) {
        resolve(this.getCurrentUser())
      } else {
        if (options.showPrompt !== false) {
          wx.showModal({
            title: '需要登录',
            content: '请先登录后再进行此操作',
            showCancel: true,
            cancelText: '取消',
            confirmText: '去登录',
            success: (res) => {
              if (res.confirm) {
                this.login({
                  success: resolve,
                  fail: reject
                })
              } else {
                reject(new Error('用户取消登录'))
              }
            }
          })
        } else {
          reject(new Error('未登录'))
        }
      }
    })
  }
}

// 创建单例
const authManager = new AuthManager()

module.exports = {
  AuthManager,
  authManager,
  // 便捷方法
  isLoggedIn: () => authManager.isLoggedIn(),
  getCurrentUser: () => authManager.getCurrentUser(),
  getToken: () => authManager.getToken(),
  login: (options) => authManager.login(options),
  logout: (options) => authManager.logout(options),
  checkTokenValid: () => authManager.checkTokenValid(),
  autoLogin: () => authManager.autoLogin(),
  ensureLogin: (options) => authManager.ensureLogin(options)
}
