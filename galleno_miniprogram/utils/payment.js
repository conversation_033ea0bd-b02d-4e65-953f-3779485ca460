// payment.js - 微信支付工具类
const { get, post } = require('./api.js')

/**
 * 微信支付V3版本工具类
 */
class PaymentUtils {

    /**
     * 创建酒店订单并调起支付
     * @param {Object} orderData 完整的订单数据
     * @param {string} orderData.conferenceId 会议ID
     * @param {string} orderData.roomId 房间ID
     * @param {string} orderData.roomType 房间类型
     * @param {string} orderData.roomName 房间名称
     * @param {string} orderData.checkinDate 入住日期
     * @param {string} orderData.checkoutDate 退房日期
     * @param {number} orderData.nights 住宿天数
     * @param {number} orderData.roomPrice 房间单价
     * @param {number} orderData.totalAmount 订单总金额
     * @param {number} orderData.depositAmount 押金金额
     * @param {string} orderData.guestName 入住人姓名
     * @param {string} orderData.guestPhone 入住人电话
     * @param {string} orderData.guestIdCard 入住人身份证号（可选）
     * @param {string} orderData.specialRequirements 特殊要求（可选）
     * @param {string} orderData.remark 备注（可选）
     * @returns {Promise}
     */
    static createPayment(orderData) {
        return new Promise((resolve, reject) => {
            wx.showLoading({
                title: '正在创建订单...'
            })

            // 调用后端接口创建酒店订单并发起支付
            post('/hotel/order/create', orderData)
                .then(res => {
                    wx.hideLoading()
                    const payParams = res
                    console.log('创建酒店订单成功', payParams)

                    // 更新本地存储中的currentOrder对象
                    if (payParams.orderId && payParams.orderNo) {
                        try {
                            // 获取当前的订单对象
                            let currentOrder = wx.getStorageSync('currentOrder') || {}

                            // 更新订单ID和订单号
                            currentOrder.orderId = payParams.orderId
                            currentOrder.orderNo = payParams.orderNo

                            // 保存回本地存储
                            wx.setStorageSync('currentOrder', currentOrder)
                            console.log('本地存储currentOrder已更新', {
                                orderId: payParams.orderId,
                                orderNo: payParams.orderNo
                            })
                        } catch (error) {
                            console.error('更新本地存储currentOrder失败', error)
                        }
                    }

                    // 调起微信支付
                    PaymentUtils.requestPayment(payParams)
                        .then((payResult) => {
                            // 支付成功，返回包含订单信息的结果
                            resolve({
                                ...payResult,
                                orderNo: payParams.orderNo,
                                orderId: payParams.orderId
                            })
                        })
                        .catch(reject)
                })
                .catch(err => {
                    wx.hideLoading()
                    console.error('创建酒店订单异常', err)
                    reject(err)
                })
        })
    }

    /**
     * 调起微信支付
     * @param {Object} payParams 支付参数
     * @returns {Promise}
     */
    static requestPayment(payParams) {
        return new Promise((resolve, reject) => {
            // 调试：打印支付参数
            console.log('准备调起微信支付，参数：', payParams)
            console.log('支付参数详情：', {
                timeStamp: payParams.timeStamp,
                nonceStr: payParams.nonceStr,
                package: payParams.packageValue,
                signType: payParams.signType,
                paySign: payParams.paySign
            })

            // 验证必要参数
            if (!payParams.timeStamp || !payParams.nonceStr || !payParams.packageValue || !payParams.signType || !payParams.paySign) {
                console.error('支付参数不完整：', payParams)
                reject(new Error('支付参数不完整'))
                return
            }

            wx.showLoading({
                title: '正在调起支付...'
            })

            wx.requestPayment({
                timeStamp: payParams.timeStamp,
                nonceStr: payParams.nonceStr,
                package: payParams.packageValue,
                signType: payParams.signType,
                paySign: payParams.paySign,
                success: (res) => {
                    wx.hideLoading()
                    console.log('支付成功', res)
                    console.log('使用微信支付V3版本（RSA签名）')

                    wx.showToast({
                        title: '支付成功',
                        icon: 'success'
                    })

                    resolve(res)
                },
                fail: (err) => {
                    wx.hideLoading()
                    console.error('支付失败详细信息：', err)
                    console.error('错误消息：', err.errMsg)
                    console.error('错误代码：', err.errCode)

                    if (err.errMsg === 'requestPayment:fail cancel') {
                        // 用户取消支付，不显示toast，直接返回取消错误
                        reject(new Error('用户取消支付'))
                    } else {
                        // 分析具体错误原因
                        let errorContent = '支付过程中出现问题，请重试或联系客服'
                        if (err.errMsg) {
                            if (err.errMsg.includes('param')) {
                                errorContent = '支付参数错误，请重新下单'
                            } else if (err.errMsg.includes('network')) {
                                errorContent = '网络连接异常，请检查网络后重试'
                            } else if (err.errMsg.includes('system')) {
                                errorContent = '系统繁忙，请稍后重试'
                            }
                        }

                        wx.showModal({
                            title: '支付失败',
                            content: errorContent,
                            showCancel: true,
                            cancelText: '重试',
                            confirmText: '联系客服',
                            success: (modalRes) => {
                                if (modalRes.confirm) {
                                    // 联系客服
                                    wx.makePhoneCall({
                                        phoneNumber: '4008889999',
                                        fail: () => {
                                            wx.showToast({
                                                title: '拨打失败',
                                                icon: 'none'
                                            })
                                        }
                                    })
                                }
                            }
                        })
                        reject(err)
                    }
                }
            })
        })
    }

    /**
     * 使用已有prepay_id重新调起支付
     * @param {string} orderNo 订单号
     * @returns {Promise}
     */
    static retryPaymentWithPrepayId(orderNo) {
        return new Promise((resolve, reject) => {
            wx.showLoading({
                title: '正在获取支付信息...'
            })

            // 调用后端接口获取已有的支付参数
            get('/hotel/order/payment-params', { orderNo })
                .then(res => {
                    wx.hideLoading()
                    console.log('获取支付参数成功', res)

                    if (res && res.timeStamp && res.nonceStr && res.packageValue && res.signType && res.paySign) {
                        // 直接调起微信支付
                        PaymentUtils.requestPayment(res)
                            .then(resolve)
                            .catch(reject)
                    } else {
                        // 支付参数无效，需要重新创建订单
                        reject(new Error('PREPAY_ID_INVALID'))
                    }
                })
                .catch(err => {
                    wx.hideLoading()
                    console.error('获取支付参数异常', err)
                    // 如果是404或者prepay_id过期，返回特殊错误码
                    if (err.message && (err.message.includes('404') || err.message.includes('过期'))) {
                        reject(new Error('PREPAY_ID_INVALID'))
                    } else {
                        reject(err)
                    }
                })
        })
    }

    /**
     * 查询并刷新支付订单状态
     * @param {string} orderNo 订单号
     * @returns {Promise}
     */
    static queryAndRefreshPaymentStatus(orderNo) {
        return new Promise((resolve, reject) => {
            console.log('开始查询并刷新支付状态，订单号:', orderNo)

            get('/hotel/payment/query', { orderNo })
                .then(result => {
                    console.log('查询并刷新支付状态成功', result)
                    resolve(result)
                })
                .catch(err => {
                    console.error('查询并刷新支付状态异常', err)
                    reject(err)
                })
        })
    }

    /**
     * 查询支付订单状态（仅返回微信支付状态）
     * @param {string} orderNo 订单号
     * @returns {Promise}
     */
    static queryPaymentStatus(orderNo) {
        return new Promise((resolve, reject) => {
            PaymentUtils.queryAndRefreshPaymentStatus(orderNo)
                .then(result => {
                    console.log('查询支付状态成功', result)
                    // 返回微信支付状态
                    resolve(result.paymentResult)
                })
                .catch(err => {
                    console.error('查询支付状态异常', err)
                    reject(err)
                })
        })
    }

    /**
     * 轮询查询支付状态
     * @param {string} outTradeNo 商户订单号
     * @param {number} maxRetries 最大重试次数，默认10次
     * @param {number} interval 查询间隔（毫秒），默认2秒
     * @returns {Promise}
     */
    static pollPaymentStatus(outTradeNo, maxRetries = 10, interval = 2000) {
        return new Promise((resolve, reject) => {
            let retryCount = 0

            const poll = () => {
                PaymentUtils.queryPaymentStatus(outTradeNo)
                    .then(result => {
                        if (result.tradeState === 'SUCCESS') {
                            // 支付成功
                            resolve(result)
                        } else if (result.tradeState === 'CLOSED' || result.tradeState === 'REVOKED') {
                            // 订单已关闭或已撤销
                            reject(new Error('订单已关闭'))
                        } else if (retryCount >= maxRetries) {
                            // 达到最大重试次数
                            reject(new Error('查询超时'))
                        } else {
                            // 继续轮询
                            retryCount++
                            setTimeout(poll, interval)
                        }
                    })
                    .catch(err => {
                        if (retryCount >= maxRetries) {
                            reject(err)
                        } else {
                            retryCount++
                            setTimeout(poll, interval)
                        }
                    })
            }

            poll()
        })
    }

    /**
     * 查询并刷新服务端订单状态
     * 小程序支付成功后调用，主动通知服务端刷新订单状态
     * @param {string} orderNo 订单号
     * @returns {Promise}
     */
    static queryAndRefreshOrderStatus(orderNo) {
        return PaymentUtils.queryAndRefreshPaymentStatus(orderNo)
    }

    /**
     * 验证支付状态并刷新订单
     * 使用简化后的接口，一次调用完成查询和刷新
     * @param {string} orderNo 订单号
     * @param {number} maxRetries 最大重试次数，默认3次
     * @returns {Promise}
     */
    static verifyPaymentAndRefreshOrder(orderNo, maxRetries = 3) {
        return new Promise((resolve, reject) => {
            console.log('开始验证支付状态并刷新订单，订单号:', orderNo)

            let retryCount = 0

            const verify = () => {
                // 使用简化后的接口，一次调用完成查询和刷新
                PaymentUtils.queryAndRefreshOrderStatus(orderNo)
                    .then(result => {
                        console.log('查询并刷新结果:', result)

                        const payResult = result.paymentResult
                        const orderInfo = result.orderInfo

                        if (payResult.trade_state === 'SUCCESS') {
                            // 支付成功
                            console.log('支付验证和订单刷新完成', orderInfo)
                            resolve(orderInfo)
                        } else if (payResult.trade_state === 'USERPAYING') {
                            // 用户支付中，等待一段时间后重试
                            if (retryCount < maxRetries) {
                                retryCount++
                                console.log(`用户支付中，${2}秒后重试，第${retryCount}次重试`)
                                setTimeout(verify, 2000)
                                return
                            } else {
                                reject(new Error('支付超时，请稍后查看订单状态'))
                                return
                            }
                        } else {
                            // 支付未成功
                            reject(new Error(`支付状态异常: ${payResult.trade_state}`))
                            return
                        }
                    })
                    .catch(err => {
                        console.error('验证支付状态并刷新订单失败', err)

                        // 如果是网络错误，可以重试
                        if (retryCount < maxRetries && (
                            err.message.includes('网络') ||
                            err.message.includes('timeout') ||
                            err.message.includes('请求失败')
                        )) {
                            retryCount++
                            console.log(`网络错误，${2}秒后重试，第${retryCount}次重试`)
                            setTimeout(verify, 2000)
                        } else {
                            reject(err)
                        }
                    })
            }

            verify()
        })
    }

    /**
     * 安全的支付成功处理
     * 在微信支付成功回调后，进行二次验证确保支付真实性
     * @param {string} orderNo 订单号
     * @param {Object} paymentResult 微信支付返回的结果
     * @returns {Promise}
     */
    static securePaymentSuccessHandler(orderNo, paymentResult) {
        return new Promise((resolve, reject) => {
            console.log('开始安全支付成功处理，订单号:', orderNo, '支付结果:', paymentResult)

            // 1. 验证微信支付返回的结果
            if (!paymentResult || paymentResult.errMsg !== 'requestPayment:ok') {
                reject(new Error('微信支付结果异常'))
                return
            }

            // 2. 查询微信支付服务器确认支付状态并刷新订单
            PaymentUtils.verifyPaymentAndRefreshOrder(orderNo)
                .then(updatedOrder => {
                    console.log('安全支付验证完成', updatedOrder)
                    resolve(updatedOrder)
                })
                .catch(err => {
                    console.error('安全支付验证失败', err)
                    // 即使验证失败，也不能简单拒绝，因为可能是网络问题
                    // 应该给用户提示，让用户知道可能存在延迟
                    reject(err)
                })
        })
    }

    /**
     * 生成订单号
     * @param {string} prefix 前缀，默认'PAY'
     * @returns {string}
     */
    static generateOrderNo(prefix = 'PAY') {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const hour = String(now.getHours()).padStart(2, '0')
        const minute = String(now.getMinutes()).padStart(2, '0')
        const second = String(now.getSeconds()).padStart(2, '0')
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')

        return `${prefix}${year}${month}${day}${hour}${minute}${second}${random}`
    }

    /**
     * 格式化金额（元转分）
     * @param {number} yuan 金额（元）
     * @returns {number} 金额（分）
     */
    static yuanToFen(yuan) {
        return Math.round(yuan * 100)
    }

    /**
     * 格式化金额（分转元）
     * @param {number} fen 金额（分）
     * @returns {number} 金额（元）
     */
    static fenToYuan(fen) {
        return fen / 100
    }
}

module.exports = PaymentUtils
