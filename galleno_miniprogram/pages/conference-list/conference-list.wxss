/* conference-list.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

/* 顶部横幅 */
.banner {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  width: calc(100% - 64rpx);
  box-sizing: border-box;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.banner-text {
  flex: 1;
}

.banner-title {
  display: block;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.banner-subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.banner-icon {
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
}

/* 筛选标签 */
.filter-section {
  padding: 0 32rpx 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.filter-scroll {
  white-space: nowrap;
  width: 100%;
}

.filter-list {
  display: flex;
  gap: 24rpx;
  min-width: max-content;
}

.filter-item {
  padding: 16rpx 32rpx;
  background: #f1f5f9;
  color: #64748b;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: #3b82f6;
  color: #ffffff;
}

/* 会议列表 */
.conference-list {
  flex: 1;
  padding: 0 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.conference-item {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.conference-item.bookable {
  border: 4rpx solid #10b981;
}

.conference-item.ended {
  opacity: 0.6;
}

/* 会议图片容器 */
.conference-image-container {
  position: relative;
  height: 320rpx;
}

.conference-image {
  width: 100%;
  height: 100%;
}

.conference-item.ended .conference-image {
  filter: grayscale(100%);
}

/* 状态标签 */
.status-tag {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-tag.bookable {
  background: #10b981;
  color: #ffffff;
}

.status-tag.ongoing {
  background: #3b82f6;
  color: #ffffff;
}

.status-tag.upcoming {
  background: #f59e0b;
  color: #ffffff;
}

.status-tag.ended {
  background: #6b7280;
  color: #ffffff;
}

/* 收藏按钮 */
.favorite-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.favorite-icon {
  font-size: 32rpx;
  color: #d1d5db;
  transition: all 0.3s ease;
}

.favorite-icon.active {
  color: #ef4444;
}

/* 会议信息 */
.conference-info {
  padding: 32rpx;
}

.conference-info.ended {
  opacity: 0.6;
}

.conference-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.conference-info.ended .conference-title {
  color: #6b7280;
}

.conference-detail {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  width: 32rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #6b7280;
  flex: 1;
}

.conference-info.ended .detail-text {
  color: #9ca3af;
}

/* 会议统计 */
.conference-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.stat-text {
  font-size: 28rpx;
  color: #3b82f6;
}

.stat-text.available {
  color: #f59e0b;
}

.stat-text.full {
  color: #ef4444;
}

.stat-text.pending {
  color: #f59e0b;
}

.conference-info.ended .stat-text {
  color: #9ca3af;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 120rpx 32rpx;
}

.loading-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 24rpx;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #6b7280;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #9ca3af;
}

/* 底部间距 */
.bottom-spacing {
  height: 40rpx;
}

/* 响应式适配 - 使用rpx单位自动适配 */
