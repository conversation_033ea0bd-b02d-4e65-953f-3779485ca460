<!--order-pending.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">
      <view class="clock-icon">⏰</view>
    </view>
    <view class="header-title">订单待支付</view>
    <view class="header-subtitle">请在30分钟内完成支付</view>
  </view>

  <!-- 订单信息卡片 -->
  <view class="order-card">
    <view class="order-header">
      <view class="order-title">{{orderData.conferenceTitle || '会议酒店预订'}}</view>
      <view class="order-status pending">待支付</view>
    </view>
    
    <view class="order-details">
      <!-- 房型信息 -->
      <view class="detail-row">
        <view class="detail-label">房型</view>
        <view class="detail-value">{{orderData.roomName || orderData.roomType}}</view>
      </view>
      
      <!-- 入住日期 -->
      <view class="detail-row">
        <view class="detail-label">入住</view>
        <view class="detail-value">{{orderData.checkinDate}} ({{orderData.nights}}晚)</view>
      </view>
      
      <!-- 退房日期 -->
      <view class="detail-row">
        <view class="detail-label">退房</view>
        <view class="detail-value">{{orderData.checkoutDate}}</view>
      </view>
      
      <!-- 入住人信息 -->
      <view class="detail-row" wx:if="{{orderData.guestInfo}}">
        <view class="detail-label">入住人</view>
        <view class="detail-value">{{orderData.guestInfo.name}}</view>
      </view>
      
      <!-- 联系电话 -->
      <view class="detail-row" wx:if="{{orderData.guestInfo}}">
        <view class="detail-label">联系电话</view>
        <view class="detail-value">{{orderData.guestInfo.phone}}</view>
      </view>
    </view>
    
    <!-- 订单号 -->
    <view class="order-number">
      <view class="order-number-label">订单号</view>
      <view class="order-number-value">{{orderData.orderNo}}</view>
    </view>
  </view>

  <!-- 金额信息 -->
  <view class="amount-card">
    <view class="amount-row">
      <view class="amount-label">预订{{orderData.nights || 1}}晚住宿</view>
      <view class="amount-note">到店支付房费</view>
    </view>
    <view class="amount-divider"></view>
    <view class="amount-row main">
      <view class="amount-label">需支付定金</view>
      <view class="amount-value">¥{{orderData.depositAmount || 200}}</view>
    </view>
  </view>

  <!-- 温馨提示 -->
  <view class="tips-card">
    <view class="tips-title">温馨提示</view>
    <view class="tips-content">
      <view class="tip-item">• 请在30分钟内完成支付，超时订单将自动取消</view>
      <view class="tip-item">• 定金支付成功后，房间将为您保留</view>
      <view class="tip-item">• 如需帮助，请联系客服：4008889999</view>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 底部操作栏 -->
<view class="action-bar">
  <button class="cancel-btn" bindtap="cancelOrder">取消订单</button>
    <button class="pay-btn {{loading ? 'loading' : ''}}"
            disabled="{{loading}}"
            bindtap="retryPayment">
      {{loading ? '支付中...' : '立即支付'}}
    </button>
</view>
