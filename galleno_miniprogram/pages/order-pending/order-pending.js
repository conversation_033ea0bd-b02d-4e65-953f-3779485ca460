// order-pending.js
const api = require('../../utils/api.js')
const PaymentUtils = require('../../utils/payment.js')

Page({
  data: {
    orderData: {},
    loading: false
  },

  onLoad: function (options) {
    console.log('订单待支付页面加载，参数:', options);

    // 从参数或本地存储获取订单数据
    let orderData = null;

    if (options.orderData) {
      // 从页面参数获取
      try {
        orderData = JSON.parse(decodeURIComponent(options.orderData));
      } catch (e) {
        console.error('解析订单数据失败:', e);
      }
    }

    if (!orderData) {
      // 从本地存储获取
      orderData = wx.getStorageSync('currentOrder');
    }

    if (orderData) {
      // 确保订单状态为待支付
      orderData.status = 'pending';
      orderData.paymentStatus = 'unpaid';

      // 确保必要的字段存在
      orderData = this.normalizeOrderData(orderData);

      this.setData({
        orderData: orderData
      });

      // 更新本地存储
      wx.setStorageSync('currentOrder', orderData);
    } else {
      // 如果没有订单数据，返回首页
      wx.showModal({
        title: '提示',
        content: '订单信息丢失，请重新预订',
        showCancel: false,
        success: () => {
          this.backToHome();
        }
      });
    }
  },

  // 标准化订单数据
  normalizeOrderData: function (orderData) {
    console.log('标准化订单数据:', orderData);

    // 确保必要字段存在
    const normalized = {
      orderId: orderData.orderId || orderData.id,
      orderNo: orderData.orderNo || orderData.orderNumber || orderData.outTradeNo || this.generateOrderId(),
      conferenceId: orderData.conferenceId,
      conferenceTitle: orderData.conferenceTitle || orderData.conferenceName || '会议酒店预订',
      categoryId: orderData.categoryId,
      roomId: orderData.roomId,
      roomName: orderData.roomName || orderData.roomTitle || orderData.roomType || '标准房',
      roomType: orderData.roomType || orderData.roomName,
      checkinDate: orderData.checkinDate || orderData.checkInDate,
      checkoutDate: orderData.checkoutDate || orderData.checkOutDate,
      nights: orderData.nights || this.calculateNights(orderData.checkinDate, orderData.checkoutDate),
      depositAmount: orderData.depositAmount || orderData.deposit || 200,
      totalAmount: orderData.totalAmount || orderData.totalPrice || orderData.depositAmount || 200,
      status: 'pending',
      paymentStatus: 'unpaid',
      guestInfo: orderData.guestInfo || {
        name: orderData.guestName || '',
        phone: orderData.guestPhone || '',
        idCard: orderData.guestIdCard || ''
      },
      createTime: orderData.createTime || orderData.createdAt || new Date().toISOString(),
      orderTime: orderData.orderTime || new Date().toISOString(),
      // 保留原始数据中的所有字段，以防遗漏
      ...orderData
    };

    // 确保关键字段不被覆盖
    normalized.status = 'pending';
    normalized.paymentStatus = 'unpaid';

    console.log('标准化后的订单数据:', normalized);
    return normalized;
  },

  // 计算住宿天数
  calculateNights: function (checkinDate, checkoutDate) {
    if (!checkinDate || !checkoutDate) return 1;

    const checkin = new Date(checkinDate);
    const checkout = new Date(checkoutDate);

    if (isNaN(checkin.getTime()) || isNaN(checkout.getTime())) return 1;

    const diffTime = checkout.getTime() - checkin.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(1, diffDays);
  },

  // 生成订单ID
  generateOrderId: function () {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `ORDER${timestamp}${random}`;
  },

  onShow: function () {
    console.log('订单待支付页面显示');
  },

  // 重新支付
  retryPayment: function () {
    if (this.data.loading) {
      return;
    }

    const orderData = this.data.orderData;
    if (!orderData) {
      wx.showToast({
        title: '订单信息异常',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    // 优先尝试使用已有的prepay_id重新调起支付
    if (orderData.orderNo) {
      console.log('尝试使用已有prepay_id重新支付，订单号:', orderData.orderNo);

      PaymentUtils.retryPaymentWithPrepayId(orderData.orderNo)
        .then((res) => {
          console.log('使用已有prepay_id支付成功', res);
          this.handleSecurePaymentSuccess(orderData, res);
        })
        .catch((err) => {
          console.log('使用已有prepay_id支付失败', err);

          // 如果prepay_id无效，则重新创建订单
          if (err.message === 'PREPAY_ID_INVALID') {
            console.log('prepay_id已失效，重新创建订单');
            this.createNewOrderAndPay(orderData);
          } else {
            this.handlePaymentFail(err);
          }
        })
        .finally(() => {
          this.setData({ loading: false });
        });
    } else {
      // 没有订单号，直接创建新订单
      this.createNewOrderAndPay(orderData);
    }
  },

  // 创建新订单并支付
  createNewOrderAndPay: function (orderData) {
    // 检查必要的支付信息
    if (!orderData.conferenceId || !orderData.roomId) {
      wx.showModal({
        title: '订单信息不完整',
        content: '订单缺少必要的预订信息，请重新预订',
        showCancel: false,
        confirmText: '重新预订',
        success: () => {
          this.backToHome();
        }
      });
      this.setData({ loading: false });
      return;
    }

    // 构建支付请求数据
    const createOrderData = {
      conferenceId: orderData.conferenceId,
      categoryId: orderData.categoryId,
      roomId: orderData.roomId,
      checkinDate: orderData.checkinDate,
      checkoutDate: orderData.checkoutDate,
      nights: orderData.nights,
      guestName: orderData.guestInfo?.name || '',
      guestPhone: orderData.guestInfo?.phone || '',
      guestIdCard: orderData.guestInfo?.idCard || '',
      totalAmount: orderData.depositAmount || 200,
      depositAmount: orderData.depositAmount || 200
    };

    console.log('重新创建订单并发起支付，订单数据:', createOrderData);

    // 调用支付工具类创建订单并支付
    PaymentUtils.createPayment(createOrderData)
      .then((res) => {
        console.log('重新创建订单支付成功', res);

        // 更新订单数据，保存后端返回的订单号
        if (res.orderNo) {
          orderData.orderNo = res.orderNo;
        }
        if (res.orderId) {
          orderData.orderId = res.orderId;
        }

        this.handleSecurePaymentSuccess(orderData, res);
      })
      .catch((err) => {
        console.log('重新创建订单支付失败', err);
        this.handlePaymentFail(err);
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 安全的支付成功处理
  handleSecurePaymentSuccess: function (orderData, paymentResult) {
    console.log('开始安全支付成功处理，订单数据:', orderData, '支付结果:', paymentResult);

    // 显示处理中状态
    wx.showLoading({
      title: '正在确认支付...',
      mask: true
    });

    const PaymentUtils = require('../../utils/payment.js');

    // 使用安全的支付成功处理器，进行二次验证
    PaymentUtils.securePaymentSuccessHandler(orderData.orderNo, paymentResult)
      .then(updatedOrder => {
        console.log('安全支付验证成功', updatedOrder);
        this.handlePaymentSuccess(orderData, updatedOrder);
      })
      .catch(err => {
        console.error('安全支付验证失败', err);
        // 即使验证失败，也认为支付可能成功了，给用户友好提示
        this.handlePaymentSuccessWithWarning(orderData, err);
      });
  },

  // 支付成功处理
  handlePaymentSuccess: function (orderData, updatedOrder) {
    console.log('处理支付成功，订单数据:', orderData, '更新后订单:', updatedOrder);

    // 更新本地订单数据
    orderData.status = 'paid';
    orderData.paymentStatus = 'paid';
    orderData.paymentTime = new Date().toISOString();
    if (updatedOrder && updatedOrder.orderId) {
      orderData.orderId = updatedOrder.orderId;
    }

    // 保存到本地存储
    wx.setStorageSync('currentOrder', orderData);

    // 添加到订单历史
    let orderHistory = wx.getStorageSync('orderHistory') || [];
    orderHistory.unshift(orderData);
    wx.setStorageSync('orderHistory', orderHistory);

    wx.hideLoading();

    // 跳转到支付成功页面
    wx.redirectTo({
      url: '/pages/payment-success/payment-success'
    });
  },

  // 支付成功但验证有警告的处理
  handlePaymentSuccessWithWarning: function (orderData, error) {
    console.log('支付成功但验证有警告，订单数据:', orderData, '错误:', error);

    wx.hideLoading();

    // 给用户提示可能存在延迟
    wx.showModal({
      title: '支付成功',
      content: '支付已完成，订单状态可能稍有延迟，请稍后查看订单详情',
      showCancel: false,
      success: () => {
        // 更新本地订单数据
        orderData.status = 'paid';
        orderData.paymentStatus = 'paid';
        orderData.paymentTime = new Date().toISOString();

        // 保存到本地存储
        wx.setStorageSync('currentOrder', orderData);

        // 添加到订单历史
        let orderHistory = wx.getStorageSync('orderHistory') || [];
        orderHistory.unshift(orderData);
        wx.setStorageSync('orderHistory', orderHistory);

        // 跳转到支付成功页面
        wx.redirectTo({
          url: '/pages/payment-success/payment-success'
        });
      }
    });
  },

  // 支付失败处理
  handlePaymentFail: function (error) {
    if (error.message === '用户取消支付') {
      // 用户再次取消支付，保持在当前页面
      wx.showToast({
        title: '支付已取消',
        icon: 'none'
      });
    } else {
      // 其他支付错误
      wx.showModal({
        title: '支付失败',
        content: '支付过程中出现问题，请重试或联系客服',
        showCancel: true,
        cancelText: '重试',
        confirmText: '联系客服',
        success: (res) => {
          if (res.confirm) {
            this.contactService();
          } else if (res.cancel) {
            // 用户选择重试
            this.retryPayment();
          }
        }
      });
    }
  },

  // 取消订单
  cancelOrder: function () {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？取消后需要重新预订。',
      success: (res) => {
        if (res.confirm) {
          // 清除订单数据
          wx.removeStorageSync('currentOrder');
          
          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });
          
          // 返回首页
          setTimeout(() => {
            this.backToHome();
          }, 1500);
        }
      }
    });
  },

  // 联系客服
  contactService: function () {
    wx.makePhoneCall({
      phoneNumber: '4008889999',
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回首页
  backToHome: function () {
    wx.switchTab({
      url: '/pages/conference-list/conference-list'
    });
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${month}-${day}`;
  },

  // 格式化时间
  formatTime: function (timeString) {
    if (!timeString) return '';
    
    const date = new Date(timeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定 - 订单待支付',
      path: '/pages/conference-list/conference-list'
    };
  }
});
