// order-list.js
const api = require('../../utils/api.js')
const auth = require('../../utils/auth.js')

Page({
  data: {
    activeTab: 'all', // 当前选中的标签
    orderList: [],
    filteredOrderList: [],
    loading: false,
    isEmpty: false,
    emptyTitle: '暂无订单',
    emptySubtitle: '预订酒店后，订单信息将在这里显示',
    tabs: [
      { key: 'all', name: '全部', count: 0 },
      { key: 'pending', name: '待支付', count: 0 },
      { key: 'paid', name: '已支付', count: 0 },
      { key: 'checkedin', name: '已入住', count: 0 },
      { key: 'completed', name: '已完成', count: 0 },
      { key: 'cancelled', name: '已取消', count: 0 }
    ]
  },

  onLoad: function (options) {
    console.log('订单列表页面加载');
    this.loadOrderList();
  },

  onShow: function () {
    console.log('订单列表页面显示');
    // 每次显示时刷新数据
    this.loadOrderList();
  },

  onPullDownRefresh: function () {
    console.log('下拉刷新');
    this.loadOrderList().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载订单列表
  loadOrderList: function () {
    if (!auth.isLoggedIn()) {
      wx.showModal({
        title: '提示',
        content: '请先登录查看订单',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/profile/profile'
          });
        }
      });
      return Promise.resolve();
    }

    this.setData({ loading: true });

    return api.API.order.getList()
      .then(orders => {
        console.log('获取订单列表成功:', orders);
        this.processOrderData(orders);
      })
      .catch(err => {
        console.error('获取订单列表失败:', err);
        // 使用本地数据作为后备方案
        this.loadLocalOrderData();
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 处理订单数据
  processOrderData: function (orders) {
    const orderList = Array.isArray(orders) ? orders : (orders.data || []);
    console.log('处理订单数据:', orderList);

    const processedOrders = orderList.map(order => {
      const statusValue = order.orderStatus || order.status;
      const normalizedStatus = this.normalizeStatus(statusValue);

      return {
        orderId: order.orderId || order.id,
        orderNo: order.orderNo || order.orderNumber,
        roomName: order.roomName || order.roomTitle || '未知房型',
        checkinDate: this.formatDate(order.checkinDate || order.checkInDate),
        checkoutDate: this.formatDate(order.checkoutDate || order.checkOutDate),
        nights: order.nights || this.calculateNights(order.checkinDate || order.checkInDate, order.checkoutDate || order.checkOutDate),
        totalPrice: order.totalAmount || order.totalPrice || 0,
        depositAmount: order.depositAmount || order.deposit || 0,
        status: normalizedStatus,
        statusText: this.getStatusText(normalizedStatus),
        conferenceTitle: order.conferenceTitle || order.conferenceName || '',
        conferenceId: order.conferenceId,
        categoryId: order.categoryId,
        roomId: order.roomId,
        roomType: order.roomType,
        guestName: order.guestName,
        guestPhone: order.guestPhone,
        guestIdCard: order.guestIdCard,
        createTime: order.createTime || order.createdAt,
        refundAmount: order.refundAmount || 0,
        refundTime: order.refundTime
      };
    });

    // 过滤掉超过30分钟未支付的订单（小程序端双重保障）
    const filteredOrders = processedOrders.filter(order => {
      // 如果不是待支付状态，直接显示
      if (order.status !== 'pending') {
        return true;
      }

      // 如果是待支付状态，检查是否超过30分钟
      if (order.createTime) {
        const createTime = new Date(order.createTime).getTime();
        const currentTime = new Date().getTime();
        const diffMinutes = (currentTime - createTime) / (1000 * 60);

        // 超过30分钟的待支付订单不显示
        return diffMinutes <= 30;
      }

      // 如果没有创建时间，为了安全起见不显示
      return false;
    });

    // 按创建时间排序，最新的在前
    filteredOrders.sort((a, b) => {
      const timeA = new Date(a.createTime || 0).getTime();
      const timeB = new Date(b.createTime || 0).getTime();
      return timeB - timeA;
    });

    this.setData({
      orderList: filteredOrders,
      isEmpty: filteredOrders.length === 0
    });

    // 更新标签计数
    this.updateTabCounts(filteredOrders);

    // 过滤当前标签的订单
    this.filterOrdersByTab(this.data.activeTab);

    // 更新空状态文本
    this.updateEmptyText(this.data.activeTab);
  },

  // 从本地存储加载数据（后备方案）
  loadLocalOrderData: function () {
    console.log('使用本地数据作为后备方案');
    const orderHistory = wx.getStorageSync('orderHistory') || [];

    const processedOrders = orderHistory.map(order => {
      const statusValue = order.orderStatus || order.status;
      const normalizedStatus = this.normalizeStatus(statusValue);

      return {
        ...order,
        status: normalizedStatus,
        statusText: this.getStatusText(normalizedStatus)
      };
    });

    // 过滤掉超过30分钟未支付的订单
    const filteredOrders = processedOrders.filter(order => {
      // 如果不是待支付状态，直接显示
      if (order.status !== 'pending') {
        return true;
      }

      // 如果是待支付状态，检查是否超过30分钟
      if (order.createTime) {
        const createTime = new Date(order.createTime).getTime();
        const currentTime = new Date().getTime();
        const diffMinutes = (currentTime - createTime) / (1000 * 60);

        // 超过30分钟的待支付订单不显示
        return diffMinutes <= 30;
      }

      // 如果没有创建时间，为了安全起见不显示
      return false;
    });

    this.setData({
      orderList: filteredOrders,
      isEmpty: filteredOrders.length === 0
    });

    this.updateTabCounts(filteredOrders);
    this.filterOrdersByTab(this.data.activeTab);
    this.updateEmptyText(this.data.activeTab);
  },

  // 标准化状态值
  normalizeStatus: function (status) {
    if (!status) return 'pending';

    const statusStr = String(status).toLowerCase();
    const statusMapping = {
      'pending': 'pending', 'unpaid': 'pending', 'wait_pay': 'pending', 'waitpay': 'pending', 'created': 'pending', '0': 'pending',
      'paid': 'paid', 'payed': 'paid', 'success': 'paid', 'confirmed': 'paid', 'payment_success': 'paid', '1': 'paid',
      'checkedin': 'checkedin', 'checked_in': 'checkedin', 'in_use': 'checkedin', 'using': 'checkedin', '2': 'checkedin',
      'completed': 'completed', 'finished': 'completed', 'done': 'completed', 'checkout': 'completed', 'checked_out': 'completed', '3': 'completed',
      'cancelled': 'cancelled', 'canceled': 'cancelled', 'cancel': 'cancelled', '4': 'cancelled', '-1': 'cancelled',
      'refunded': 'refunded', 'refund': 'refunded', 'refund_success': 'refunded', '5': 'refunded'
    };

    return statusMapping[statusStr] || 'pending';
  },

  // 获取状态文本
  getStatusText: function (status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'checkedin': '已入住',
      'completed': '已完成',
      'cancelled': '已取消',
      'refunded': '已退款'
    };
    return statusMap[status] || `未知状态(${status})`;
  },

  // 更新标签计数
  updateTabCounts: function (orders) {
    const counts = {
      all: orders.length,
      pending: 0,
      paid: 0,
      checkedin: 0,
      completed: 0,
      cancelled: 0,
      refunded: 0
    };

    orders.forEach(order => {
      if (counts.hasOwnProperty(order.status)) {
        counts[order.status]++;
      }
    });

    const updatedTabs = this.data.tabs.map(tab => ({
      ...tab,
      count: counts[tab.key] || 0
    }));

    this.setData({ tabs: updatedTabs });
  },

  // 标签切换
  onTabTap: function (e) {
    const tabKey = e.currentTarget.dataset.tab;
    console.log('切换标签:', tabKey);

    this.setData({ activeTab: tabKey });
    this.filterOrdersByTab(tabKey);
    this.updateEmptyText(tabKey);
  },

  // 根据标签过滤订单
  filterOrdersByTab: function (tabKey) {
    let filteredList = this.data.orderList;

    if (tabKey !== 'all') {
      filteredList = this.data.orderList.filter(order => order.status === tabKey);
    }

    this.setData({
      filteredOrderList: filteredList,
      isEmpty: filteredList.length === 0
    });
  },

  // 更新空状态文本
  updateEmptyText: function (tabKey) {
    let emptyTitle = '暂无订单';
    let emptySubtitle = '预订酒店后，订单信息将在这里显示';

    if (tabKey !== 'all') {
      // 找到对应的标签名称
      const currentTab = this.data.tabs.find(tab => tab.key === tabKey);
      if (currentTab) {
        emptyTitle = `暂无${currentTab.name}订单`;
        emptySubtitle = '切换到其他标签查看更多订单';
      }
    }

    this.setData({
      emptyTitle: emptyTitle,
      emptySubtitle: emptySubtitle
    });
  },

  // 订单项点击处理
  onOrderItemTap: function (e) {
    const orderData = e.currentTarget.dataset.order;
    console.log('点击订单:', orderData);
    
    if (!orderData) {
      wx.showToast({
        title: '订单信息异常',
        icon: 'none'
      });
      return;
    }

    // 根据订单状态决定跳转行为
    switch (orderData.status) {
      case 'pending':
        // 待支付订单，跳转到订单待支付页面
        this.navigateToOrderPending(orderData);
        break;
      case 'paid':
      case 'checkedin':
      case 'completed':
      case 'cancelled':
      case 'refunded':
        // 其他状态订单，显示订单详情
        this.showOrderDetail(orderData);
        break;
      default:
        wx.showToast({
          title: '未知订单状态',
          icon: 'none'
        });
    }
  },

  // 跳转到订单待支付页面
  navigateToOrderPending: function (orderData) {
    console.log('跳转到订单待支付页面，订单数据:', orderData);
    
    // 构建完整的订单数据
    const completeOrderData = {
      orderId: orderData.orderId,
      orderNo: orderData.orderNo,
      conferenceId: orderData.conferenceId,
      conferenceTitle: orderData.conferenceTitle,
      categoryId: orderData.categoryId,
      roomId: orderData.roomId,
      roomName: orderData.roomName,
      roomType: orderData.roomType,
      checkinDate: orderData.checkinDate,
      checkoutDate: orderData.checkoutDate,
      nights: orderData.nights,
      depositAmount: orderData.depositAmount || 200,
      totalAmount: orderData.totalPrice || orderData.depositAmount || 200,
      status: 'pending',
      paymentStatus: 'unpaid',
      guestInfo: {
        name: orderData.guestName || '',
        phone: orderData.guestPhone || '',
        idCard: orderData.guestIdCard || ''
      }
    };

    // 保存到本地存储
    wx.setStorageSync('currentOrder', completeOrderData);

    // 跳转到订单待支付页面
    wx.navigateTo({
      url: '/pages/order-pending/order-pending'
    });
  },

  // 显示订单详情
  showOrderDetail: function (orderData) {
    let content = `订单号：${orderData.orderNo}\n`;
    content += `房型：${orderData.roomName}\n`;
    content += `入住：${orderData.checkinDate} (${orderData.nights}晚)\n`;
    content += `退房：${orderData.checkoutDate}\n`;
    content += `状态：${orderData.statusText}\n`;
    
    if (orderData.status === 'refunded' && orderData.refundAmount) {
      content += `退款金额：¥${orderData.refundAmount}`;
    } else {
      content += `金额：¥${orderData.totalPrice || orderData.depositAmount || 0}`;
    }

    wx.showModal({
      title: '订单详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 格式化日期
  formatDate: function (dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;

    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}-${day}`;
  },

  // 计算住宿天数
  calculateNights: function (checkinDate, checkoutDate) {
    if (!checkinDate || !checkoutDate) return 1;

    const checkin = new Date(checkinDate);
    const checkout = new Date(checkoutDate);

    if (isNaN(checkin.getTime()) || isNaN(checkout.getTime())) return 1;

    const diffTime = checkout.getTime() - checkin.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(1, diffDays);
  },

  // 前往预订页面
  goToBooking: function () {
    wx.switchTab({
      url: '/pages/conference-list/conference-list'
    });
  }
});
