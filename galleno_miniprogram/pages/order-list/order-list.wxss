/* order-list.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签栏 */
.tab-bar {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-scroll {
  white-space: nowrap;
}

.tab-list {
  display: flex;
  padding: 0 20rpx;
}

.tab-item {
  flex-shrink: 0;
  padding: 24rpx 20rpx;
  margin-right: 20rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.tab-item.active {
  color: #3b82f6;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #3b82f6;
  border-radius: 2rpx;
}

.tab-name {
  font-size: 28rpx;
  font-weight: 500;
}

.tab-count {
  font-size: 24rpx;
  margin-left: 8rpx;
  opacity: 0.7;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.loading-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

.order-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.order-item:active {
  background: #f8f9fa;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-number {
  font-size: 26rpx;
  color: #666;
  font-family: monospace;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.order-status.pending {
  background: #fff3cd;
  color: #856404;
}

.order-status.paid {
  background: #d4edda;
  color: #155724;
}

.order-status.checkedin {
  background: #cce5ff;
  color: #004085;
}

.order-status.completed {
  background: #e2e3e5;
  color: #383d41;
}

.order-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.order-status.refunded {
  background: #ffeaa7;
  color: #6c5ce7;
}

/* 订单内容 */
.order-content {
  margin-bottom: 20rpx;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 100rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 订单底部 */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.price-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.price-value {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  border: 1rpx solid #e9ecef;
  background: #f8f9fa;
  color: #666;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.action-btn:active {
  opacity: 0.8;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-btn {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.empty-btn:active {
  opacity: 0.8;
}
