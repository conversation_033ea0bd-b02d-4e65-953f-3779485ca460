/* payment-success.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 成功状态 */
.success-section {
  background: #ffffff;
  padding: 80rpx 32rpx 60rpx;
  text-align: center;
  margin-bottom: 32rpx;
}

.success-icon {
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.icon {
  font-size: 80rpx;
  color: #ffffff;
  font-weight: bold;
}

.success-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.success-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
}

/* 订单信息 */
.order-section {
  margin: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.order-card {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #ffffff;
}

.order-number {
  font-size: 28rpx;
  font-weight: 500;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.order-status.paid {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.order-content {
  padding: 32rpx;
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.order-row:last-child {
  margin-bottom: 0;
}

.order-label {
  font-size: 28rpx;
  color: #6b7280;
}

.order-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 32rpx;
}

.order-footer {
  background: #f9fafb;
  padding: 32rpx;
  border-top: 2rpx solid #e5e7eb;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 28rpx;
  color: #6b7280;
}

.price-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
}

.price-row.deposit .price-label,
.price-row.deposit .price-value {
  color: #10b981;
}

.price-row.remaining .price-label,
.price-row.remaining .price-value {
  color: #f59e0b;
}

/* 重要提醒 */
.reminder-section {
  margin: 32rpx;
}

.reminder-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.reminder-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.reminder-item:last-child {
  margin-bottom: 0;
}

.reminder-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

.reminder-content {
  flex: 1;
}

.reminder-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.reminder-text {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 操作按钮 */
.action-section {
  margin: 32rpx;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  padding: 32rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background: #3b82f6;
  color: #ffffff;
}

.action-btn.primary:active {
  background: #2563eb;
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 2rpx solid #e5e7eb;
}

.action-btn.secondary:active {
  background: #e5e7eb;
}

/* 底部间距 */
.bottom-spacing {
  height: 40rpx;
}
