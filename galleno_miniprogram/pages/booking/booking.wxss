/* booking.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 160rpx;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 预订信息摘要 */
.booking-summary {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.summary-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  padding: 32rpx;
  color: #ffffff;
}

.summary-title {
  font-size: 32rpx;
  font-weight: 600;
}

.summary-content {
  padding: 32rpx;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 28rpx;
  color: #6b7280;
}

.summary-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 32rpx;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
  width: 100%;
  min-width: 600rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.required {
  color: #ef4444;
}

.form-input {
  width: 100% !important;
  min-width: 600rpx !important;
  padding: 32rpx !important;
  border: 2rpx solid #e5e7eb !important;
  border-radius: 12rpx !important;
  font-size: 32rpx !important;
  background: #ffffff !important;
  background-color: #ffffff !important;
  color: #1f2937 !important;
  line-height: 1.5 !important;
  box-sizing: border-box !important;
  transition: all 0.3s ease;
  -webkit-appearance: none !important;
  -webkit-tap-highlight-color: transparent !important;
  opacity: 1 !important;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* 姓名输入框特殊样式 */
.form-input.name-input {
  min-width: 700rpx !important;
  padding: 36rpx !important;
  font-size: 36rpx !important;
  font-weight: 500;
}

.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
}



.error-text {
  display: block;
  font-size: 24rpx;
  color: #ef4444;
  margin-top: 8rpx;
}



/* 联系方式确认 */
.contact-info {
  background: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  font-size: 28rpx;
  color: #6b7280;
}

.contact-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

/* 费用明细 */
.cost-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.cost-details {
  background: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
}

.cost-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.cost-row:last-child {
  margin-bottom: 0;
}

.cost-label {
  font-size: 28rpx;
  color: #6b7280;
}

.cost-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

.cost-row.total .cost-label,
.cost-row.total .cost-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

.cost-row.deposit .cost-label,
.cost-row.deposit .cost-value {
  color: #f59e0b;
  font-weight: 600;
}

.cost-divider {
  height: 2rpx;
  background: #e5e7eb;
  margin: 16rpx 0;
}

/* 预订条款 */
.terms-section {
  margin: 32rpx;
}

.terms-checkbox {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d1d5db;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  font-size: 24rpx;
  color: #ffffff;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #3b82f6;
  border-color: #3b82f6;
}

.terms-text {
  font-size: 28rpx;
  color: #6b7280;
  margin-right: 8rpx;
}

.terms-link {
  font-size: 28rpx;
  color: #3b82f6;
  text-decoration: underline;
}

/* 底部支付栏 */
.payment-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 2rpx solid #e5e7eb;
  padding: 32rpx;
  display: flex;
  align-items: center;
  gap: 32rpx;
  z-index: 100;
}

.payment-info {
  flex-shrink: 0;
}

.payment-label {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}

.payment-price {
  font-size: 40rpx;
  font-weight: bold;
  color: #f59e0b;
}

.payment-btn {
  flex: 1;
  background: #f59e0b;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.payment-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.payment-btn::after {
  border: none;
}

/* 底部间距 */
.bottom-spacing {
  height: 40rpx;
}
