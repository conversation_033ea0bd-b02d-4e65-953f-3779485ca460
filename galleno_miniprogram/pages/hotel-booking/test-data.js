// test-data.js - 测试数据，用于验证房型数据转换逻辑

// 模拟validateCode接口返回的数据结构
const mockCategoryCodeResponse = {
  categoryId: "TEST123",
  conference: 1,
  roomCount: 10,
  conferenceTitle: "2024年度科技创新大会",
  roomList: [
    {
      id: 1,
      conferenceId: 1,
      roomTitle: "豪华大床房",
      roomImgUrl: "https://example.com/deluxe.jpg",
      operator: "admin",
      roomCount: 3
    },
    {
      id: 2,
      conferenceId: 1,
      roomTitle: "商务双床房",
      roomImgUrl: "https://example.com/business.jpg",
      operator: "admin",
      roomCount: 4
    },
    {
      id: 3,
      conferenceId: 1,
      roomTitle: "标准大床房",
      roomImgUrl: "https://example.com/standard.jpg",
      operator: "admin",
      roomCount: 3
    }
  ]
};

// 预期转换后的房型数据格式
const expectedRoomTypes = [
  {
    id: 1,
    type: "room_1",
    name: "豪华大床房",
    size: "35",
    bedType: "大床",
    available: 3,
    price: 588,
    facilities: ['📶', '📺', '🛁', '☕'],
    originalData: {
      id: 1,
      conferenceId: 1,
      roomTitle: "豪华大床房",
      roomImgUrl: "https://example.com/deluxe.jpg",
      operator: "admin",
      roomCount: 3
    }
  },
  {
    id: 2,
    type: "room_2",
    name: "商务双床房",
    size: "42",
    bedType: "双床",
    available: 4,
    price: 688,
    facilities: ['📶', '📺', '🛁', '☕', '💼'],
    originalData: {
      id: 2,
      conferenceId: 1,
      roomTitle: "商务双床房",
      roomImgUrl: "https://example.com/business.jpg",
      operator: "admin",
      roomCount: 4
    }
  },
  {
    id: 3,
    type: "room_3",
    name: "标准大床房",
    size: "28",
    bedType: "大床",
    available: 3,
    price: 388,
    facilities: ['📶', '📺', '🛁'],
    originalData: {
      id: 3,
      conferenceId: 1,
      roomTitle: "标准大床房",
      roomImgUrl: "https://example.com/standard.jpg",
      operator: "admin",
      roomCount: 3
    }
  }
];

module.exports = {
  mockCategoryCodeResponse,
  expectedRoomTypes
};
