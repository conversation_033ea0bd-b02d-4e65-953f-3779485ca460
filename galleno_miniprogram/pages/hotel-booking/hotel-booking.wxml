<!--hotel-booking.wxml-->
<view class="container">
  <!-- 会议信息横幅 -->
  <view class="conference-banner">
    <view class="banner-content">
      <text class="banner-icon">📅</text>
      <view class="banner-info">
        <text class="conference-title">{{conferenceTitle}}</text>
        <text class="conference-details">{{conferenceLocation}} · {{conferenceDate}}</text>
      </view>
    </view>
    <view class="banner-tip">
      <text class="tip-icon">ℹ️</text>
      <text class="tip-text">请选择房型和入住日期完成预订</text>
    </view>
  </view>

  <!-- 房型选择 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">选择房型</view>
      <view class="refresh-btn" bindtap="refreshRoomTypes" wx:if="{{!loading}}">
        <text class="refresh-icon">🔄</text>
        <text class="refresh-text">重新加载</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-content">
        <text class="loading-icon">⏳</text>
        <text class="loading-text">正在加载房型信息...</text>
      </view>
    </view>

    <!-- 房型列表 -->
    <view class="room-list" wx:elif="{{roomTypes.length > 0}}">
      <view class="room-card {{selectedRoom === item.type ? 'selected' : ''}}"
            wx:for="{{roomTypes}}"
            wx:key="id"
            bindtap="selectRoom"
            data-room="{{item}}">

        <view class="room-info">
          <text class="room-name">{{item.name}}</text>

          <view class="room-bottom">
            <view class="room-facilities">
            </view>
          </view>
        </view>

        <!-- 选中指示器 -->
        <view class="select-indicator" wx:if="{{selectedRoom === item.type}}">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>

    <!-- 无房型提示 -->
    <view class="empty-room-list" wx:else>
      <view class="empty-content">
        <text class="empty-icon">🏨</text>
        <text class="empty-text">暂无可预订的房型</text>
        <text class="empty-tip">请联系会议组织方确认房型配置</text>
      </view>
    </view>
  </view>

  <!-- 日期选择区域 -->
  <view class="section" wx:if="{{selectedRoom}}">
    <view class="section-title">选择入住日期</view>

    <!-- 日期范围提示 -->
    <view class="date-range-tip" wx:if="{{bookingRangeStart && bookingRangeEnd}}">
      <text class="tip-icon">📅</text>
      <text class="tip-text">可预订日期：{{bookingRangeStart}} 至 {{bookingRangeEnd}}</text>
    </view>

    <view class="date-section">
      <view class="date-row">
        <!-- 入住日期选择 -->
        <picker mode="date"
                value="{{checkinDate || minDate}}"
                start="{{minDate}}"
                end="{{maxDate}}"
                bindchange="onCheckinDateChange"
                class="date-picker-inline">
          <view class="date-item">
            <text class="date-label">入住日期</text>
            <view class="date-value">
              <text class="date-text">{{checkinDate || '请选择'}}</text>
              <text class="date-icon">📅</text>
            </view>
          </view>
        </picker>

        <!-- 退房日期选择 -->
        <picker mode="date"
                value="{{checkoutDate || checkinDate}}"
                start="{{checkinDate || minDate}}"
                end="{{maxDate}}"
                bindchange="onCheckoutDateChange"
                disabled="{{!checkinDate}}"
                class="date-picker-inline">
          <view class="date-item {{!checkinDate ? 'disabled' : ''}}">
            <text class="date-label">退房日期</text>
            <view class="date-value">
              <text class="date-text">{{checkoutDate || '请选择'}}</text>
              <text class="date-icon">📅</text>
            </view>
          </view>
        </picker>
      </view>
      
      <!-- 定金信息 -->
      <view class="deposit-info" wx:if="{{checkinDate && checkoutDate}}">
        <view class="deposit-row">
          <text class="deposit-label">预订{{nights}}晚住宿</text>
          <text class="deposit-note">到店支付房费</text>
        </view>
        <view class="deposit-divider"></view>
        <view class="deposit-row main">
          <text class="deposit-label">需支付定金</text>
          <text class="deposit-value">¥200</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 底部预订栏 -->
<view class="booking-bar">
  <view class="booking-info">
    <text class="booking-label">定金</text>
    <text class="booking-price">¥200</text>
  </view>
  <button class="booking-btn {{canBook ? '' : 'disabled'}}" 
          disabled="{{!canBook}}" 
          bindtap="proceedToBooking">
    {{bookButtonText}}
  </button>
</view>


