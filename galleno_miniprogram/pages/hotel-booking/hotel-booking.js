// hotel-booking.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    conferenceId: '',
    conferenceTitle: '2024年度科技创新大会',
    conferenceLocation: '北京国际会议中心',
    conferenceDate: '2024年3月15-17日',
    categoryId: '', // 识别码
    conferenceData: null, // 完整的会议信息

    selectedRoom: '',
    selectedRoomData: null,
    checkinDate: '',
    checkoutDate: '',
    nights: 0,
    depositAmount: 200, // 固定定金金额

    minDate: '',
    maxDate: '',
    bookingRangeStart: '', // 会议预定开始日期
    bookingRangeEnd: '', // 会议预定结束日期

    canBook: false,
    bookButtonText: '请先选择房型和日期',
    loading: true,

    roomTypes: [] // 将从API获取数据
  },

  onLoad: function (options) {
    console.log('酒店预定页面接收参数:', options);

    // 获取传递的会议信息
    if (options.conferenceId) {
      this.setData({
        conferenceId: options.conferenceId
      });
    }

    if (options.title) {
      this.setData({
        conferenceTitle: decodeURIComponent(options.title)
      });
    }

    if (options.location) {
      this.setData({
        conferenceLocation: decodeURIComponent(options.location)
      });
    }

    if (options.date) {
      this.setData({
        conferenceDate: decodeURIComponent(options.date)
      });
    }

    if (options.categoryId) {
      this.setData({
        categoryId: decodeURIComponent(options.categoryId)
      });
    }

    // 加载会议详情和房型数据
    this.loadConferenceData();
  },

  // 初始化日期范围（后备方案）
  initDateRange: function () {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const maxDate = new Date(today);
    maxDate.setMonth(today.getMonth() + 3); // 3个月内

    this.setData({
      currentDate: this.formatDate(tomorrow),
      minDate: this.formatDate(tomorrow),
      maxDate: this.formatDate(maxDate),
      bookingRangeStart: this.formatDate(tomorrow),
      bookingRangeEnd: this.formatDate(maxDate)
    });

    console.log('使用默认日期范围:', {
      minDate: this.data.minDate,
      maxDate: this.data.maxDate
    });
  },

  // 加载会议数据
  loadConferenceData: function () {
    if (!this.data.conferenceId) {
      console.error('缺少会议ID参数');
      this.setData({
        loading: false,
        bookButtonText: '参数错误，无法加载会议信息'
      });
      return;
    }

    console.log('开始加载会议详情:', this.data.conferenceId);

    // 调用API获取会议详情
    api.API.conference.getConference(this.data.conferenceId)
      .then(conference => {
        console.log('获取会议详情成功:', conference);
        this.processConferenceData(conference);
        // 获取会议信息后再加载房型数据
        this.loadRoomTypes();
      })
      .catch(err => {
        console.error('获取会议详情失败:', err);
        // 使用静态数据作为后备方案
        this.setConferenceInfo(this.data.conferenceId);
        this.initDateRange();
        this.loadRoomTypes();
      });
  },

  // 处理会议数据
  processConferenceData: function (conference) {
    console.log('处理会议数据:', conference);

    // 更新会议基本信息
    this.setData({
      conferenceData: conference,
      conferenceTitle: conference.conferenceTitle || this.data.conferenceTitle,
      conferenceLocation: conference.address || this.data.conferenceLocation,
      conferenceDate: this.formatConferenceDate(conference.startTime, conference.endTime)
    });

    // 设置预定日期范围
    this.setBookingDateRange(conference);
  },

  // 设置预定日期范围
  setBookingDateRange: function (conference) {
    const today = new Date();
    const todayStr = this.formatDate(today);
    let minDate, maxDate, bookingRangeStart, bookingRangeEnd;

    // 使用会议的预定日期范围
    if (conference.bookingRangeStart && conference.bookingRangeEnd) {
      // 保持原始的预定范围用于验证
      bookingRangeStart = conference.bookingRangeStart;
      bookingRangeEnd = conference.bookingRangeEnd;

      // 计算实际可选的最小日期（不能早于今天）
      const rangeStartStr = conference.bookingRangeStart;
      const rangeEndStr = conference.bookingRangeEnd;

      // 使用字符串比较确定最小日期
      minDate = rangeStartStr > todayStr ? rangeStartStr : todayStr;
      maxDate = rangeEndStr;

      console.log('使用会议预定日期范围:', {
        会议原始开始日期: conference.bookingRangeStart,
        会议原始结束日期: conference.bookingRangeEnd,
        今天日期: todayStr,
        实际最小可选日期: minDate,
        实际最大可选日期: maxDate
      });
    } else {
      // 如果没有设置预定范围，使用默认范围
      console.log('会议未设置预定日期范围，使用默认范围');
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      const maxDateObj = new Date(today);
      maxDateObj.setMonth(today.getMonth() + 3); // 3个月内

      bookingRangeStart = this.formatDate(tomorrow);
      bookingRangeEnd = this.formatDate(maxDateObj);
      minDate = bookingRangeStart;
      maxDate = bookingRangeEnd;
    }

    this.setData({
      bookingRangeStart: bookingRangeStart,
      bookingRangeEnd: bookingRangeEnd,
      minDate: minDate,
      maxDate: maxDate
    });

    console.log('最终设置的日期范围:', {
      bookingRangeStart: this.data.bookingRangeStart,
      bookingRangeEnd: this.data.bookingRangeEnd,
      minDate: this.data.minDate,
      maxDate: this.data.maxDate
    });
  },

  // 格式化会议日期显示
  formatConferenceDate: function (startTime, endTime) {
    if (!startTime) return '时间待定';

    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : null;

    const formatDate = (date) => {
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    };

    if (end && start.getTime() !== end.getTime()) {
      return `${formatDate(start)}-${formatDate(end)}`;
    } else {
      return formatDate(start);
    }
  },

  // 设置会议信息
  setConferenceInfo: function (conferenceId) {
    const app = getApp()
    const conferenceInfo = app.globalData.conferenceInfo || {
      'tech2024': {
        title: '2024年度科技创新大会',
        location: '北京国际会议中心',
        date: '2024年3月15-17日'
      },
      'ai2024': {
        title: '人工智能发展论坛',
        location: '上海国际展览中心',
        date: '2024年3月10-12日'
      },
      'blockchain2024': {
        title: '区块链技术峰会',
        location: '深圳会展中心',
        date: '2024年3月20-22日'
      }
    };

    const info = conferenceInfo[conferenceId];
    if (info) {
      this.setData({
        conferenceTitle: info.title,
        conferenceLocation: info.location,
        conferenceDate: info.date
      });
    }
  },

  // 加载房型数据
  loadRoomTypes: function () {
    if (!this.data.categoryId || !this.data.conferenceId) {
      console.error('缺少必要参数:', {
        categoryId: this.data.categoryId,
        conferenceId: this.data.conferenceId
      });
      this.setData({
        loading: false,
        bookButtonText: '参数错误，无法加载房型'
      });

      // 显示错误提示
      wx.showModal({
        title: '参数错误',
        content: '缺少识别码或会议信息，请重新验证识别码',
        showCancel: false,
        confirmText: '返回',
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    console.log('开始加载房型数据:', {
      categoryId: this.data.categoryId,
      conferenceId: this.data.conferenceId
    });

    // 调用validateCode接口获取房型数据
    api.API.conference.validateCode(this.data.categoryId, this.data.conferenceId)
      .then(categoryCode => {
        console.log('获取房型数据成功:', categoryCode);
        this.processRoomData(categoryCode);
      })
      .catch(err => {
        console.error('获取房型数据失败:', err);
        this.setData({
          loading: false,
          bookButtonText: '加载房型失败，请重试'
        });

        // 显示错误提示并提供重试选项
        wx.showModal({
          title: '加载失败',
          content: '获取房型信息失败，是否重试？',
          confirmText: '重试',
          cancelText: '返回',
          success: (res) => {
            if (res.confirm) {
              this.setData({ loading: true });
              this.loadRoomTypes();
            } else {
              wx.navigateBack();
            }
          }
        });
      });
  },

  // 处理房型数据
  processRoomData: function (categoryCode) {
    if (!categoryCode || !categoryCode.roomList || categoryCode.roomList.length === 0) {
      console.log('没有可用的房型数据');
      this.setData({
        loading: false,
        roomTypes: [],
        bookButtonText: '暂无可预订房型'
      });
      return;
    }

    // 房型默认配置
    const roomDefaults = {
      size: '35',
      bedType: '标准',
      facilities: ['📶', '📺', '🛁']
    };

    // 根据房型名称智能匹配配置
    const getRoomConfig = (roomTitle) => {
      const title = (roomTitle || '').toLowerCase();

      if (title.includes('豪华') || title.includes('deluxe')) {
        return {
          size: '35',
          bedType: '大床',
          facilities: ['📶', '📺', '🛁', '☕']
        };
      } else if (title.includes('商务') || title.includes('business')) {
        return {
          size: '42',
          bedType: '双床',
          facilities: ['📶', '📺', '🛁', '☕', '💼']
        };
      } else if (title.includes('套房') || title.includes('suite')) {
        return {
          size: '65',
          bedType: '套房',
          facilities: ['📶', '📺', '🛁', '☕', '🛎️']
        };
      } else if (title.includes('标准') || title.includes('standard')) {
        return {
          size: '28',
          bedType: '大床',
          facilities: ['📶', '📺', '🛁']
        };
      }

      return roomDefaults;
    };

    // 转换房型数据格式
    const roomTypes = categoryCode.roomList.map((room, index) => {
      const config = getRoomConfig(room.roomTitle);

      return {
        id: room.id,
        type: `room_${room.id}`, // 使用房型ID作为type
        name: room.roomTitle || '未命名房型',
        size: config.size,
        bedType: config.bedType,
        available: room.roomCount || 1, // 使用roomCount作为可用数量
        facilities: config.facilities,
        originalData: room // 保存原始数据
      };
    });

    console.log('转换后的房型数据:', roomTypes);
    console.log('房型数据统计:', {
      总数量: roomTypes.length,
      房型名称: roomTypes.map(r => r.name),
      可用房间: roomTypes.map(r => r.available)
    });

    this.setData({
      loading: false,
      roomTypes: roomTypes,
      bookButtonText: '请先选择房型和日期'
    });
  },

  // 刷新房型数据
  refreshRoomTypes: function () {
    this.setData({
      loading: true,
      roomTypes: [],
      selectedRoom: '',
      selectedRoomData: null,
      checkinDate: '',
      checkoutDate: '',
      nights: 0
    });
    // 重新加载会议数据和房型数据
    this.loadConferenceData();
  },



  // 选择房型
  selectRoom: function (e) {
    const room = e.currentTarget.dataset.room;
    console.log('选择房型:', room);

    this.setData({
      selectedRoom: room.type,
      selectedRoomData: room
    });

    this.updateBookingButton();
    this.calculatePrice();
  },



  // 验证日期是否在允许范围内
  validateDateInRange: function (dateString) {
    // 检查必要的数据是否存在
    if (!this.data.bookingRangeStart || !this.data.bookingRangeEnd) {
      return true; // 如果没有设置范围，则允许选择
    }

    // 使用日期字符串直接比较，避免时区问题
    const selectedDateStr = dateString;
    const rangeStartStr = this.data.bookingRangeStart;
    const rangeEndStr = this.data.bookingRangeEnd;

    // 字符串比较（格式：YYYY-MM-DD）
    if (selectedDateStr < rangeStartStr || selectedDateStr > rangeEndStr) {
      wx.showToast({
        title: `请选择${rangeStartStr}至${rangeEndStr}之间的日期`,
        icon: 'none',
        duration: 3000
      });
      return false;
    }

    return true;
  },



  // 入住日期选择
  onCheckinDateChange: function (e) {
    const selectedDate = e.detail.value;
    console.log('选择入住日期:', selectedDate);

    // 验证日期是否在允许范围内
    if (!this.validateDateInRange(selectedDate)) {
      return;
    }

    this.setData({
      checkinDate: selectedDate,
      checkoutDate: '' // 清空退房日期，让用户重新选择
    });

    this.calculatePrice();
    this.updateBookingButton();
  },

  // 退房日期选择
  onCheckoutDateChange: function (e) {
    const selectedDate = e.detail.value;
    console.log('选择退房日期:', selectedDate);

    if (!this.data.checkinDate) {
      wx.showToast({
        title: '请先选择入住日期',
        icon: 'none'
      });
      return;
    }

    // 验证日期是否在允许范围内
    if (!this.validateDateInRange(selectedDate)) {
      return;
    }

    // 验证退房日期是否晚于入住日期
    const checkinDateStr = this.data.checkinDate;
    const checkoutDateStr = selectedDate;

    if (checkoutDateStr <= checkinDateStr) {
      wx.showToast({
        title: '退房日期必须晚于入住日期',
        icon: 'none'
      });
      return;
    }

    this.setData({
      checkoutDate: selectedDate
    });

    this.calculatePrice();
    this.updateBookingButton();
  },

  // 计算住宿天数
  calculatePrice: function () {
    if (!this.data.selectedRoomData || !this.data.checkinDate || !this.data.checkoutDate) {
      return;
    }

    const checkinDate = new Date(this.data.checkinDate);
    const checkoutDate = new Date(this.data.checkoutDate);
    const nights = Math.ceil((checkoutDate - checkinDate) / (1000 * 60 * 60 * 24));

    this.setData({
      nights: nights
    });
  },

  // 更新预订按钮状态
  updateBookingButton: function () {
    const canBook = this.data.selectedRoom && this.data.checkinDate && this.data.checkoutDate;
    let buttonText = '请先选择房型和日期';
    
    if (this.data.selectedRoom && !this.data.checkinDate) {
      buttonText = '请选择入住日期';
    } else if (this.data.selectedRoom && this.data.checkinDate && !this.data.checkoutDate) {
      buttonText = '请选择退房日期';
    } else if (canBook) {
      buttonText = '立即预订';
    }

    this.setData({
      canBook: canBook,
      bookButtonText: buttonText
    });
  },

  // 前往预订页面
  proceedToBooking: function () {
    if (!this.data.canBook) {
      wx.showToast({
        title: this.data.bookButtonText,
        icon: 'none'
      });
      return;
    }

    // 构建预订数据
    const bookingData = {
      conferenceId: this.data.conferenceId,
      conferenceTitle: this.data.conferenceTitle,
      categoryId: this.data.categoryId,
      roomId: this.data.selectedRoomData.id, // 使用房型ID
      roomType: this.data.selectedRoomData.type,
      roomName: this.data.selectedRoomData.name,
      checkinDate: this.data.checkinDate,
      checkoutDate: this.data.checkoutDate,
      nights: this.data.nights,
      depositAmount: this.data.depositAmount, // 定金金额
      originalRoomData: this.data.selectedRoomData.originalData // 保存原始房型数据
    };

    // 将数据存储到全局或传递给下一页
    wx.setStorageSync('bookingData', bookingData);

    wx.navigateTo({
      url: '/pages/booking/booking'
    });
  },

  // 格式化日期
  formatDate: function (date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: `${this.data.conferenceTitle} - 酒店预订`,
      path: `/pages/hotel-booking/hotel-booking?conferenceId=${this.data.conferenceId}&title=${encodeURIComponent(this.data.conferenceTitle)}`
    };
  }
});
