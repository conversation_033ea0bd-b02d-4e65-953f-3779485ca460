// app.js
const auth = require('./utils/auth.js')

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 获取系统信息
    this.getSystemInfo()

    // 初始化mock数据
    this.initMockData()

    // 初始化认证状态
    this.initAuth()
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      wx.getSystemInfo({
        success: (res) => {
          this.globalData.systemInfo = res
          console.log('系统信息:', res)

          // 设置屏幕信息
          this.globalData.screenWidth = res.screenWidth || 375
          this.globalData.screenHeight = res.screenHeight || 667
          this.globalData.windowWidth = res.windowWidth || 375
          this.globalData.windowHeight = res.windowHeight || 667
        },
        fail: (err) => {
          console.error('获取系统信息失败:', err)
          // 设置默认值
          this.globalData.screenWidth = 375
          this.globalData.screenHeight = 667
          this.globalData.windowWidth = 375
          this.globalData.windowHeight = 667
        }
      })
    } catch (e) {
      console.error('系统信息获取异常:', e)
    }
  },

  // 初始化mock数据
  initMockData() {
    // 检查是否已经有订单历史数据，如果没有则创建mock数据
    const orderHistory = wx.getStorageSync('orderHistory')
    if (!orderHistory || orderHistory.length === 0) {
      const mockOrders = [
        {
          orderId: 'HT202403150001',
          conferenceId: 'tech2024',
          conferenceTitle: '2024年度科技创新大会',
          roomType: 'deluxe',
          roomName: '豪华大床房',
          roomPrice: 588,
          checkinDate: '2024-03-16',
          checkoutDate: '2024-03-18',
          nights: 2,
          totalPrice: 1176,
          depositAmount: 200,
          status: 'paid',
          statusText: '已支付',
          orderTime: '2024-03-15T10:30:00.000Z',
          paymentTime: '2024-03-15T10:35:00.000Z',
          guestInfo: {
            name: '张先生',
            phone: '***********',
            idCard: '110101199001011234',
            specialRequests: ''
          }
        },
        {
          orderId: 'HT202403120002',
          conferenceId: 'ai2024',
          conferenceTitle: '人工智能发展论坛',
          roomType: 'business',
          roomName: '商务双床房',
          roomPrice: 688,
          checkinDate: '2024-03-13',
          checkoutDate: '2024-03-15',
          nights: 2,
          totalPrice: 1376,
          depositAmount: 200,
          status: 'completed',
          statusText: '已完成',
          orderTime: '2024-03-12T14:20:00.000Z',
          paymentTime: '2024-03-12T14:25:00.000Z',
          guestInfo: {
            name: '张先生',
            phone: '***********',
            idCard: '110101199001011234',
            specialRequests: '需要安静的房间'
          }
        }
      ]
      wx.setStorageSync('orderHistory', mockOrders)
    }

    // 检查用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo) {
      // 可以设置一个默认的用户信息用于测试
      const mockUserInfo = {
        name: '张先生',
        phone: '***********',
        idCard: '110101199001011234'
      }
      wx.setStorageSync('userInfo', mockUserInfo)
    }
  },

  globalData: {
    userInfo: null,
    systemInfo: null,
    screenWidth: 375,
    screenHeight: 667,
    windowWidth: 375,
    windowHeight: 667,
    // 会议识别码映射
    correctCodes: {
      'tech2024': 'TECH2024',
      'ai2024': 'AI2024',
      'blockchain2024': 'BLOCK2024'
    },
    // 会议信息
    conferenceInfo: {
      'tech2024': {
        title: '2024年度科技创新大会',
        location: '北京国际会议中心',
        date: '2024年3月15-17日'
      },
      'ai2024': {
        title: '人工智能发展论坛',
        location: '上海国际展览中心',
        date: '2024年3月10-12日'
      },
      'blockchain2024': {
        title: '区块链技术峰会',
        location: '深圳会展中心',
        date: '2024年3月20-22日'
      }
    }
  },

  // 初始化认证状态
  initAuth() {
    console.log('初始化认证状态...')

    // 检查是否有保存的登录状态
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')

    if (token && userInfo) {
      console.log('发现已保存的登录状态:', userInfo.nickName)

      // 验证token有效性（静默验证，不显示加载提示）
      auth.checkTokenValid()
        .then(validUserInfo => {
          console.log('Token验证成功，用户信息已更新:', validUserInfo.nickName)
        })
        .catch(err => {
          console.log('Token验证失败，已清除登录状态:', err.message)
        })
    } else {
      console.log('未发现登录状态，用户需要重新登录')
    }
  }
})
