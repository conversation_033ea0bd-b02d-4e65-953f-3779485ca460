/**app.wxss**/
/* 全局样式重置 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  box-sizing: border-box;
}

/* 全局盒模型 */
view, text, image, button, input, textarea, scroll-view, swiper, picker {
  box-sizing: border-box;
}

/* 通用容器 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

/* 响应式容器 */
.responsive-container {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 32rpx;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: 44rpx;
}

.safe-area-bottom {
  padding-bottom: 34rpx;
}

/* 通用按钮样式 */
.btn-primary {
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary:active {
  background: #2563eb;
}

.btn-primary.disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:active {
  background: #e5e7eb;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 文本样式 */
.text-primary {
  color: #3b82f6;
}

.text-secondary {
  color: #6b7280;
}

.text-success {
  color: #10b981;
}

.text-warning {
  color: #f59e0b;
}

.text-error {
  color: #ef4444;
}

/* 响应式布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 32rpx;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式间距 */
.mt-16 { margin-top: 16rpx; }
.mt-24 { margin-top: 24rpx; }
.mt-32 { margin-top: 32rpx; }
.mt-48 { margin-top: 48rpx; }
.mb-16 { margin-bottom: 16rpx; }
.mb-24 { margin-bottom: 24rpx; }
.mb-32 { margin-bottom: 32rpx; }
.mb-48 { margin-bottom: 48rpx; }
.p-16 { padding: 16rpx; }
.p-24 { padding: 24rpx; }
.p-32 { padding: 32rpx; }
.p-48 { padding: 48rpx; }

/* 响应式宽度 */
.w-full { width: 100%; }
.w-half { width: 50%; }
.w-third { width: 33.333%; }
.w-quarter { width: 25%; }

/* 响应式高度 */
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 响应式字体大小 */
.text-xs { font-size: 20rpx; }
.text-sm { font-size: 24rpx; }
.text-base { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-2xl { font-size: 40rpx; }

/* 圆角 */
.rounded-sm { border-radius: 8rpx; }
.rounded { border-radius: 12rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-xl { border-radius: 24rpx; }
.rounded-full { border-radius: 50%; }

/* 响应式设计 - 使用rpx单位自动适配不同屏幕 */
