# 识别码列表页面 roomList 错误排查指南

## 错误现象
前端报错：`Uncaught TypeError: value.map is not a function`

## 错误原因分析
这个错误表明在前端列表页面的 formatter 函数中，`roomList` 字段的值不是一个数组，导致无法调用 `map` 方法。

## 可能的原因

### 1. 数据库迁移未完成
如果还没有执行数据库迁移脚本，`category_code_room` 表可能不存在，导致查询失败。

### 2. 现有识别码没有关联数据
如果数据库中有识别码记录，但没有对应的房型关联数据。

### 3. 服务依赖注入问题
`CategoryCodeRoomService` 可能没有正确注入。

## 解决方案

### 步骤1：检查数据库状态
执行以下SQL检查数据库状态：

```sql
-- 检查关联表是否存在
SHOW TABLES LIKE 'category_code_room';

-- 检查现有数据
SELECT * FROM category_code LIMIT 5;
SELECT * FROM category_code_room LIMIT 5;
```

### 步骤2：执行数据库迁移
如果 `category_code_room` 表不存在，执行迁移脚本：

```sql
-- 执行完整的迁移脚本
source sql/category_code_migration.sql;
```

### 步骤3：为现有数据创建默认关联
如果有识别码但没有房型关联，可以创建默认关联：

```sql
-- 为没有关联数据的识别码创建默认关联（如果原来有room_id）
INSERT INTO category_code_room (category_id, conference, room_id, room_count, create_time)
SELECT category_id, conference, room_id, 1, NOW()
FROM category_code 
WHERE room_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM category_code_room ccr 
    WHERE ccr.category_id = category_code.category_id 
    AND ccr.conference = category_code.conference
);
```

### 步骤4：验证修复
1. 重启应用服务器
2. 访问识别码列表页面
3. 检查是否还有错误

## 代码修复说明

### 后端修复
1. **CategoryCode实体类**：添加了构造函数确保 `roomList` 始终被初始化为空数组
2. **CategoryCodeServiceImpl**：添加了异常处理和空值检查
3. **数据查询逻辑**：确保即使查询失败也会设置空的 `roomList`

### 前端修复
1. **类型检查**：使用 `Array.isArray()` 严格检查数据类型
2. **循环替代**：使用传统 for 循环替代 `map` 方法，更安全
3. **异常处理**：添加了错误边界处理

## 临时解决方案

如果问题仍然存在，可以使用以下临时方案：

### 方案1：简化显示
修改前端 formatter 函数：

```javascript
formatter: function(value, row, index) {
    return '房型配置'; // 简单显示，不展示详细信息
}
```

### 方案2：使用其他字段
如果 `roomList` 有问题，可以临时使用 `roomCount` 字段：

```javascript
formatter: function(value, row, index) {
    return '共 ' + (row.roomCount || 0) + ' 间房';
}
```

## 预防措施

1. **数据库迁移检查**：部署前确保数据库迁移完成
2. **数据完整性**：确保所有识别码都有对应的房型关联数据
3. **前端防护**：始终对数组操作进行类型检查
4. **后端保障**：确保API返回的数据结构一致

## 调试工具

### 前端调试
在浏览器控制台执行：
```javascript
// 检查表格数据
console.log($('#bootstrap-table').bootstrapTable('getData'));
```

### 后端调试
在 CategoryCodeServiceImpl 中添加日志：
```java
System.out.println("roomList size: " + (code.getRoomList() != null ? code.getRoomList().size() : "null"));
```

## 联系支持
如果问题仍然存在，请提供：
1. 错误的完整堆栈信息
2. 数据库表结构截图
3. 示例数据记录
